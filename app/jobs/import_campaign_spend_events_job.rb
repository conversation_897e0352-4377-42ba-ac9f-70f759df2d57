class ImportCampaignSpendEventsJob < ApplicationJob
  queue_as :default

  def perform(start_date: 1.month.ago.beginning_of_month.to_date, end_date: Date.yesterday)
    start_date = start_date.to_date
    end_date = end_date.to_date

    campaign_spends = DailyCampaignSpend
      .includes(:click_url)
      .where(spend_date: start_date.to_date..end_date.to_date)
    return if campaign_spends.empty?

    # Preload and update in one step
    update_campaign_spends_with_events(campaign_spends)
  end

  private

  def update_campaign_spends_with_events(campaign_spends)
    click_url_ids = campaign_spends.map { |cs| cs.click_url&.id }.compact.uniq
    dates = campaign_spends.pluck(:spend_date).uniq

    # Use the enhanced preload method for better performance
    event_aggregates_map = preload_event_aggregates(click_url_ids, dates)

    # Group events by campaign spend key for easier lookup
    event_map = {}
    event_aggregates_map.each do |(click_url_id, date, event), data|
      key = [ click_url_id, date ]
      event_map[key] ||= {}
      event_map[key][event] = data
    end

    # Bulk update using upsert
    campaign_spends.find_in_batches(batch_size: 1000) do |batch|
      updates = batch.filter_map do |spend|
        events = event_map[[ spend.click_url&.id, spend.spend_date ]]
        next unless events&.present?

        formatted_events = format_event_data(events)
        next if spend.event_data == formatted_events

        { id: spend.id, event_data: formatted_events }
      end

      bulk_update(updates) if updates.any?
    end
  end

  def preload_event_aggregates(click_url_ids, dates)
    priority_origins = [
      EventDataOrigin::FEEDMOB,
      EventDataOrigin::IMPORTED_ADJUST,
      EventDataOrigin::IMPORTED_APPSFLYER,
      EventDataOrigin::IMPORTED_SINGULAR,
      EventDataOrigin::FEEDMOB_AGENCY,
      EventDataOrigin::FEEDMOB_REDSHIFT,
      EventDataOrigin::FEEDMOB_NET_SPEND,
      EventDataOrigin::MMP_AGGREGATE
    ]

    # Get only paid events to filter event data
    paid_events = PaidEvent.all

    result_map = {}

    # Use raw SQL with a window function to select the highest priority aggregate per group
    # This reduces memory usage by having the database do the prioritization
    aggregates = EventAggregate
      .select("click_url_id, date, event, count, data_origin, ROW_NUMBER() OVER (PARTITION BY click_url_id, date, event ORDER BY position(data_origin::text in '#{priority_origins.join(',')}')) as priority_rank")
      .where(click_url_id: click_url_ids, date: dates, data_origin: priority_origins, event: paid_events)
      .from("(#{EventAggregate.where(click_url_id: click_url_ids, date: dates, data_origin: priority_origins, event: paid_events).to_sql}) as event_aggregates")

    # Use find_each to batch process results, reducing memory usage
    EventAggregate.connection.execute(
      "SELECT click_url_id, date, event, count, data_origin FROM (#{aggregates.to_sql}) ranked WHERE priority_rank = 1"
    ).each do |row|
      click_url_id = row["click_url_id"]
      date = row["date"].to_date
      event = row["event"]
      count = row["count"]
      origin = row["data_origin"]

      result_map[[ click_url_id, date, event ]] = { count: count, origin: origin }
    end

    result_map
  end

  def format_event_data(events)
    events.transform_values { |event_data| { count: event_data[:count], source: event_data[:origin] } }
  end

  def bulk_update(updates)
    DailyCampaignSpend.upsert_all(
      updates,
      update_only: [ :event_data ]
    )
  end
end
