# app/jobs/populate_mobile_app_data_job.rb
class PopulateMobileAppDataJob < ApplicationJob
  queue_as :default

  class SensorTowerAPIError < StandardError; end

  BATCH_SIZE = 50

  def perform(mobile_app_id = nil)
    if mobile_app_id
      mobile_app = MobileApp.find(mobile_app_id)

      return unless mobile_app.bundle_id.present?

      begin
        process_single_app(mobile_app)
      rescue => e
        Rails.logger.error "Failed to process mobile app #{mobile_app_id}: #{e.message}"
        raise
      end
    else
      process_all_apps
    end
  end

  private

  def process_all_apps
    android_apps = MobileApp.where(platform: "android")
    process_apps_in_batches(android_apps, "android")
    ios_apps = MobileApp.where(platform: "ios")
    process_apps_in_batches(ios_apps, "ios")
  end

  def process_apps_in_batches(apps, platform)
    apps.in_batches(of: BATCH_SIZE) do |batch|
      begin
        app_ids = batch.pluck(:bundle_id).compact.reject(&:blank?)
        next if app_ids.empty?

        sensor_tower_data = fetch_sensor_tower_data(platform, app_ids)

        batch.each do |app|
          next unless app.bundle_id.present?

          if sensor_tower_data && sensor_tower_data["apps"]&.any?
            update_app_with_sensor_tower_data(app, sensor_tower_data)
          end
        end
      rescue => e
        Rails.logger.error "Failed to process #{platform} batch: #{e.message}"
      end
    end
  end

  def process_single_app(mobile_app)
    client = SensorTower::Client.new
    platform = mobile_app.platform&.downcase
    api_options = { country: "US" }

    response = case platform
    when "ios"
      client.get_ios_apps([ mobile_app.bundle_id ], api_options)
    when "android"
      client.get_android_apps([ mobile_app.bundle_id ], api_options)
    else
      raise SensorTowerAPIError, "Unsupported platform: #{platform}"
    end

    update_app_with_sensor_tower_data(mobile_app, response)
  rescue SensorTower::APIError => e
    Rails.logger.error "SensorTower API error for #{mobile_app.bundle_id}: #{e.message}"
    raise SensorTowerAPIError, "Failed to fetch data from SensorTower: #{e.message}"
  end

  def fetch_sensor_tower_data(platform, app_ids)
    return {} if app_ids.empty?

    begin
      client = SensorTower::Client.new
      api_options = { country: "US" }

      case platform
      when "ios"
        response = client.get_ios_apps(app_ids, api_options)
      when "android"
        response = client.get_android_apps(app_ids, api_options)
      else
        raise SensorTowerAPIError, "Unsupported platform: #{platform}"
      end

      response
    rescue SensorTower::APIError => e
      Rails.logger.error "SensorTower API error for #{platform} apps: #{e.message}"
      raise SensorTowerAPIError, "Failed to fetch data from SensorTower: #{e.message}"
    end
  end

  def update_app_with_sensor_tower_data(mobile_app, sensor_tower_response)
    begin
      adapter = SensorTower::AppDataAdapter.new(sensor_tower_response, mobile_app.bundle_id, mobile_app.platform)

      ActiveRecord::Base.transaction do
        update_main_category(mobile_app, adapter.primary_genre) if adapter.primary_genre
        update_categorizations(mobile_app, adapter.genre_names) if adapter.genre_names&.any?
        update_logo(mobile_app, adapter.artwork) if adapter.artwork.present?
        update_basic_info_from_adapter(mobile_app, adapter)
      end

      find_android_data_for_ios_app(mobile_app)
    rescue SensorTower::AppDataAdapter::AppNotFoundError => e
      Rails.logger.error "App not found in SensorTower data: #{e.message}"
      nil
    end
  end

  def update_main_category(mobile_app, primary_genre)
    return if primary_genre.blank?

    category = MobileAppCategory.find_or_create_by_name(primary_genre)
    if category && mobile_app.main_category != category
      mobile_app.update!(main_category: category)
    end
  end

  def update_categorizations(mobile_app, genre_names)
    return if genre_names.blank?

    categories = genre_names.filter_map do |genre_name|
      MobileAppCategory.find_or_create_by_name(genre_name)
    end

    mobile_app.mobile_app_categories = categories
  end

  def update_logo(mobile_app, artwork_url)
    return if mobile_app.logo.attached?

    begin
      uri = URI.parse(artwork_url)
      downloaded_file = uri.open

      filename = "#{mobile_app.name.parameterize}_logo#{File.extname(uri.path)}"
      filename = "logo.png" if filename.blank? || filename == "_logo"

      mobile_app.logo.attach(
        io: downloaded_file,
        filename: filename,
        content_type: downloaded_file.content_type
      )
    rescue => e
      Rails.logger.error "Failed to download logo for #{mobile_app.name} (#{mobile_app.bundle_id}): #{e.message}"
    end
  end

  def update_basic_info_from_adapter(mobile_app, adapter)
    updates = {}

    if mobile_app.description.blank? && adapter.description.present?
      updates[:description] = adapter.description
    end

    if mobile_app.store_url.blank? && adapter.url.present?
      updates[:store_url] = adapter.url
    end

    if updates.any?
      mobile_app.update!(updates)
    end
  end

  def find_android_data_for_ios_app(mobile_app)
    return unless ios_app?(mobile_app)

    categories = mobile_app.mobile_app_categories.reload
    should_find_android_data = categories.empty? || categories.all? { |cat| cat.name.match?(/\A\d+\z/) }

    return unless should_find_android_data

    android_app = MobileApp.find_by(name: mobile_app.name, platform: "android")

    if android_app
      if android_app.mobile_app_categories.empty?
        process_android_app(android_app)
      end
      android_app.reload
      if android_app.mobile_app_categories.any?
        update_categorizations(mobile_app, android_app.mobile_app_categories.map(&:name))
      end
    end

    final_categories = mobile_app.mobile_app_categories.reload

    if final_categories.any? && final_categories.all? { |cat| cat.name.match?(/\A\d+\z/) }
      begin
        parser = AppStoreParser.new(mobile_app.bundle_id, "en")
        if parser.genre_names.present?
          update_categorizations(mobile_app, parser.genre_names)
        end
      rescue AppStoreParser::Error => e
        Rails.logger.error "AppStoreParser failed for #{mobile_app.name} (#{mobile_app.bundle_id}): #{e.message}"
      end
    end
  end

  def process_android_app(android_app)
    return unless android_app.bundle_id.present?

    begin
      process_single_app(android_app)
    rescue => e
      Rails.logger.error "Failed to process Android app #{android_app.id} for category association: #{e.message}"
    end
  end

  def ios_app?(mobile_app)
    mobile_app.platform == "ios"
  end

  def android_app?(mobile_app)
    mobile_app.platform == "android"
  end
end
