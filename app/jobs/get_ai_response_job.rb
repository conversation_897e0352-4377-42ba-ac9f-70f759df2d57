class GetAiResponseJob < ApplicationJob
  queue_as :default

  def perform(conversation_id)
    conversation = Conversation.find(conversation_id)
    invoke(conversation)
  end

  def execute_tool_uses(message)
    message.update(status: "tool_executing")

    tool_results = []
    error = nil

    # Get the user from the conversation to set up authentication
    user_id = message.conversation.user_id

    # Create API client with authentication
    with_user_auth(user_id) do |api_client|
      message.tool_uses.each do |tool_use|
        is_ok, result = ToolUseExecutorService.execute(message, tool_use, api_client)

        if result[:error]
          error = result[:error]
          break
        end
        if is_ok
          tool_results << result[:tool_result]
        end
      end
    end

    if error
      message.update(status: "error", error_message: error)
    else
      if tool_results.any?
        message.update(tool_results: tool_results, status: "completed")
        GetAiResponseJob.perform_later(message.conversation_id)
      else
        message.conversation.update(status: "active")
      end
    end
  end

  private

  def non_stream_result(conversation, messages)
    default_model_config = AiConfig.configuration.model_configs[:claude_3_5_haiku]
    model_config = AiConfig.configuration.model_configs.fetch(conversation.model, default_model_config)

    default_inference_profile = model_config[:inference_profiles][:default]
    inference_config = model_config[:inference_profiles].fetch(conversation.profile, default_inference_profile)

    params = build_request_params(conversation, messages, model_config, inference_config)
    message = conversation.messages.create(
      role: "assistant",
      content: "",
      status: "streaming"
    )

    begin
      response = bedrock_client(model_config[:region]).converse(params)

      # Calculate cost upfront if usage information is available
      metadata = {}
      if response.usage
        metadata = {
          cost: (
            response.usage.input_tokens * model_config[:pricing][:input_tokens] +
            response.usage.output_tokens * model_config[:pricing][:output_tokens]
          ).round(4),
          model: model_config[:model_id],
          usage: response.usage.to_h
        }
        TokenUsage.create!(
          model: model_config[:model_id],
          source: "conversation",
          input_tokens: response.usage.input_tokens,
          output_tokens: response.usage.output_tokens
        )
      end

      message.update(
        content: response.output.message.content.first&.text.to_s,
        status: if response.output.message.content.any? { |c| c.tool_use }
                  "tool_calling"
                else
                  "completed"
                end,
        metadata: metadata
      )

      # Handle tool uses if present
      if response.output.message.content.any? { |c| c.tool_use }
        tool_uses = response.output.message.content
          .select { |c| c.tool_use }
          .map { |c| c.tool_use.to_h }

        message.update(tool_uses: tool_uses)
      end

      conversation.update!(status: "active")

      if message && message.status == "completed" && message.role == "assistant" && message.tool_uses.empty?
        process_delegated_response(message)
      end

      [ response.stop_reason, message ]
    end
  end

  def stream_result(conversation, messages)
    default_model_config = AiConfig.configuration.model_configs[:claude_3_5_haiku]
    model_config = AiConfig.configuration.model_configs.fetch(conversation.model, default_model_config)

    default_inference_profile = model_config[:inference_profiles][:default]
    inference_config = model_config[:inference_profiles].fetch(conversation.profile, default_inference_profile)

    stop_reason = ""
    message = nil
    text = ""
    tool_uses = []
    tool_use = {}
    reasoning_content = ""

    handler = Proc.new do |stream|
      stream.on_message_start_event do |event|
        Rails.logger.debug "Message Start Event: #{event}"
        message = conversation.messages.create(role: event.role, content: "", status: "streaming")
      end

      stream.on_content_block_start_event do |event|
        Rails.logger.debug "Content Block Start Event: #{event}"
        if event.dig(:start, :tool_use)
          tool_use[:tool_use_id] = event.dig(:start, :tool_use, :tool_use_id)
          tool_use[:name] = event.dig(:start, :tool_use, :name)
          tool_use[:input] = ""
          message.update(status: "tool_calling")
        end
      end

      stream.on_content_block_delta_event do |event|
        Rails.logger.debug "Content Block Delta Event: #{event}"
        if event.dig(:delta, :tool_use)
          tool_use[:input] += event.dig(:delta, :tool_use, :input)
        elsif event.dig(:delta, :text)
          text += event.dig(:delta, :text)
          message.update(content: text)
        elsif event.dig(:delta, :reasoning_content, :text)
          reasoning_content += event.dig(:delta, :reasoning_content, :text)
          message.update(reasoning_content: reasoning_content)
        elsif event.dig(:delta, :reasoning_content, :signature)
          message.update(reasoning_signature: event.dig(:delta, :reasoning_content, :signature))
        else
          Rails.logger.warn "Unknown delta event: #{event}"
        end
      end

      stream.on_content_block_stop_event do |event|
        Rails.logger.debug "Content Block Stop Event: #{event}"
        if tool_use && tool_use[:input].present?
          tool_use[:input] = JSON.parse(tool_use[:input])
          tool_uses << tool_use

          Rails.logger.debug "Tool Uses: #{tool_uses}"

          message.update(tool_uses: tool_uses)
          tool_use = {}
        end
      end

      stream.on_message_stop_event do |event|
        Rails.logger.debug "Message Stop Event: #{event}"
        stop_reason = event[:stop_reason]
        unless message.status == "tool_calling"
          message.update(status: "completed")
          conversation.update!(status: "active")

          if message.role == "assistant" && message.tool_uses.empty?
            process_delegated_response(message)
          end
        end
      end

      stream.on_metadata_event do |event|
        Rails.logger.debug "Metadata Event: #{event}"
        metadata = message.metadata
        if event[:usage] && event[:usage][:input_tokens] && event[:usage][:output_tokens]
          input_tokens = event[:usage][:input_tokens]
          output_tokens = event[:usage][:output_tokens]
          metadata[:cost] = (
            input_tokens * model_config[:pricing][:input_tokens] + output_tokens * model_config[:pricing][:output_tokens]
          ).round(4)
          metadata[:model] = model_config[:model_id]

          TokenUsage.create!(
            model: model_config[:model_id],
            source: "conversation",
            input_tokens: input_tokens,
            output_tokens: output_tokens
          )
        end
        message.update(metadata: metadata.merge(event.to_h))
      end

      stream.on_internal_server_exception_event do |event|
        conversation.messages.create(role: "assistant", content: "", status: "error", error_message: event.message)
      end

      stream.on_model_stream_error_exception_event do |event|
        conversation.messages.create(role: "assistant", content: "", status: "error", error_message: event.message)
      end

      stream.on_validation_exception_event do |event|
        conversation.messages.create(role: "assistant", content: "", status: "error", error_message: event.message)
      end

      stream.on_throttling_exception_event do |event|
        conversation.messages.create(role: "assistant", content: "", status: "error", error_message: event.message)
      end

      stream.on_service_unavailable_exception_event do |event|
        conversation.messages.create(role: "assistant", content: "", status: "error", error_message: event.message)
      end
    end

    params = build_request_params(conversation, messages, model_config, inference_config, handler: handler)

    Rails.logger.debug "Params: #{params}"

    bedrock_client(model_config[:region]).converse_stream(params)
    [ stop_reason, message ]
  end

  def invoke(conversation)
    messages = build_messages(conversation)

    last_message = messages.last
    if last_message[:role] == "assistant"
      return
    end

    if conversation.streaming
      stop_reason, message = stream_result(conversation, messages)
    else
      stop_reason, message = non_stream_result(conversation, messages)
    end

    if stop_reason == "tool_use" && message.tool_uses.any?
      if requires_confirmation?(message.tool_uses)
        message.update(status: :awaiting_confirmation)
        return
      end

      execute_tool_uses(message)
    end
  rescue Aws::Errors::ServiceError => e
    conversation.messages.create(
      role: "assistant",
      content: "",
      status: "error",
      error_message: e.message
    )
    [ nil, nil ]
  end

  def build_request_params(conversation, messages, model_config, inference_config, handler: nil)
    system_info = "Current Datetime: #{Time.zone.now.strftime('%Y-%m-%d %H:%M:%S')}".strip

    system_messages = [
      {
        text: system_info
      }
    ]

    if conversation.conversation_contexts.any?
      system_messages << { text: "Context:\n#{conversation.formatted_context}" }
    end

    assistant = AiConfig.configuration.assistants.fetch(
      conversation.assistant,
      AiConfig.configuration.assistants[:default]
    )

    if assistant[:system_prompt].present?
      system_messages << { text: assistant[:system_prompt] }
    end

    params = {
      model_id: model_config[:model_id],
      inference_config: inference_config[:config],
      additional_model_request_fields: inference_config[:additional_config] || {},
      messages: messages,
      system: system_messages
    }

    if handler
      params[:event_stream_handler] = handler
    end

    tools = assistant[:tools]

    if tools.any?
      params[:tool_config] = {
        tools: tools.map do | tool_name |
          tool_spec = AssistantTools::Base.find(tool_name)&.tool_spec

          if tool_spec
            { tool_spec: tool_spec }
          else
            Rails.logger.warn "Unknown tool: #{tool_name}"
            nil
          end
        end
      }
    end

    params
  end

  def build_messages(conversation)
    messages = []

    conversation.messages.each do |message|
      if message.role == "user"
        content = []

        if message.content.present?
          content << { text: message.content }
        end

        # Add documents and attachments as before
        if message.documents.any?
          message.documents.each do |document|
            content << {
              document: {
                format: extract_format(document.filename.extension),
                name: Bedrock::Service.sanitize_filename(document.filename.to_s),
                source: {
                  bytes: document.download
                }
              }
            }
          end
        end

        if message.images.any?
          message.images.each do |image|
            if [ "png", "jpeg", "gif", "webp" ].include?(image.filename.extension)
              content << {
                image: {
                  format: image.filename.extension,
                  source: {
                    bytes: image.download
                  }
                }
              }
            end
          end
        end

        if message.metadata["attachments"].present?
          message.metadata["attachments"].each do |attachment|
            if [ "png", "jpeg", "gif", "webp" ].include?(attachment["filetype"])
              content << {
                image: {
                  format: attachment["filetype"],
                  source: {
                    bytes: SlackClient.download_slack_file_bytes(attachment["url_private_download"])
                  }
                }
              }
            end

            if [ "pdf", "csv", "doc", "docx", "xls", "xlsx", "html", "txt", "md" ].include?(attachment["filetype"])
              content << {
                document: {
                  format: attachment["filetype"],
                  name: Bedrock::Service.sanitize_filename(attachment["name"]),
                  source: {
                    bytes: SlackClient.download_slack_file_bytes(attachment["url_private_download"])
                  }
                }
              }
            elsif attachment["mimetype"].start_with?("text")
              content << {
                document: {
                  format: "txt",
                  name: Bedrock::Service.sanitize_filename(attachment["name"]),
                  source: {
                    bytes: SlackClient.download_slack_file_bytes(attachment["url_private_download"])
                  }
                }
              }
            end
          end
        end

        messages << { role: "user", content: content }
      elsif message.role == "assistant"
        content = []
        if message.content.present?
          content << { text: message.content }
        end

        if message.tool_uses.any? && message.tool_results.any?
          message.tool_uses.each do |tool_use|
            content << {
              tool_use: {
                tool_use_id: tool_use["tool_use_id"],
                name: tool_use["name"],
                input: tool_use["input"]
              }
            }
          end
        end

        if message.reasoning_content.present? && message.reasoning_signature.present?
          content << {
            reasoning_content: {
              reasoning_text: {
                text: message.reasoning_content,
                signature: message.reasoning_signature
              }
            }
          }
        end

        messages << { role: "assistant", content: content }

        if message.tool_results.any?
          content = message.tool_results.map do |tool_result|
            {
              tool_result: {
                tool_use_id: tool_result["tool_use_id"],
                content: tool_result["content"],
                status: tool_result["status"] || "success"
              }
            }
          end

          messages << { role: "user", content: content }
        end
      end
    end

    Rails.logger.debug "Messages: #{messages}"

    messages
  end

  def bedrock_client(region)
    Bedrock::Service.new(region: region).client
  end

  def extract_format(extension)
    format_mapping = %w[pdf csv doc docx xls xlsx html txt md]
    format_mapping.include?(extension.downcase) ? extension.downcase : "txt"
  end

  def requires_confirmation?(tool_uses)
    tool_uses.any? do |tool_use|
      AssistantTools::Base.find(tool_use["name"])&.confirmation_required?
    end
  end

  def process_delegated_response(message)
    conversation = message.conversation

    # Check if this is a delegated conversation
    delegate_metadata = conversation.metadata.slice("delegate_parent_message_id", "delegate_parent_tool_use_id")
    return if delegate_metadata.empty?

    parent_message_id = delegate_metadata["delegate_parent_message_id"]
    parent_tool_use_id = delegate_metadata["delegate_parent_tool_use_id"]

    begin
      parent_message = Message.find(parent_message_id)

      # Create tool result for the parent message
      new_tool_result = {
        "tool_use_id" => parent_tool_use_id,
        "content": [ { text: "<MESSAGES_FROM_ASSISTANT>#{message.content}</MESSAGES_FROM_ASSISTANT>\n Now forward this message to the user, be sure to preserved key information" } ]
      }

      # Update tool_results array
      current_results = parent_message.tool_results || []
      parent_message.update(tool_results: current_results + [ new_tool_result ], status: "completed")

      # Optionally mark the parent conversation as active again if needed
      parent_message.conversation.update(status: "active") if parent_message.conversation.status != "active"

      GetAiResponseJob.perform_later(parent_message.conversation_id)
    rescue => e
      Rails.logger.error "Failed to process delegated response: #{e.message}"
    end
  end
end
