module SyncExternal
  class PartnersJob < ApplicationJob
    queue_as :default

    def perform(*args)
      collection.each do |legacy|
        process(legacy)
      end
    end

    def collection
      ExternalDatabase.main[:vendors]
        .where(deleted_at: nil)
        .order(Sequel.asc(:id))
    end

    def process(legacy)
      partner = Partner.find_or_initialize_by(legacy_id: legacy[:id])
      partner.name = legacy[:vendor_name] if partner.name.blank?
      partner.email = legacy[:main_contact] if partner.email.blank?
      partner.status = status_mapping[legacy[:status]] if partner.status.blank?
      partner.created_at = legacy[:created_at] if partner.created_at.blank?
      partner.is_test = legacy[:for_test] if partner.is_test.nil?
      partner.email_suffix = extract_email_suffixes(legacy[:main_contact_info]) if partner.email_suffix.blank?

      # Always override main_category based on legacy vendor_type
      main_category = find_partner_category(legacy[:vendor_type])
      partner.main_category = main_category

      # Ensure main_category is included in partner_categories
      if main_category && !partner.partner_categories.include?(main_category)
        partner.partner_categories << main_category
      end

      partner.save!
    end

    def status_mapping
      {
        1 => "archived",
        2 => "initial_call",
        3 => "onboarding_paperwork",
        4 => "collecting_additional_info",
        5 => "first_client_approval",
        6 => "tracking_setup",
        7 => "first_campaign_launch_and_monitor",
        8 => "running",
        9 => "paused",
        10 => "onboarding"
      }
    end

    def vendor_type_mapping
      {
        1 => "Affiliate Network",
        2 => "Direct",
        3 => "Network",
        4 => "Media Buyer",
        5 => "Ad Network",
        6 => "Demand Side Platform (DSP)",
        7 => "Incent",
        8 => "Native",
        9 => "O&O",
        10 => "Pop Platform",
        11 => "SDK Network",
        12 => "OEM / Preload partner",
        13 => "Incent O&O",
        14 => "Email Marketing",
        15 => "Influencer Marketing Platform",
        16 => "CTV Partner",
        17 => "Browser & Search Partner"
      }
    end

    def find_partner_category(vendor_type_id)
      return nil unless vendor_type_id

      category_name = vendor_type_mapping[vendor_type_id]
      return nil unless category_name

      PartnerCategory.find_by(name: category_name)
    end

    def extract_email_suffixes(contacts_info)
      contacts_info = JSON.parse(contacts_info)
      email_suffixes = []
      if contacts_info.is_a?(Array)
        contacts_info.each do |contact|
          if contact["email"].present?
            domain = contact["email"].split("@").last
            email_suffixes << "@#{domain}" if domain.present?
          end
        end
      end
      email_suffixes.uniq.join(",")
    rescue JSON::ParserError => e
      Rails.logger.error("Error parsing contacts_info: #{e}")
      nil
    end
  end
end
