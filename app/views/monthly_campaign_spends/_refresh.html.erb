<!-- Refresh But<PERSON> with Dropdown -->
<div data-controller="dropdown" class="relative">
  <button type="button"
          data-action="dropdown#toggle" 
          class="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-300 hover:text-gray-100 bg-gray-800 rounded-md hover:bg-gray-700 transition-colors duration-150">
    Refresh
    <%= icon "arrow-path", set: "mini", class: "-mr-1 ml-1.5 h-5 w-5 text-gray-400" %>
  </button>

  <div data-dropdown-target="menu" 
       data-controller="date-range"
       class="hidden absolute right-0 z-20 mt-2 w-64 origin-top-right rounded-md bg-gray-800 p-4 shadow-lg ring-1 ring-gray-700">
    <%= form_with url: refresh_campaign_spends_path, method: :post do |refresh_form| %>
      <div class="space-y-3">
        <h3 class="text-sm font-medium text-gray-200 mb-2">Select Refresh Period</h3>
        
        <div class="space-y-4">
          <div>
            <%= refresh_form.label :start_date, "Start Date", class: "block text-sm font-medium text-gray-200" %>
            <%= refresh_form.date_field :start_date,
                value: 1.months.ago.beginning_of_month.to_date,
                data: { date_range_target: "startDate" },
                class: "mt-1 block w-full rounded-md border-0 bg-gray-700 px-3 py-2 text-gray-100 ring-1 ring-inset ring-gray-600 focus:ring-2 focus:ring-primary-500 text-sm" %>
          </div>

          <div>
            <%= refresh_form.label :end_date, "End Date", class: "block text-sm font-medium text-gray-200" %>
            <%= refresh_form.date_field :end_date,
                value: Date.yesterday - 1.day,
                data: { date_range_target: "endDate" },
                class: "mt-1 block w-full rounded-md border-0 bg-gray-700 px-3 py-2 text-gray-100 ring-1 ring-inset ring-gray-600 focus:ring-2 focus:ring-primary-500 text-sm" %>
          </div>

          <!-- Quick Select Buttons -->
          <div class="grid grid-cols-2 gap-2">
            <button type="button" 
                    data-action="date-range#setCurrentMonth"
                    class="px-3 py-1 text-xs font-medium text-gray-300 bg-gray-700 rounded hover:bg-gray-600">
              Current Month
            </button>
            <button type="button"
                    data-action="date-range#setLastMonth"
                    class="px-3 py-1 text-xs font-medium text-gray-300 bg-gray-700 rounded hover:bg-gray-600">
              Last Month
            </button>
          </div>

          <div class="mt-4">
            <%= refresh_form.submit "Refresh Data",
                class: "w-full px-4 py-2 text-sm font-medium text-white bg-primary-600 rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500" %>
          </div>
        </div>
      </div>
    <% end %>
  </div>
</div>
