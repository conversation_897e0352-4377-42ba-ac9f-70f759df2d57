<!-- CSV Export Button with Dropdown -->
<div data-controller="dropdown" class="relative">
  <button type="button"
          data-action="dropdown#toggle" 
          class="inline-flex items-center px-3 py-2 text-sm font-medium text-emerald-300 hover:text-emerald-100 bg-emerald-800/30 rounded-md hover:bg-emerald-800/50 transition-colors duration-150">
    Export
    <%= icon "arrow-down-tray", set: "mini", class: "-mr-1 ml-1.5 h-5 w-5 text-emerald-500" %>
  </button>

  <div data-dropdown-target="menu" 
      class="hidden absolute right-0 z-20 mt-2 w-72 origin-top-right rounded-md bg-gray-800 p-4 shadow-lg ring-1 ring-gray-700">
    <%= form_with scope: :query, url: export_campaign_spends_path(format: :csv), method: :get do |export_form| %>
      <!-- Hidden fields to maintain current filters -->
      <% @query.attributes.each do |attr, value| %>
        <% if value.present? %>
          <% if attr.end_with?('_in') && value.is_a?(Array) %>
            <% value.each do |v| %>
              <%= hidden_field_tag "query[#{attr}][]", v %>
            <% end %>
          <% elsif value.is_a?(Array) %>
            <% value.each do |v| %>
              <%= export_form.hidden_field "#{attr}[]", value: v %>
            <% end %>
          <% else %>
            <%= export_form.hidden_field attr, value: value %>
          <% end %>
        <% end %>
      <% end %>
      
      <div class="space-y-3">
        <h3 class="text-sm font-medium text-gray-200 mb-2">Select Fields to Export</h3>
        
        <div class="space-y-2 max-h-80 overflow-y-auto">
          <% export_fields = [
            ['date', 'Spend Date', true],
            ['client_name', 'Client Name', true],
            ['client_legacy_id', 'Client ID', false],
            ['vendor_name', 'Vendor Name', true],
            ['vendor_legacy_id', 'Vendor ID', false],
            ['campaign_name', 'Campaign Name', true],
            ['campaign_legacy_id', 'Campaign ID', false],
            ['click_url_legacy_id', 'Click URL ID', true],
            ['gross_spend', 'Gross Spend', true],
            ['gross_spend_source', 'Gross Spend Source', false],
            ['net_spend', 'Net Spend', true],
            ['net_spend_source', 'Net Spend Source', false],
            ['margin', 'Margin', true]
          ] %>
          
          <% export_fields.each do |field, label, default_checked| %>
            <label class="flex items-center space-x-2">
              <%= check_box_tag "query[export_fields][]", 
                  field,
                  default_checked,
                  class: "h-4 w-4 rounded border-gray-600 bg-gray-700 text-primary-400 focus:ring-primary-500" %>
              <span class="text-sm text-gray-200"><%= label %></span>
            </label>
          <% end %>

        </div>

        <div class="mt-4">
          <%= export_form.submit "Download CSV", 
              class: "w-full px-4 py-2 text-sm font-medium text-white bg-primary-600 rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500" %>
        </div>
      </div>
    <% end %>
  </div>
</div>