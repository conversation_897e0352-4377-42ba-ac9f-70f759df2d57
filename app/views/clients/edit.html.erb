<% content_for :title, "Edit Client" %>

<div class="w-full h-screen flex flex-col">
  <% if @client.errors.any? %>
    <%= alerting :error, @client.errors.full_messages.join(", ") %>
  <% end %>

  <header class="border-b dark:border-gray-800 bg-white dark:bg-gray-900 shadow-sm">
    <div class="max-w-7xl px-4 h-16 flex items-center sm:px-6 2xl:px-12 mx-auto">
      <div class="flex flex-col md:flex-row md:items-center md:justify-between w-full">
        <div class="flex items-end space-x-2">
          <h1 class="text-xl font-semibold text-gray-900 dark:text-white">
            <%= @client.persisted? ? "Edit Client" : "New Client" %>
          </h1>
          <% if @client.persisted? %>
            <span class="inline-block border border-primary-700 rounded-md px-1.5 py-0.5 text-xs font-medium text-primary-700 dark:text-primary-300 tracking-wide">
              ID: <%= @client.id %>
            </span>
          <% end %>
        </div>

        <div class="flex items-center space-x-4">
          <% if @client.persisted? %>
            <%= link_to client_path(@client), 
              class: "inline-flex items-center gap-x-2 rounded-full bg-primary-100 dark:bg-primary-900 px-4 py-2 text-sm font-medium text-primary-700 dark:text-primary-300 hover:bg-primary-200 dark:hover:bg-primary-800 transition-all duration-200 ease-in-out shadow-sm hover:shadow-md" do %>
              <%= icon "arrow-left", class: "h-4 w-4" %>
              <span>Back to client</span>
            <% end %>
          <% end %>
        </div>
      </div>
    </div>
  </header>

  <div class="grow overflow-y-scroll bg-gray-50 dark:bg-gray-950">
    <div class="mx-auto max-w-7xl px-4 sm:px-6 2xl:px-12 py-6">
      <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
        <!-- Navigation Sidebar -->
        <div class="lg:col-span-1">
          <div class="bg-white dark:bg-gray-900 rounded-lg shadow-sm border dark:border-gray-800 p-4 sticky top-6">
            <nav class="space-y-2">
              <% @sections.each do |section| %>
                <% is_active = @current_section == section[:key] || (@current_section.present? && !@sections.any? { |s| s[:key] == @current_section } && section[:key] == 'overview') %>
                <%= link_to section[:label], edit_client_path(@client, section: section[:key]), class: "block w-full text-left px-3 py-2 rounded-md text-sm font-medium transition-colors #{is_active ? 'bg-primary-100 dark:bg-primary-800 text-primary-700 dark:text-primary-300' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800'}" %>
              <% end %>
            </nav>
          </div>
        </div>

        <!-- Main Content Area -->
        <div class="lg:col-span-3">
          <%= form_with(model: @client, local: true, html: { multipart: true }) do |form| %>
            <div class="space-y-6">
              <% case @current_section %>
              <% when 'overview' %>
                <%= render "clients/form_sections/basic_information", form: form, client: @client %>
                <%= render "clients/form_sections/client_details", form: form, client: @client %>
                <%= render "clients/form_sections/client_social_media_links", form: form, client: @client %>
              <% when 'events' %>
                <%= render "clients/form_sections/client_events", form: form, client: @client %>
              <% when 'creatives' %>
                <%= render "clients/form_sections/client_creatives", form: form, client: @client %>
              <% when 'documents' %>
                <%= render "clients/form_sections/client_documents", form: form, client: @client %>
              <% when 'integrations' %>
                <%= render "clients/form_sections/client_direct_partners", form: form %>
                <%= render "clients/form_sections/client_track_parties", form: form %>
                <%= render "clients/form_sections/client_fraud_platforms", form: form, client: @client %>
              <% when 'client_reports' %>
                <%= render "clients/form_sections/client_reports", form: form, client: @client %>
              <% else %>
                <%= render "clients/form_sections/basic_information", form: form, client: @client %>
              <% end %>

              <!-- Form Actions - Always visible -->
              <div class="bg-white dark:bg-gray-900 overflow-hidden rounded-xl border border-gray-200 dark:border-gray-800 shadow-sm sticky bottom-0 z-10">
                <div class="px-6 py-5 bg-gradient-to-r from-gray-50 to-white dark:from-gray-800 dark:to-gray-900">
                  <div class="flex items-center justify-between">
                    <!-- Left side - Form status/info -->
                    <div class="flex items-center space-x-4">
                      <% if @client.persisted? %>
                        <div class="flex items-center text-sm text-gray-500 dark:text-gray-400">
                          <%= icon "clock", class: "mr-1.5 h-4 w-4" %>
                          Last updated <%= time_ago_in_words(@client.updated_at) %> ago
                        </div>
                      <% else %>
                        <div class="flex items-center text-sm text-gray-500 dark:text-gray-400">
                          <%= icon "plus-circle", class: "mr-1.5 h-4 w-4" %>
                          Creating new client
                        </div>
                      <% end %>
                    </div>

                    <!-- Right side - Action buttons -->
                    <div class="flex items-center space-x-3">
                      <%= link_to @client.persisted? ? client_path(@client) : clients_path, 
                                  class: "inline-flex items-center px-5 py-2.5 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-lg text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 hover:border-gray-400 dark:hover:border-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-all duration-200 ease-in-out" do %>
                        <%= icon "x-mark", class: "mr-2 h-4 w-4" %>
                        Cancel
                      <% end %>
                      
                      <% if @client.persisted? %>
                        <%= form.submit "Update Client", 
                                        class: "cursor-pointer inline-flex items-center px-6 py-2.5 border border-transparent shadow-sm text-sm font-medium rounded-lg text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-all duration-200 ease-in-out transform hover:scale-[1.02] active:scale-[0.98]",
                                        data: { disable_with: "#{icon('arrow-path', class: 'animate-spin mr-2 h-4 w-4')} Updating...".html_safe } %>
                      <% else %>
                        <%= form.submit "Create Client", 
                                        class: "cursor-pointer inline-flex items-center px-6 py-2.5 border border-transparent shadow-sm text-sm font-medium rounded-lg text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-all duration-200 ease-in-out transform hover:scale-[1.02] active:scale-[0.98]",
                                        data: { disable_with: "#{icon('arrow-path', class: 'animate-spin mr-2 h-4 w-4')} Creating...".html_safe } %>
                      <% end %>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          <% end %>
        </div>
      </div>
    </div>
  </div>
</div>
