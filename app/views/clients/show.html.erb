<% content_for :title, "Client: #{@client.name}" %>

<div class="w-full h-full flex flex-col bg-gray-50 dark:bg-gray-950">
  <% if notice.present? %>
    <%= alerting :info, notice %>
  <% end %>
  <%= render 'clients/show/header' %>
  
  <div class="grow overflow-y-auto">
    <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-6">
      <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
        <!-- Navigation Sidebar -->
        <div class="lg:col-span-1">
          <div class="bg-white dark:bg-gray-900 rounded-lg shadow-sm border dark:border-gray-800 p-4 sticky top-6">
            <nav class="space-y-2">
              <% @sections.each do |section| %>
                <%= link_to section[:label], client_path(@client, section: section[:key]), class: "block w-full text-left px-3 py-2 rounded-md text-sm font-medium transition-colors #{@current_section == section[:key] ? 'bg-primary-100 dark:bg-primary-800 text-primary-700 dark:text-primary-300' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800'}" %>
              <% end %>
            </nav>
          </div>
        </div>

        <!-- Main Content Area -->
        <div class="lg:col-span-3">
          <div class="space-y-6">
            <% case @current_section %>
            <% when 'overview' %>
              <%= render 'clients/show/basic_info' %>
              <%= render 'clients/show/client_details' %>
            <% when 'events' %>
              <%= render 'clients/show/events' %>
            <% when 'creatives' %>
              <%= render 'clients/show/creatives' %>
            <% when 'budgets' %>
              <%= render 'clients/show/budgets' %>
            <% when 'documents' %>
              <%= render 'clients/show/documents' %>
            <% when 'integrations' %>
              <%= render 'clients/show/direct_partners' %>
              <%= render 'clients/show/track_parties' %>
              <%= render 'clients/show/fraud_platforms' %>
            <% when 'client_reports' %>
              <%= render "clients/show/client_reports" %>
            <% when 'mobile_apps' %>
              <%= render 'clients/show/mobile_apps' %>
            <% when 'communication' %>
              <%= render 'clients/show/slack_messages' %>
            <% else %>
              <%= render 'clients/show/basic_info' %>
              <%= render 'clients/show/client_details' %>
            <% end %>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
