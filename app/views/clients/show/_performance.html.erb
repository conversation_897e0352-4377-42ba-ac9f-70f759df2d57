<!-- Budget Usage Chart -->
<div class="bg-white dark:bg-gray-900 overflow-hidden rounded-xl border border-gray-200 dark:border-gray-800 shadow-sm mb-6">
  <div class="px-4 py-3 border-b border-gray-200 dark:border-gray-800">
    <div class="flex items-center justify-between">
      <h3 class="text-base font-medium text-gray-900 dark:text-white flex items-center">
        <%= icon "chart-bar", class: "mr-2 h-4 w-4" %>
        Monthly Budget vs Spend
      </h3>
      <% if @monthly_budget_breakdown.present? %>
        <span class="inline-flex items-center rounded-full bg-blue-100 dark:bg-blue-900/50 px-2 py-0.5 text-xs font-medium text-blue-700 dark:text-blue-300">
          <%= @monthly_budget_breakdown.count %> months
        </span>
      <% end %>
    </div>
  </div>

  <div class="px-4 py-5">
    <% if @monthly_budget_breakdown.present? %>
      <% # 优化：将颜色数组移到循环外，避免重复创建
         colors = ["#3B82F6", "#10B981", "#F59E0B", "#EF4444", "#8B5CF6", "#06B6D4", "#84CC16", "#F97316"] %>

      <!-- Prediction Info -->
      <% if @monthly_budget_chart_data[:prediction_data] && @monthly_budget_chart_data[:prediction_data][:predicted_total].present? %>
        <div class="mb-4 p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg border border-yellow-200 dark:border-yellow-800 text-sm">
          <div class="flex items-center">
            <%= icon "light-bulb", class: "mr-2 h-4 w-4 text-yellow-500" %>
            <span class="font-medium">Prediction for <%= Date.current.strftime("%B %Y") %>:</span>
            <span class="ml-2">$<%= number_with_delimiter(@monthly_budget_chart_data[:prediction_data][:predicted_total].round(2)) %></span>
            <span class="ml-3 px-2 py-0.5 rounded-full text-xs <%= @monthly_budget_chart_data[:prediction_data][:confidence] == "High" ? "bg-green-100 text-green-800 dark:bg-green-900/50 dark:text-green-300" : @monthly_budget_chart_data[:prediction_data][:confidence] == "Medium" ? "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/50 dark:text-yellow-300" : "bg-red-100 text-red-800 dark:bg-red-900/50 dark:text-red-300" %>">
              <%= @monthly_budget_chart_data[:prediction_data][:confidence] %> Confidence
            </span>
          </div>
        </div>
      <% end %>

      <!-- Chart -->
      <%= column_chart @monthly_budget_chart_data[:data],
                       stacked: false,
                       prefix: "$",
                       thousands: ",",
                       height: "350px",
                       library: {
                         plugins: {
                           legend: {
                             display: true,
                             position: "bottom",
                           },
                           datalabels: {
                             display: "function(context) { return context.dataset.name === 'Unused Budget' || context.dataset.name === 'Total Budget' || context.dataset.name === 'Total Spent' || context.dataset.name === 'Predicted Spend'; }",
                             anchor: "end",
                             align: "top",
                             color: "#374151",
                             font: {
                               size: 10,
                               weight: "bold",
                             },
                             formatter: "function(value, context) { return '$' + value.toLocaleString(); }",
                           },
                         },
                         scales: {
                           x: {
                             grid: {
                               display: false,
                             },
                           },
                           y: {
                             beginAtZero: true,
                             display: false,
                             grid: {
                               display: false,
                             },
                           },
                         },
                         datasets: {
                           bar: {
                             categoryPercentage: 0.95,
                             barPercentage: 1.0,
                           },
                         },
                       } %>
    <% else %>
      <div class="text-center py-8">
        <p class="text-sm text-gray-500 dark:text-gray-400">No budget data available for the selected period</p>
      </div>
    <% end %>
  </div>
</div>

<!-- Partner Breakdown -->
<div class="bg-white dark:bg-gray-900 overflow-hidden rounded-xl border border-gray-200 dark:border-gray-800 shadow-sm mb-6">
  <div class="px-4 py-3 border-b border-gray-200 dark:border-gray-800">
    <div class="flex items-center justify-between">
      <h3 class="text-base font-medium text-gray-900 dark:text-white flex items-center">
        <%= icon "users", class: "mr-2 h-4 w-4" %>
        Partner Spend Breakdown
      </h3>
      <% if @partner_breakdown.present? %>
        <span class="inline-flex items-center rounded-full bg-green-100 dark:bg-green-900/50 px-2 py-0.5 text-xs font-medium text-green-700 dark:text-green-300">
          <%= @partner_breakdown.count %> partners
        </span>
      <% end %>
    </div>
  </div>

  <div class="px-4 py-5">
    <% if @partner_breakdown.present? %>
      <div class="space-y-3">
        <% @partner_breakdown.each_with_index do |partner, index| %>
          <% color = colors[index % colors.length] %>
          <div class="flex items-center justify-between py-3 px-4 rounded-lg bg-gray-50 dark:bg-gray-800/50 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors duration-200">
            <div class="flex-1 min-w-0">
              <div class="flex items-center space-x-3">
                <div class="flex-shrink-0">
                  <div class="h-4 w-4 rounded-full shadow-sm" style="background-color: <%= color %>"></div>
                </div>
                <div class="flex-1 min-w-0">
                  <p class="text-sm font-medium text-gray-900 dark:text-white truncate">
                    <%= partner[:partner_name] %>
                  </p>
                  <div class="flex items-center space-x-2 text-xs text-gray-500 dark:text-gray-400">
                    <span><%= pluralize(partner[:campaigns_count], "campaign") %></span>
                    <span>•</span>
                    <%= link_to "View details", campaign_spends_path(partner_name: partner[:partner_name], client_id: @client.legacy_id),
                                class: "text-blue-500 dark:text-blue-400 hover:underline" %>
                  </div>
                </div>
              </div>
            </div>
            <div class="flex-shrink-0 text-right">
              <div class="text-sm font-semibold text-gray-900 dark:text-white">
                $<%= number_with_delimiter(partner[:spend_amount].round(2)) %>
              </div>
              <div class="text-xs text-gray-500 dark:text-gray-400">
                <%= number_to_percentage(partner[:percentage], precision: 1) %>
              </div>
            </div>
          </div>
        <% end %>
      </div>
    <% else %>
      <div class="text-center py-8">
        <p class="text-sm text-gray-500 dark:text-gray-400">No partner data available</p>
      </div>
    <% end %>
  </div>
</div>

<!-- Country Breakdown -->
<div class="bg-white dark:bg-gray-900 overflow-hidden rounded-xl border border-gray-200 dark:border-gray-800 shadow-sm">
  <div class="px-4 py-3 border-b border-gray-200 dark:border-gray-800">
    <div class="flex items-center justify-between">
      <h3 class="text-base font-medium text-gray-900 dark:text-white flex items-center">
        <%= icon "globe-alt", class: "mr-2 h-4 w-4" %>
        Country Spend Breakdown
      </h3>
      <% if @country_breakdown.present? %>
        <span class="inline-flex items-center rounded-full bg-purple-100 dark:bg-purple-900/50 px-2 py-0.5 text-xs font-medium text-purple-700 dark:text-purple-300">
          <%= @country_breakdown.count %> countries
        </span>
      <% end %>
    </div>
  </div>

  <div class="px-4 py-5">
    <% if @country_breakdown.present? %>
      <div class="space-y-3">
        <% @country_breakdown.each_with_index do |country, index| %>
          <% color = colors[index % colors.length] %>
          <div class="flex items-center justify-between py-3 px-4 rounded-lg bg-gray-50 dark:bg-gray-800/50 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors duration-200">
            <div class="flex-1 min-w-0">
              <div class="flex items-center space-x-3">
                <div class="flex-shrink-0">
                  <div class="h-4 w-4 rounded-full shadow-sm" style="background-color: <%= color %>"></div>
                </div>
                <div class="flex-1 min-w-0">
                  <p class="text-sm font-medium text-gray-900 dark:text-white truncate">
                    <%= country[:country_code] %>
                  </p>
                  <div class="flex items-center space-x-2 text-xs text-gray-500 dark:text-gray-400">
                    <span><%= pluralize(country[:campaigns_count], "campaign") %></span>
                    <span>•</span>
                    <%= link_to "View details", campaign_spends_path(country_code: country[:country_code], client_id: @client.legacy_id),
                                class: "text-blue-500 dark:text-blue-400 hover:underline" %>
                  </div>
                </div>
              </div>
            </div>
            <div class="flex-shrink-0 text-right">
              <div class="text-sm font-semibold text-gray-900 dark:text-white">
                $<%= number_with_delimiter(country[:spend_amount].round(2)) %>
              </div>
              <div class="text-xs text-gray-500 dark:text-gray-400">
                <%= number_to_percentage(country[:percentage], precision: 1) %>
              </div>
            </div>
          </div>
        <% end %>
      </div>
    <% else %>
      <div class="text-center py-8">
        <p class="text-sm text-gray-500 dark:text-gray-400">No country data available</p>
      </div>
    <% end %>
  </div>
</div>
