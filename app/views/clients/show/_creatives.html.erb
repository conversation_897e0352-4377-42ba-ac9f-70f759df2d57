<% if @client.client_creatives.any? %>
  <div class="bg-white dark:bg-gray-900 overflow-hidden rounded-xl border border-gray-200 dark:border-gray-800 shadow-sm">
    <div class="px-4 py-3 border-b border-gray-200 dark:border-gray-800">
      <div class="flex items-center justify-between">
        <h3 class="text-base font-medium text-gray-900 dark:text-white flex items-center">
          <%= icon "photo", class: "mr-2 h-4 w-4" %>
          Creative Assets
        </h3>
        <span class="inline-flex items-center rounded-full bg-indigo-100 dark:bg-indigo-900/50 px-2 py-0.5 text-xs font-medium text-indigo-700 dark:text-indigo-300">
          <%= @client.client_creatives.count %> assets
        </span>
      </div>
    </div>
    <div class="px-6 py-5">
      <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
        <% @client.client_creatives.each do |creative| %>
          <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
            <div class="flex items-start justify-between mb-3">
              <h4 class="text-sm font-medium text-gray-900 dark:text-white truncate">
                <%= creative.name %>
              </h4>
              <div class="flex items-center space-x-2 flex-shrink-0 ml-2">
                <% if creative.file.attached? %>
                  <%= link_to rails_blob_path(creative.file, disposition: "attachment"), 
                             target: "_blank", 
                             class: "text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300" do %>
                    <%= icon "document-arrow-down", class: "h-4 w-4" %>
                  <% end %>
                <% end %>
                <% if creative.url.present? %>
                  <a href="<%= creative.url %>" target="_blank" class="text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300">
                    <%= icon "arrow-top-right-on-square", class: "h-4 w-4" %>
                  </a>
                <% end %>
              </div>
            </div>
            
            <% if creative.file.attached? %>
              <% content_type = creative.file.content_type %>
              <% if content_type&.start_with?('image/') %>
                <div class="mb-3 rounded-lg overflow-hidden bg-gray-100 dark:bg-gray-700">
                  <%= image_tag creative.file, 
                              class: "w-full h-32 object-contain",
                              alt: creative.name %>
                </div>
              <% elsif content_type&.start_with?('video/') %>
                <div class="mb-3 rounded-lg overflow-hidden bg-gray-100 dark:bg-gray-700">
                  <video class="w-full h-32 object-cover" controls preload="metadata">
                    <source src="<%= rails_blob_path(creative.file) %>" type="<%= content_type %>">
                    Your browser does not support the video tag.
                  </video>
                </div>
              <% end %>
              
              <div class="text-xs text-gray-500 dark:text-gray-400 mb-2 break-words">
                <span class="font-medium">File:</span> 
                <span class="break-all" title="<%= creative.file.filename %>">
                  <%= truncate(creative.file.filename.to_s, length: 30) %>
                </span>
                <span class="text-gray-400">(<%= number_to_human_size(creative.file.byte_size) %>)</span>
              </div>
            <% end %>
            
            <% if creative.url.present? %>
              <div class="text-xs text-gray-500 dark:text-gray-400 font-mono break-all mb-2">
                <span class="font-medium">URL:</span> <%= truncate(creative.url, length: 40) %>
              </div>
            <% end %>
            
            <div class="text-xs text-gray-500 dark:text-gray-400">
              Added <%= time_ago_in_words(creative.created_at) %> ago
            </div>
          </div>
        <% end %>
      </div>
    </div>
  </div>
<% else %>
  <div class="bg-white dark:bg-gray-900 overflow-hidden rounded-xl border border-gray-200 dark:border-gray-800 shadow-sm">
    <div class="px-4 py-3 border-b border-gray-200 dark:border-gray-800">
      <h3 class="text-base font-medium text-gray-900 dark:text-white flex items-center">
        <%= icon "photo", class: "mr-2 h-4 w-4" %>
        Creative Assets
      </h3>
    </div>
    <div class="text-center py-6">
      <p class="text-xs text-gray-500 dark:text-gray-400">This client has no creative assets</p>
    </div>
  </div>
<% end %>
