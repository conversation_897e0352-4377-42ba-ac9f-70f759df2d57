<% # Budget Usage Summary Component %>
<% if @budget_usage %>
  <div class="bg-white dark:bg-gray-900 overflow-hidden rounded-xl border border-gray-200 dark:border-gray-800 shadow-sm">
    <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-800">
      <div class="flex items-center justify-between">
        <h3 class="text-lg font-medium text-gray-900 dark:text-white flex items-center">
          <%= icon "chart-pie", class: "mr-2 h-5 w-5 text-primary-400" %>
          Budget Usage Summary
          <span class="ml-2 text-sm font-normal text-gray-500 dark:text-gray-400">
            (<%= @budget_usage[:period_start].strftime("%b %d, %Y") %> - <%= @budget_usage[:period_end].strftime("%b %d, %Y") %>)
          </span>
        </h3>

        <!-- Date Range Selector -->
        <div class="flex items-center space-x-2">
          <%= form_with url: budget_data_client_path(@client), method: :get, local: false, data: { turbo_frame: "budget_data_frame" }, class: "flex items-center space-x-2" do |form| %>
            <% # Calculate default date range (2 months ago to yesterday)
               default_end_date = Date.current - 1.day # Yesterday
               default_start_date = (Date.current - 2.months).beginning_of_month # First day of 2 months ago %>
            <% # For show action (no params), use default dates
               # For budget_data action (with params), use selected dates
               start_value = action_name == "show" ? default_start_date.strftime("%Y-%m-%d") : (params[:start_date] || default_start_date.strftime("%Y-%m-%d"))
               end_value = action_name == "show" ? default_end_date.strftime("%Y-%m-%d") : (params[:end_date] || default_end_date.strftime("%Y-%m-%d")) %>
            <%= form.date_field :start_date,
                                value: start_value,
                                autocomplete: "off",
                                class: "text-xs border border-gray-300 dark:border-gray-600 rounded-md px-2 py-1 bg-white dark:bg-gray-800 text-gray-900 dark:text-white",
                                onchange: "this.form.requestSubmit()" %>
            <span class="text-xs text-gray-500 dark:text-gray-400">to</span>
            <%= form.date_field :end_date,
                                value: end_value,
                                autocomplete: "off",
                                class: "text-xs border border-gray-300 dark:border-gray-600 rounded-md px-2 py-1 bg-white dark:bg-gray-800 text-gray-900 dark:text-white",
                                onchange: "this.form.requestSubmit()" %>
          <% end %>
        </div>
      </div>
    </div>

    <div class="px-6 py-5">
      <% if @budget_usage[:total_budget] && @budget_usage[:total_budget] > 0 %>
        <!-- Budget Overview Stats -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          <div class="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-4">
            <div class="text-sm font-medium text-gray-500 dark:text-gray-400">Total Budget</div>
            <div class="text-2xl font-bold text-gray-900 dark:text-white">
              $<%= number_with_delimiter(@budget_usage[:total_budget].round(2)) %>
            </div>
          </div>

          <div class="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-4">
            <div class="text-sm font-medium text-gray-500 dark:text-gray-400">Total Spent</div>
            <div class="text-2xl font-bold text-gray-900 dark:text-white">
              $<%= number_with_delimiter(@budget_usage[:total_spent].round(2)) %>
            </div>
          </div>

          <div class="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-4">
            <div class="text-sm font-medium text-gray-500 dark:text-gray-400">Usage Rate</div>
            <div class="text-2xl font-bold <%= @budget_usage[:usage_percentage] > 90 ? "text-red-600 dark:text-red-400" : @budget_usage[:usage_percentage] > 75 ? "text-yellow-600 dark:text-yellow-400" : "text-green-600 dark:text-green-400" %>">
              <%= @budget_usage[:usage_percentage] %>%
            </div>
          </div>
        </div>
      <% else %>
        <!-- No Budget Data Message -->
        <div class="text-center py-8">
          <div class="text-gray-400">
            <%= icon "chart-pie", class: "h-12 w-12 mx-auto mb-4 opacity-60" %>
            <h4 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No Budget Data</h4>
            <p class="text-sm text-gray-500 dark:text-gray-400">No budget or spend data available for the selected period.</p>
          </div>
        </div>
      <% end %>

      <% if @budget_usage[:total_budget] && @budget_usage[:total_budget] > 0 %>
        <!-- Progress Bar -->
        <div class="mb-6">
          <div class="flex justify-between text-sm text-gray-600 dark:text-gray-400 mb-2">
            <span>Budget Utilization</span>
            <span><%= @budget_usage[:usage_percentage] %>% used</span>
          </div>
          <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3">
            <div class="<%= @budget_usage[:usage_percentage] > 90 ? "bg-red-500" : @budget_usage[:usage_percentage] > 75 ? "bg-yellow-500" : "bg-green-500" %> h-3 rounded-full transition-all duration-300"
                 style="width: <%= [@budget_usage[:usage_percentage], 100].min %>%"></div>
          </div>
          <div class="flex justify-between text-xs text-gray-500 dark:text-gray-400 mt-1">
            <span>$0</span>
            <span>$<%= number_with_delimiter(@budget_usage[:total_budget].round(2)) %></span>
          </div>
        </div>
      <% end %>

      <!-- Monthly Budget vs Spend Chart -->
      <% if @monthly_budget_breakdown.present? %>
        <div class="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-4">
          <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-3">Monthly Budget vs Spend</h4>

          <%= column_chart @monthly_budget_chart_data[:data],
                           stacked: true,
                           prefix: "$",
                           thousands: ",",
                           height: "300px",
                           library: {
                             plugins: {
                               legend: {
                                 display: true,
                                 position: "bottom",
                               },
 
                               datalabels: {
                                 display: "function(context) { return (context.dataset.stack === 'partner_breakdown' && context.dataset.name === 'Unused Budget') || (context.dataset.stack === 'budget_comparison' && context.dataset.name === 'Remaining Budget'); }",
                                 anchor: "end",
                                 align: "top",
                                 color: "#374151",
                                 font: {
                                   size: 11,
                                   weight: "bold",
                                 },
                                 formatter: "function(value, context) { var dataIndex = context.dataIndex; var total = 0; context.chart.data.datasets.forEach(function(dataset) { if (dataset.stack === context.dataset.stack && dataset.data[dataIndex]) { total += dataset.data[dataIndex][1]; } }); return '$' + total.toLocaleString(); }",
                               },
                             },
                             scales: {
                               x: {
                                 grid: {
                                   display: false,
                                 },
                               },
                               y: {
                                 beginAtZero: true,
                                 display: false,
                                 grid: {
                                   display: false,
                                 },
                               },
                             },
                             datasets: {
                               bar: {
                                 categoryPercentage: 0.8,
                                 barPercentage: 0.9,
                               },
                             },
                           } %>








        </div>
      <% end %>

      <!-- Additional Info -->
      <div class="mt-4 text-xs text-gray-500 dark:text-gray-400">
        <div class="flex justify-between">
          <span>Period: <%= @budget_usage[:period_start].strftime("%b %d, %Y") %> - <%= @budget_usage[:period_end].strftime("%b %d, %Y") %></span>
          <span>Active Budgets: <%= @budget_usage[:budgets_count] %></span>
        </div>
      </div>
    </div>
  </div>
<% else %>
  <!-- No Budget Data Available -->
  <div class="bg-white dark:bg-gray-900 overflow-hidden rounded-xl border-2 border-dashed border-gray-200 dark:border-gray-700">
    <div class="px-6 py-12 text-center">
      <div class="text-gray-400">
        <%= icon "chart-pie", class: "h-12 w-12 mx-auto mb-4 opacity-60" %>
        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No Budget Usage Data</h3>
        <p class="text-sm mb-6">No budget or spend data available for the last 2 months.</p>
      </div>
    </div>
  </div>
<% end %>
