<header class="border-b dark:border-gray-800 bg-white dark:bg-gray-900 shadow-sm sticky top-0 z-10">
  <div class="max-w-7xl px-4 h-16 flex items-center sm:px-6 2xl:px-12 mx-auto">
    <div class="flex items-center justify-between w-full">
      <div class="flex items-center space-x-3">
        <%= image_tag "feedmob-logo-full-white-1000px.png", class: "mx-auto h-7 w-auto hidden dark:block", alt: "feedmob logo" %>
        <div class="h-6 border-r border-gray-300 dark:border-gray-700"></div>
        <div class="flex items-center">
          <span class="text-base font-medium text-gray-900 dark:text-white"><%= @client.name %></span>
          <span class="ml-2.5 inline-block border border-primary-700 rounded-md px-2 py-0.5 text-xs font-medium text-primary-700 dark:text-primary-300 tracking-wide">
            client details
          </span>
        </div>
      </div>

      <div class="flex items-center space-x-3">
        <%= link_to clients_path, 
          class: "inline-flex items-center gap-x-2 rounded-full bg-gray-100 dark:bg-gray-800 px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700 transition-all duration-200 ease-in-out shadow-sm hover:shadow-md" do %>
          <%= icon "arrow-left", class: "h-4 w-4" %>
          <span>Back to Clients</span>
        <% end %>

        <% current_section_data = @sections.find { |s| s[:key] == @current_section } %>
        <% if current_section_data&.fetch(:editable, true) %>
          <%= link_to edit_client_path(@client, section: @current_section), 
            class: "inline-flex items-center gap-x-2 rounded-full bg-primary-100 dark:bg-primary-800 px-4 py-2 text-sm font-medium text-primary-700 dark:text-primary-300 hover:bg-primary-200 dark:hover:bg-primary-700 transition-all duration-200 ease-in-out shadow-sm hover:shadow-md" do %>
            <%= icon "pencil", class: "h-4 w-4" %>
            <span>Edit</span>
          <% end %>
        <% end %>

        <%= link_to audit_logs_path(model: "Client", model_id: @client.id), 
          class: "p-2 text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:hover:text-gray-300 transition-colors duration-200",
          title: "View audit logs" do %>
          <%= icon "document-text", class: "h-4 w-4" %>
        <% end %>
      </div>
    </div>
  </div>
</header>
