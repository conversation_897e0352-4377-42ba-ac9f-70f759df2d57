<%# filepath: /Users/<USER>/Projects/assistant/app/views/clients/show/_client_reports.html.erb %>
<div class="bg-white dark:bg-gray-900 overflow-hidden rounded-xl border border-gray-200 dark:border-gray-800 shadow-sm">
  <div class="px-4 py-3 border-b border-gray-200 dark:border-gray-800">
    <div class="flex items-center justify-between">
      <h3 class="text-base font-medium text-gray-900 dark:text-white flex items-center">
        <%= icon "document-text", class: "mr-2 h-4 w-4" %>
        Client Reports
      </h3>
      <% if @client.client_reports.active.any? %>
        <span class="inline-flex items-center rounded-full bg-cyan-100 dark:bg-cyan-900/50 px-2 py-0.5 text-xs font-medium text-cyan-700 dark:text-cyan-300">
          <%= @client.client_reports.active.count %>
        </span>
      <% end %>
    </div>
  </div>
  <div class="px-2">
    <% if @client.client_reports.active.any? %>
      <ul role="list" class="divide-y divide-gray-200 dark:divide-gray-800">
        <% @client.client_reports.active.each do |report| %>
          <li class="py-3 px-2">
            <%= link_to client_report_path(report), class: "flex items-center hover:bg-gray-50 dark:hover:bg-gray-800 rounded-lg p-2 -m-2 transition-colors duration-150" do %>
              <div class="flex-shrink-0 mr-3">
                <div class="h-8 w-8 flex items-center justify-center rounded bg-cyan-100 dark:bg-cyan-900 text-cyan-700 dark:text-cyan-300 text-xs font-medium">
                  <%= icon "document-text", class: "h-4 w-4" %>
                </div>
              </div>
              <div class="min-w-0 flex-1">
                <div class="text-sm font-medium text-gray-900 dark:text-white truncate">
                  <%= report.report_name %>
                </div>
                <div class="text-xs text-gray-500 dark:text-gray-400">
                  <%= report.report_type&.upcase %>
                </div>
              </div>
              <div class="flex-shrink-0 ml-3 flex items-center space-x-2">
                <% if report.client_report_files.ready.any? %>
                  <span class="inline-flex items-center rounded-full bg-green-100 dark:bg-green-900/50 px-2 py-0.5 text-xs font-medium text-green-700 dark:text-green-300">
                    Ready
                  </span>
                <% else %>
                  <span class="inline-flex items-center rounded-full bg-gray-100 dark:bg-gray-800 px-2 py-0.5 text-xs font-medium text-gray-700 dark:text-gray-400">
                    Pending
                  </span>
                <% end %>
                <%= icon "chevron-right", class: "h-4 w-4 text-gray-400 dark:text-gray-500" %>
              </div>
            <% end %>
          </li>
        <% end %>
      </ul>
    <% else %>
      <div class="text-center py-6">
        <p class="text-xs text-gray-500 dark:text-gray-400">This client has no reports available</p>
      </div>
    <% end %>
  </div>
</div>