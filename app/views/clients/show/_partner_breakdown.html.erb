<%# Client Partner Breakdown Component %>
<% if @partner_breakdown.present? && @partner_breakdown.any? %>
  <div class="bg-white dark:bg-gray-900 overflow-hidden rounded-xl border border-gray-200 dark:border-gray-800 shadow-sm">
    <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-800">
      <h3 class="text-lg font-medium text-gray-900 dark:text-white flex items-center">
        <%= icon "building-office", class: "mr-2 h-5 w-5 text-primary-400" %>
        Partner Spend Analysis
        <span class="ml-2 text-sm font-normal text-gray-500 dark:text-gray-400">
          (Last 6 months)
        </span>
      </h3>
    </div>
    
    <div class="px-6 py-5">
      <!-- Pie Chart for Partner Distribution -->
      <div class="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-4 mb-6">
        <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-3">Spend Distribution by Partner</h4>
        <% 
          pie_data = @partner_breakdown.map { |item| [item[:partner_name], item[:spend_amount]] }
        %>
        <%= pie_chart pie_data,
            prefix: "$",
            thousands: ",",
            height: "300px",
            colors: [
              "#6366F1", "#EC4899", "#10B981", "#F59E0B", "#EF4444",
              "#8B5CF6", "#14B8A6", "#F43F5E", "#3B82F6", "#84CC16"
            ],
            library: {
              plugins: {
                legend: {
                  position: 'bottom',
                  labels: {
                    usePointStyle: true,
                    padding: 15,
                    boxWidth: 12
                  }
                }
              }
            } %>
      </div>

      <!-- Top Partners List -->
      <div class="space-y-3">
        <h4 class="text-sm font-medium text-gray-900 dark:text-white">Top Partners</h4>
        <% @partner_breakdown.first(5).each_with_index do |partner, index| %>
          <div class="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
            <div class="flex items-center justify-between mb-2">
              <div class="flex items-center">
                <div class="w-3 h-3 rounded-full mr-3" 
                     style="background-color: <%= ['#6366F1', '#EC4899', '#10B981', '#F59E0B', '#EF4444'][index % 5] %>"></div>
                <h5 class="text-sm font-medium text-gray-900 dark:text-white truncate">
                  <%= partner[:partner_name] %>
                </h5>
              </div>
              <div class="text-right">
                <div class="text-sm font-semibold text-gray-900 dark:text-white">
                  $<%= number_with_delimiter(partner[:spend_amount].to_i) %>
                </div>
                <div class="text-xs text-gray-500 dark:text-gray-400">
                  <%= partner[:percentage] %>% of total
                </div>
              </div>
            </div>
            
            <!-- Progress bar for this partner -->
            <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mb-2">
              <div class="h-2 rounded-full transition-all duration-300" 
                   style="width: <%= partner[:percentage] %>%; background-color: <%= ['#6366F1', '#EC4899', '#10B981', '#F59E0B', '#EF4444'][index % 5] %>"></div>
            </div>
            
            <!-- Additional partner info -->
            <div class="flex justify-between text-xs text-gray-500 dark:text-gray-400">
              <span>Campaigns: <%= partner[:campaigns_count] %></span>
              <span>Partner ID: <%= partner[:partner_id] %></span>
            </div>
          </div>
        <% end %>
        
        <% if @partner_breakdown.length > 5 %>
          <div class="text-center pt-2">
            <span class="text-sm text-gray-500 dark:text-gray-400">
              ... and <%= @partner_breakdown.length - 5 %> more partners
            </span>
          </div>
        <% end %>
      </div>

      <!-- Summary Stats -->
      <div class="mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
        <div class="grid grid-cols-3 gap-4 text-sm">
          <div>
            <span class="text-gray-500 dark:text-gray-400">Total Partners:</span>
            <span class="font-medium text-gray-900 dark:text-white ml-2"><%= @partner_breakdown.length %></span>
          </div>
          <div>
            <span class="text-gray-500 dark:text-gray-400">Total Spend:</span>
            <span class="font-medium text-gray-900 dark:text-white ml-2">
              $<%= number_with_delimiter(@partner_breakdown.sum { |p| p[:spend_amount] }.to_i) %>
            </span>
          </div>
          <div>
            <span class="text-gray-500 dark:text-gray-400">Top Partner Share:</span>
            <span class="font-medium text-gray-900 dark:text-white ml-2">
              <%= @partner_breakdown.first[:percentage] %>%
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
<% else %>
  <!-- No Partner Data Available -->
  <div class="bg-white dark:bg-gray-900 overflow-hidden rounded-xl border-2 border-dashed border-gray-200 dark:border-gray-700">
    <div class="px-6 py-12 text-center">
      <div class="text-gray-400">
        <%= icon "building-office", class: "h-12 w-12 mx-auto mb-4 opacity-60" %>
        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No Partner Data</h3>
        <p class="text-sm mb-6">No partner spend data available for the last 6 months.</p>
      </div>
    </div>
  </div>
<% end %>
