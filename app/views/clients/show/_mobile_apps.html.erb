<% if @client.mobile_apps.active.any? %>
  <div class="bg-white dark:bg-gray-900 overflow-hidden rounded-xl border border-gray-200 dark:border-gray-800 shadow-sm">
    <div class="px-4 py-3 border-b border-gray-200 dark:border-gray-800">
      <div class="flex items-center justify-between">
        <h3 class="text-base font-medium text-gray-900 dark:text-white flex items-center">
          <%= icon "device-phone-mobile", class: "mr-2 h-4 w-4" %>
          Mobile Apps
        </h3>
        <span class="inline-flex items-center rounded-full bg-cyan-100 dark:bg-cyan-900/50 px-2 py-0.5 text-xs font-medium text-cyan-700 dark:text-cyan-300">
          <%= @client.mobile_apps.active.count %>
        </span>
      </div>
    </div>
    <div class="px-2" data-controller="toggle-content">
      <ul role="list" class="divide-y divide-gray-200 dark:divide-gray-800">
        <% @client.mobile_apps.active.order(:id).first(5).each do |app| %>
          <li class="py-3 px-2">
            <%= link_to mobile_app_path(app), class: "flex items-center hover:bg-gray-50 dark:hover:bg-gray-800 rounded-lg p-2 -m-2 transition-colors duration-150" do %>
              <div class="flex-shrink-0 mr-3">
                <% if app.logo.attached? %>
                  <%= image_tag app.logo.variant(format: :webp), 
                                class: "h-8 w-8 rounded object-contain border border-gray-200 dark:border-gray-700", 
                                alt: "#{app.name} icon" %>
                <% else %>
                  <div class="h-8 w-8 flex items-center justify-center rounded bg-cyan-100 dark:bg-cyan-900 text-cyan-700 dark:text-cyan-300 text-xs font-medium">
                    <%= app.name[0..1].upcase %>
                  </div>
                <% end %>
              </div>
              <div class="min-w-0 flex-1">
                <div class="text-sm font-medium text-gray-900 dark:text-white truncate">
                  <%= app.name %>
                </div>
                <div class="text-xs text-gray-500 dark:text-gray-400">
                  <%= app.platform&.upcase %>
                  <% if app.bundle_id.present? %>
                    • <%= truncate(app.bundle_id, length: 20) %>
                  <% end %>
                </div>
              </div>
              <div class="flex-shrink-0 ml-3">
                <%= icon "chevron-right", class: "h-4 w-4 text-gray-400 dark:text-gray-500" %>
              </div>
            <% end %>
          </li>
        <% end %>
        
        <% if @client.mobile_apps.active.count > 5 %>
          <div data-toggle-content-target="content" data-index="mobile-apps" class="hidden">
            <% @client.mobile_apps.active.order(:id).offset(5).each do |app| %>
              <li class="py-3 px-2">
                <%= link_to mobile_app_path(app), class: "flex items-center hover:bg-gray-50 dark:hover:bg-gray-800 rounded-lg p-2 -m-2 transition-colors duration-150" do %>
                  <div class="flex-shrink-0 mr-3">
                    <% if app.logo.attached? %>
                      <%= image_tag app.logo.variant(format: :webp), 
                                    class: "h-8 w-8 rounded object-contain border border-gray-200 dark:border-gray-700", 
                                    alt: "#{app.name} icon" %>
                    <% else %>
                      <div class="h-8 w-8 flex items-center justify-center rounded bg-cyan-100 dark:bg-cyan-900 text-cyan-700 dark:text-cyan-300 text-xs font-medium">
                        <%= app.name[0..1].upcase %>
                      </div>
                    <% end %>
                  </div>
                  <div class="min-w-0 flex-1">
                    <div class="text-sm font-medium text-gray-900 dark:text-white truncate">
                      <%= app.name %>
                    </div>
                    <div class="text-xs text-gray-500 dark:text-gray-400">
                      <%= app.platform&.upcase %>
                      <% if app.bundle_id.present? %>
                        • <%= truncate(app.bundle_id, length: 20) %>
                      <% end %>
                    </div>
                  </div>
                  <div class="flex-shrink-0 ml-3">
                    <%= icon "chevron-right", class: "h-4 w-4 text-gray-400 dark:text-gray-500" %>
                  </div>
                <% end %>
              </li>
            <% end %>
          </div>
        <% end %>
      </ul>
      
      <% if @client.mobile_apps.active.count > 5 %>
        <div class="px-2 py-3 border-t border-gray-200 dark:border-gray-700">
          <button data-toggle-content-target="trigger" 
                  data-index="mobile-apps"
                  data-action="click->toggle-content#toggle"
                  class="flex items-center text-sm text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300">
            <span>Show all apps</span>
            <%= icon "chevron-down", class: "ml-1 h-4 w-4 transition-transform duration-150", data: { toggle_content_target: "icon", index: "mobile-apps" } %>
          </button>
        </div>
      <% end %>
    </div>
  </div>
<% end %>
