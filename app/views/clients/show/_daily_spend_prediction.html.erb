<% # Prediction Insights Component %>
<% if @daily_spend_prediction.present? %>
  <div class="bg-white dark:bg-gray-900 overflow-hidden rounded-xl border border-gray-200 dark:border-gray-800 shadow-sm">
    <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-800">
      <h3 class="text-lg font-medium text-gray-900 dark:text-white flex items-center">
        <%= icon "light-bulb", class: "mr-2 h-5 w-5 text-primary-400" %>
        Prediction Insights
        <span class="ml-2 text-sm font-normal text-gray-500 dark:text-gray-400">
          (<%= Date.current.strftime("%B %Y") %>)
        </span>
      </h3>
    </div>

    <div class="px-6 py-5">
      <!-- Prediction Details -->
      <div class="mt-4 p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg border border-yellow-200 dark:border-yellow-800">
        <div class="flex items-start">
          <%= icon "light-bulb", class: "h-5 w-5 text-yellow-600 dark:text-yellow-400 mt-0.5 mr-2 flex-shrink-0" %>
          <div>
            <h5 class="text-sm font-medium text-yellow-800 dark:text-yellow-200">
              Prediction Insight
              <span class="text-xs font-normal">(<%= @daily_spend_prediction[:prediction_method] %>)</span>
              <% if @daily_spend_prediction[:confidence] %>
                <span class="text-xs px-2 py-1 rounded-full ml-2 <%= @daily_spend_prediction[:confidence] == "High" ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200" : @daily_spend_prediction[:confidence] == "Medium" ? "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200" : "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200" %>">
                  <%= @daily_spend_prediction[:confidence] %> Confidence
                </span>
              <% end %>
            </h5>
            <p class="text-sm text-yellow-700 dark:text-yellow-300 mt-1">
              Based on <%= @daily_spend_prediction[:prediction_method].downcase %> analysis, we predict a total of
              <strong>${<%= number_with_delimiter(@daily_spend_prediction[:predicted_total].round(2)) %></strong>
              by month end.
              <% if @daily_spend_prediction[:current_total] > 0 %>
                <% prediction_vs_current = ((@daily_spend_prediction[:predicted_total] - @daily_spend_prediction[:current_total]) / @daily_spend_prediction[:current_total] * 100).round(1) %>
                This represents a
                <strong><%= prediction_vs_current > 0 ? "+" : "" %><%= prediction_vs_current %>%</strong>
                change from current spend.
              <% elsif @daily_spend_prediction[:recent_trend] && @daily_spend_prediction[:recent_trend].any? %>
                <% last_month_amount = @daily_spend_prediction[:recent_trend].last[:amount] %>
                <% if last_month_amount > 0 %>
                  <% vs_last_month = ((@daily_spend_prediction[:predicted_total] - last_month_amount) / last_month_amount * 100).round(1) %>
                  This represents a
                  <strong><%= vs_last_month > 0 ? "+" : "" %><%= vs_last_month %>%</strong>
                  change from last month.
                <% end %>
              <% end %>
            </p>
            <% if @daily_spend_prediction[:recent_trend] && @daily_spend_prediction[:recent_trend].any? %>
              <p class="text-xs text-yellow-600 dark:text-yellow-400 mt-2">
                Recent trend data: <%= @daily_spend_prediction[:recent_trend].length %> months,
                latest: $<%= number_with_delimiter(@daily_spend_prediction[:recent_trend].last[:amount].round(2)) %>
                (<%= @daily_spend_prediction[:recent_trend].last[:formatted_month] %>)
              </p>
            <% end %>
          </div>
        </div>
      </div>
    </div>
  </div>
<% else %>
  <!-- No Prediction Data Available -->
  <div class="bg-white dark:bg-gray-900 overflow-hidden rounded-xl border-2 border-dashed border-gray-200 dark:border-gray-700">
    <div class="px-6 py-12 text-center">
      <div class="text-gray-400">
        <%= icon "light-bulb", class: "h-12 w-12 mx-auto mb-4 opacity-60" %>
        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No Prediction Data</h3>
        <p class="text-sm mb-6">No prediction data available for the current month.</p>
      </div>
    </div>
  </div>
<% end %>
