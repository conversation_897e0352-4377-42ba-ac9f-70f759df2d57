<% # Daily Spend Prediction Component %>
<% if @current_month_daily_spends.present? && @daily_spend_prediction.present? %>
  <div class="bg-white dark:bg-gray-900 overflow-hidden rounded-xl border border-gray-200 dark:border-gray-800 shadow-sm">
    <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-800">
      <h3 class="text-lg font-medium text-gray-900 dark:text-white flex items-center">
        <%= icon "chart-bar", class: "mr-2 h-5 w-5 text-primary-400" %>
        Current Month Spend Analysis
        <span class="ml-2 text-sm font-normal text-gray-500 dark:text-gray-400">
          (<%= Date.current.strftime("%B %Y") %>)
        </span>
      </h3>
    </div>

    <div class="px-6 py-5">
      <!-- Current Month Stats -->
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <div class="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 border border-blue-200 dark:border-blue-800">
          <div class="text-sm font-medium text-blue-600 dark:text-blue-400">Current Total</div>
          <div class="text-xl font-bold text-blue-900 dark:text-blue-100">
            $<%= number_with_delimiter(@daily_spend_prediction[:current_total].to_i) %>
          </div>
        </div>

        <div class="bg-green-50 dark:bg-green-900/20 rounded-lg p-4 border border-green-200 dark:border-green-800">
          <div class="text-sm font-medium text-green-600 dark:text-green-400">Predicted Total</div>
          <div class="text-xl font-bold text-green-900 dark:text-green-100">
            $<%= number_with_delimiter(@daily_spend_prediction[:predicted_total].to_i) %>
          </div>
        </div>

        <div class="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-4 border border-purple-200 dark:border-purple-800">
          <div class="text-sm font-medium text-purple-600 dark:text-purple-400">Daily Average</div>
          <div class="text-xl font-bold text-purple-900 dark:text-purple-100">
            $<%= number_with_delimiter(@daily_spend_prediction[:average_daily].to_i) %>
          </div>
        </div>

        <div class="bg-orange-50 dark:bg-orange-900/20 rounded-lg p-4 border border-orange-200 dark:border-orange-800">
          <div class="text-sm font-medium text-orange-600 dark:text-orange-400">Days Remaining</div>
          <div class="text-xl font-bold text-orange-900 dark:text-orange-100">
            <%= @daily_spend_prediction[:days_remaining] %>
          </div>
        </div>
      </div>

      <!-- Daily Spend Chart -->
      <div class="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-4">
        <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-3">Daily Spend Trend</h4>
        <%= line_chart @current_month_daily_spends,
                       prefix: "$",
                       thousands: ",",
                       height: "250px",
                       colors: ["#3B82F6"],
                       curve: false,
                       library: {
                         plugins: {
                           legend: {
                             display: false,
                           },
                         },
                         scales: {
                           x: {
                             grid: {
                               display: false,
                             },
                             ticks: {
                               maxRotation: 45,
                               minRotation: 45,
                               callback: "function(value, index, values) {
                                                          const date = new Date(this.getLabelForValue(value));
                                                          return date.getDate();
                                                        }",
                             },
                           },
                           y: {
                             beginAtZero: true,
                             grid: {
                               color: "rgba(156, 163, 175, 0.1)",
                             },
                           },
                         },
                       } %>
      </div>

      <!-- Prediction Details -->
      <div class="mt-4 p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg border border-yellow-200 dark:border-yellow-800">
        <div class="flex items-start">
          <%= icon "light-bulb", class: "h-5 w-5 text-yellow-600 dark:text-yellow-400 mt-0.5 mr-2 flex-shrink-0" %>
          <div>
            <h5 class="text-sm font-medium text-yellow-800 dark:text-yellow-200">
              Prediction Insight
              <span class="text-xs font-normal">(<%= @daily_spend_prediction[:prediction_method] %>)</span>
              <% if @daily_spend_prediction[:confidence] %>
                <span class="text-xs px-2 py-1 rounded-full ml-2 <%= @daily_spend_prediction[:confidence] == "High" ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200" : @daily_spend_prediction[:confidence] == "Medium" ? "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200" : "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200" %>">
                  <%= @daily_spend_prediction[:confidence] %> Confidence
                </span>
              <% end %>
            </h5>
            <p class="text-sm text-yellow-700 dark:text-yellow-300 mt-1">
              Based on <%= @daily_spend_prediction[:prediction_method].downcase %> analysis, we predict a total of
              <strong>${<%= number_with_delimiter(@daily_spend_prediction[:predicted_total].to_i) %></strong>
              by month end.
              <% if @daily_spend_prediction[:current_total] > 0 %>
                <% prediction_vs_current = ((@daily_spend_prediction[:predicted_total] - @daily_spend_prediction[:current_total]) / @daily_spend_prediction[:current_total] * 100).round(1) %>
                This represents a
                <strong><%= prediction_vs_current > 0 ? "+" : "" %><%= prediction_vs_current %>%</strong>
                change from current spend.
              <% elsif @daily_spend_prediction[:recent_trend] && @daily_spend_prediction[:recent_trend].any? %>
                <% last_month_amount = @daily_spend_prediction[:recent_trend].last[:amount] %>
                <% if last_month_amount > 0 %>
                  <% vs_last_month = ((@daily_spend_prediction[:predicted_total] - last_month_amount) / last_month_amount * 100).round(1) %>
                  This represents a
                  <strong><%= vs_last_month > 0 ? "+" : "" %><%= vs_last_month %>%</strong>
                  change from last month.
                <% end %>
              <% end %>
            </p>
            <% if @daily_spend_prediction[:recent_trend] && @daily_spend_prediction[:recent_trend].any? %>
              <p class="text-xs text-yellow-600 dark:text-yellow-400 mt-2">
                Recent trend data: <%= @daily_spend_prediction[:recent_trend].length %> months,
                latest: $<%= number_with_delimiter(@daily_spend_prediction[:recent_trend].last[:amount].to_i) %>
                (<%= @daily_spend_prediction[:recent_trend].last[:formatted_month] %>)
              </p>
            <% end %>
          </div>
        </div>
      </div>

      <!-- Additional Info -->
      <div class="mt-4 text-xs text-gray-500 dark:text-gray-400">
        <div class="flex justify-between">
          <span>Prediction Method: Linear trend based on daily average</span>
          <span>Last Updated: <%= Date.current.strftime("%b %d, %Y") %></span>
        </div>
      </div>
    </div>
  </div>
<% else %>
  <!-- No Daily Spend Data Available -->
  <div class="bg-white dark:bg-gray-900 overflow-hidden rounded-xl border-2 border-dashed border-gray-200 dark:border-gray-700">
    <div class="px-6 py-12 text-center">
      <div class="text-gray-400">
        <%= icon "chart-bar", class: "h-12 w-12 mx-auto mb-4 opacity-60" %>
        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No Daily Spend Data</h3>
        <p class="text-sm mb-6">No daily spend data available for the current month.</p>
      </div>
    </div>
  </div>
<% end %>
