<% if @client.client_events.any? %>
  <div class="bg-white dark:bg-gray-900 overflow-hidden rounded-xl border border-gray-200 dark:border-gray-800 shadow-sm">
    <div class="px-4 py-3 border-b border-gray-200 dark:border-gray-800">
      <div class="flex items-center justify-between">
        <h3 class="text-base font-medium text-gray-900 dark:text-white flex items-center">
          <%= icon "bolt", class: "mr-2 h-4 w-4" %>
          Tracking Events
        </h3>
        <span class="inline-flex items-center rounded-full bg-purple-100 dark:bg-purple-900/50 px-2 py-0.5 text-xs font-medium text-purple-700 dark:text-purple-300">
          <%= @client.client_events.count %> events
        </span>
      </div>
    </div>
    <div class="px-2">
      <div class="space-y-4">
        <% @client.client_events.order(:is_primary_kpi).reverse.each do |event| %>
          <div class="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
            <div class="flex items-start justify-between">
              <div class="flex-1">
                <div class="flex items-center gap-2 mb-2">
                  <h4 class="text-sm font-semibold text-gray-900 dark:text-white">
                    <%= event.feedmob_event || event.app_sdk_event %>
                  </h4>
                  <% if event.is_primary_kpi %>
                    <span class="inline-flex items-center rounded-full bg-primary-100 dark:bg-primary-900/50 px-2 py-0.5 text-xs font-medium text-primary-700 dark:text-primary-300">
                      <%= icon "star", class: "mr-1 h-3 w-3" %>Primary KPI
                    </span>
                  <% end %>
                </div>
                
                <% if event.description.present? %>
                  <p class="text-sm text-gray-600 dark:text-gray-400 mb-3">
                    <%= event.description %>
                  </p>
                <% end %>

                <div class="grid grid-cols-1 sm:grid-cols-3 gap-3 text-xs">
                  <% if event.goal_cpa.present? %>
                    <div>
                      <span class="text-gray-500 dark:text-gray-400">Goal CPA:</span>
                      <span class="font-medium text-gray-900 dark:text-white ml-1">$<%= event.goal_cpa %></span>
                    </div>
                  <% end %>
                  
                  <% if event.cvr_goal_from_install.present? %>
                    <div>
                      <span class="text-gray-500 dark:text-gray-400">CVR Goal:</span>
                      <span class="font-medium text-gray-900 dark:text-white ml-1"><%= event.cvr_goal_from_install.round(2) %>%</span>
                    </div>
                  <% end %>

                  <% if event.app_sdk_event.present? && event.feedmob_event.present? %>
                    <div>
                      <span class="text-gray-500 dark:text-gray-400">SDK Event:</span>
                      <span class="font-mono text-xs font-medium text-gray-900 dark:text-white ml-1"><%= event.app_sdk_event %></span>
                    </div>
                  <% end %>
                </div>
              </div>
            </div>
          </div>
        <% end %>
      </div>
    </div>
  </div>
<% else %>
  <div class="bg-white dark:bg-gray-900 overflow-hidden rounded-xl border border-gray-200 dark:border-gray-800 shadow-sm">
    <div class="px-4 py-3 border-b border-gray-200 dark:border-gray-800">
      <h3 class="text-base font-medium text-gray-900 dark:text-white flex items-center">
        <%= icon "bolt", class: "mr-2 h-4 w-4" %>
        Tracking Events
      </h3>
    </div>
    <div class="px-2">
      <div class="text-center py-6">
        <p class="text-xs text-gray-500 dark:text-gray-400">This client has no tracking events configured</p>
      </div>
    </div>
  </div>
<% end %>
