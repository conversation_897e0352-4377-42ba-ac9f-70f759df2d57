<%# filepath: /Users/<USER>/Projects/assistant/app/views/clients/show/_client_details.html.erb %>
<% if @client.client_detail.present? %>
  <div class="bg-white dark:bg-gray-900 overflow-hidden rounded-xl border border-gray-200 dark:border-gray-800 shadow-sm">
    <div class="px-4 py-3 border-b border-gray-200 dark:border-gray-800">
      <div class="flex items-center justify-between">
        <h3 class="text-base font-medium text-gray-900 dark:text-white flex items-center">
          <%= icon "file-check", library: "lucide", class: "mr-2 h-4 w-4" %>
          Client Details
        </h3>
      </div>
    </div>
    
    <div class="px-4 py-4 space-y-6">
      <!-- Contact Information -->
      <div class="space-y-3">
        <h4 class="text-sm font-semibold text-gray-900 dark:text-white pb-2 border-b border-gray-200 dark:border-gray-800 flex items-center">
          <%= icon "circle-user", library: "lucide", class: "mr-2 h-4 w-4 text-primary-400" %>
          Contact Information
        </h4>
        
        <% 
          contact_fields = [
            { field: @client.client_detail.contact_name, label: "Contact Name", icon: "user" },
            { field: @client.client_detail.preferred_communication_channel&.upcase, label: "Preferred Communication", icon: "message-circle" },
            { field: @client.client_detail.account_executive_user&.name, label: "Account Executive", icon: "briefcase" },
            { field: @client.client_detail.account_manager_user&.name, label: "Account Manager", icon: "briefcase" }
          ].select { |item| item[:field].present? }
        %>
        
        <% if contact_fields.any? %>
          <dl class="grid grid-cols-1 sm:grid-cols-2 gap-x-4 gap-y-3">
            <% contact_fields.each do |item| %>
              <div>
                <dt class="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide flex items-center">
                  <%= icon item[:icon], library: "lucide", class: "mr-1.5 h-3 w-3" %>
                  <%= item[:label] %>
                </dt>
                <dd class="mt-1 text-sm font-medium text-gray-900 dark:text-white">
                  <%= item[:field] %>
                </dd>
              </div>
            <% end %>
          </dl>
        <% else %>
          <div class="text-center py-4">
            <p class="text-xs text-gray-500 dark:text-gray-400">No contact information available</p>
          </div>
        <% end %>
      </div>

      <!-- Business Information -->
      <div class="space-y-3">
        <h4 class="text-sm font-semibold text-gray-900 dark:text-white pb-2 border-b border-gray-200 dark:border-gray-800 flex items-center">
          <%= icon "building", library: "lucide", class: "mr-2 h-4 w-4 text-green-400" %>
          Business Information
        </h4>
        
        <% 
          business_fields = [
            { field: @client.client_detail.target_audience, label: "Target Audience" },
            { field: @client.client_detail.client_persona, label: "Client Persona" },
            { field: @client.client_detail.user_behavior_notes, label: "User Behavior Notes" }
          ].select { |item| item[:field].present? }
        %>
        
        <% if business_fields.any? %>
          <div class="space-y-3">
            <% business_fields.each do |item| %>
              <div>
                <dt class="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide mb-1">
                  <%= item[:label] %>
                </dt>
                <dd class="text-sm text-gray-900 dark:text-white leading-relaxed">
                  <%= simple_format item[:field], class: "mb-0" %>
                </dd>
              </div>
            <% end %>
          </div>
        <% else %>
          <div class="text-center py-4">
            <p class="text-xs text-gray-500 dark:text-gray-400">No business information available</p>
          </div>
        <% end %>
      </div>

      <!-- Geographic & Technical Information -->
      <div class="space-y-3">
        <h4 class="text-sm font-semibold text-gray-900 dark:text-white pb-2 border-b border-gray-200 dark:border-gray-800 flex items-center">
          <%= icon "globe", library: "lucide", class: "mr-2 h-4 w-4 text-purple-400" %>
          Geographic & Technical
        </h4>
        
        <% geo_tech_present = @client.client_detail.supported_geos.present? && @client.client_detail.supported_geos.any? || @client.client_detail.user_flow_description.present? %>
        
        <% if geo_tech_present %>
          <div class="space-y-3">
            <!-- Supported Geos -->
            <% if @client.client_detail.supported_geos.present? && @client.client_detail.supported_geos.any? %>
              <div>
                <dt class="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide mb-2 flex items-center">
                  <%= icon "map-pin", library: "lucide", class: "mr-1.5 h-3 w-3" %>
                  Supported Geos
                </dt>
                <dd class="flex flex-wrap gap-1">
                  <% @client.client_detail.supported_geos.each do |geo| %>
                    <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-purple-100 dark:bg-purple-900/50 text-purple-700 dark:text-purple-300">
                      <%= geo %>
                    </span>
                  <% end %>
                </dd>
              </div>
            <% end %>

            <!-- User Flow Description -->
            <% if @client.client_detail.user_flow_description.present? %>
              <div>
                <dt class="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide mb-1 flex items-center">
                  <%= icon "workflow", library: "lucide", class: "mr-1.5 h-3 w-3" %>
                  User Flow Description
                </dt>
                <dd class="text-sm text-gray-900 dark:text-white leading-relaxed">
                  <%= simple_format @client.client_detail.user_flow_description, class: "mb-0" %>
                </dd>
              </div>
            <% end %>
          </div>
        <% else %>
          <div class="text-center py-4">
            <p class="text-xs text-gray-500 dark:text-gray-400">No geographic or technical information</p>
          </div>
        <% end %>
      </div>

      <!-- Challenges & Restrictions -->
      <% 
        challenge_fields = [
          { field: @client.client_detail.biggest_obstacles, label: "Biggest Obstacles", icon: "triangle-alert" },
          { field: @client.client_detail.noteworthy_restrictions, label: "Noteworthy Restrictions", icon: "shield-alert" }
        ].select { |item| item[:field].present? }
      %>
      
      <div class="space-y-3">
        <h4 class="text-sm font-semibold text-gray-900 dark:text-white pb-2 border-b border-gray-200 dark:border-gray-800 flex items-center">
          <%= icon "shield-alert", library: "lucide", class: "mr-2 h-4 w-4 text-red-400" %>
          Challenges & Restrictions
        </h4>
        
        <% if challenge_fields.any? %>
          <div class="space-y-3">
            <% challenge_fields.each do |item| %>
              <div>
                <dt class="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide mb-1 flex items-center">
                  <%= icon item[:icon], library: "lucide", class: "mr-1.5 h-3 w-3" %>
                  <%= item[:label] %>
                </dt>
                <dd class="text-sm text-gray-900 dark:text-white leading-relaxed">
                  <%= simple_format item[:field], class: "mb-0" %>
                </dd>
              </div>
            <% end %>
          </div>
        <% else %>
          <div class="text-center py-4">
            <p class="text-xs text-gray-500 dark:text-gray-400">No challenges or restrictions reported</p>
          </div>
        <% end %>
      </div>

      <!-- Additional Notes Section -->
      <% if @client.client_detail.additional_notes.present? %>
        <div class="space-y-3">
          <h4 class="text-sm font-semibold text-gray-900 dark:text-white pb-2 border-b border-gray-200 dark:border-gray-800 flex items-center">
            <%= icon "file-text", library: "lucide", class: "mr-2 h-4 w-4 text-yellow-400" %>
            Additional Notes
          </h4>
          
          <div class="text-sm text-gray-900 dark:text-white leading-relaxed">
            <%= simple_format @client.client_detail.additional_notes, class: "mb-0" %>
          </div>
        </div>
      <% end %>
    </div>
  </div>
<% else %>
  <!-- Fallback when no client_detail exists -->
  <div class="bg-white dark:bg-gray-900 overflow-hidden rounded-xl border-2 border-dashed border-gray-300 dark:border-gray-700">
    <div class="px-4 py-4 text-center">
      <p class="text-xs text-gray-500 dark:text-gray-400">Client details haven't been added yet.</p>
    </div>
  </div>
<% end %>
