<div class="bg-white dark:bg-gray-900 overflow-hidden rounded-xl border border-gray-200 dark:border-gray-800 shadow-sm">
  <div class="px-4 py-3 border-b border-gray-200 dark:border-gray-800">
    <div class="flex items-center justify-between">
      <h3 class="text-base font-medium text-gray-900 dark:text-white flex items-center">
        <%= icon "chat-bubble-left-right", class: "mr-2 h-4 w-4" %>
        Slack Messages
      </h3>
      <% if @client.slack_messages.any? %>
        <span class="inline-flex items-center rounded-full bg-purple-100 dark:bg-purple-900/50 px-2 py-0.5 text-xs font-medium text-purple-700 dark:text-purple-300">
          <%= @client.slack_messages.count %>
        </span>
      <% end %>
    </div>
  </div>
  <div class="px-2">
    <% if @client.slack_messages.any? %>
      <ul role="list" class="divide-y divide-gray-200 dark:divide-gray-800">
        <% @client.slack_messages.order(message_time: :desc).first(3).each do |message| %>
          <li class="py-3 px-2">
            <div class="flex items-start space-x-3">
              <div class="flex-shrink-0">
                <div class="h-8 w-8 rounded-full bg-purple-100 dark:bg-purple-900 flex items-center justify-center">
                  <%= icon "chat-bubble-left", class: "h-4 w-4 text-purple-600 dark:text-purple-400" %>
                </div>
              </div>
              <div class="min-w-0 flex-1">
                <div class="text-sm text-gray-900 dark:text-white">
                  <span class="font-medium"><%= message.message_user_name || "Unknown User" %></span>
                  <span class="text-gray-500 dark:text-gray-400">in #<%= message.channel_name || message.channel_id %></span>
                </div>
                <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  <%= message.message_time&.strftime("%b %d, %Y at %I:%M %p") %>
                </div>
                <div class="text-sm text-gray-600 dark:text-gray-300 mt-2 line-clamp-2">
                  <%= truncate(message.message_text, length: 100) %>
                </div>
              </div>
            </div>
          </li>
        <% end %>
      </ul>
      
      <div class="px-2 py-3 border-t border-gray-200 dark:border-gray-700">
        <%= link_to slack_messages_path(client_id: @client.id), 
              class: "flex items-center text-sm text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300" do %>
          <span>View all linked messages</span>
          <%= icon "arrow-top-right-on-square", class: "ml-1 h-4 w-4" %>
        <% end %>
      </div>
    <% else %>
      <div class="text-center py-6">
        <p class="text-xs text-gray-500 dark:text-gray-400">This client has no linked Slack messages</p>
      </div>
    <% end %>
  </div>
</div>