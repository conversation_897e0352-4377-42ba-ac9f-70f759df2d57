<div data-controller="dropdown" class="relative inline-block text-left">
  <button type="button" 
          data-action="dropdown#toggle" 
          class="<%= button_classes %>">
    <% if local_assigns[:icon_name] %>
      <%= icon local_assigns[:icon_name], library: "lucide", class: "h-4 w-4 #{local_assigns[:show_label] ? 'mr-2' : ''}" %>
    <% end %>
    <% if local_assigns[:show_label] %>
      <span><%= current_label %></span>
    <% end %>
    <% unless local_assigns[:hide_chevron] %>
      <%= icon "chevron-down", set: "mini", class: "-mr-1 #{local_assigns[:show_label] ? 'ml-1.5' : 'ml-1'} h-5 w-5 text-gray-400" %>
    <% end %>
  </button>

  <div data-dropdown-target="menu"
      data-transition-enter-from="opacity-0 scale-95"
      data-transition-enter-to="opacity-100 scale-100"
      data-transition-leave-from="opacity-100 scale-100"
      data-transition-leave-to="opacity-0 scale-95"
      class="hidden absolute <%= local_assigns[:menu_position] || 'right-0' %> z-30 mt-2 <%= local_assigns[:menu_width] || 'w-56' %> origin-top-right rounded-md bg-gray-800 shadow-lg ring-1 ring-gray-700 focus:outline-none">
    <div class="py-1">
      <% if local_assigns[:include_blank] %>
        <label class="flex items-center px-4 py-2 hover:bg-gray-700 cursor-pointer">
          <%= form.radio_button field, "", 
              class: "hidden peer", 
              data: { action: "auto-submit#submit" } %>
          <span class="text-sm font-medium text-gray-200 peer-checked:text-primary-400">
            <%= local_assigns[:blank_text] || "Choose..." %>
          </span>
          <%= icon "check", class: "ml-auto h-5 w-5 text-primary-400 hidden peer-checked:block" %>
        </label>
        <div class="border-t border-gray-700 my-1"></div>
      <% end %>
      
      <%= form.collection_radio_buttons field, collection, value_method, label_method, 
          {}, data: { action: "auto-submit#submit" } do |b| %>
        <label class="flex items-center px-4 py-2 hover:bg-gray-700 cursor-pointer">
          <%= b.radio_button class: "hidden peer" %>
          <div class="flex-1">
            <span class="text-sm font-medium text-gray-200 peer-checked:text-primary-400">
              <%= b.text %>
            </span>
            <% if local_assigns[:show_descriptions] && local_assigns[:descriptions] && local_assigns[:descriptions][b.value] %>
              <p class="text-xs text-gray-400 mt-0.5">
                <%= local_assigns[:descriptions][b.value] %>
              </p>
            <% end %>
          </div>
          <%= icon "check", class: "ml-auto h-5 w-5 text-primary-400 hidden peer-checked:block" %>
        </label>
      <% end %>
    </div>
  </div>
</div>