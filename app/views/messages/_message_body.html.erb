<div id="<%= dom_id(message) %>_body">
  <% if message.error? && message.error_message.present? %>
    <div class="mt-2 p-4 rounded-md bg-red-900/20 border border-red-800">
      <div class="text-sm text-red-300">
        <%= message.error_message %>
      </div>
    </div>
  <% end %>

  <% if message.reasoning_content.present? && message.role != "user" %>
    <div data-controller="markdown" class="py-3 mb-5 w-full">
      <pre class="hidden" data-markdown-target="content"><%= message.reasoning_content %></pre>

      <article data-markdown-target="rendered"
              class="w-full max-w-none bg-gray-800 border-l-4 border-indigo-500 px-4 py-3 rounded-r-md text-gray-400 break-words prose-sm prose prose-slate prose-invert">
      </article>
    </div>
  <% end %>

  <div data-controller="markdown">
    <article data-content-target="raw" class="hidden text-gray-400 break-words prose prose-slate prose-invert">
      <pre class="bg-gray-800" data-markdown-target="content"><%= message.content %></pre>
    </article>

    <article data-markdown-target="rendered" data-content-target="rendered"
            class="text-gray-400 max-w-none break-words prose prose-slate prose-invert prose-pre:bg-code prose-pre:mt-0 prose-pre:mb-0 prose-code:before:hidden prose-code:after:hidden prose-th:p-2 prose-td:p-2 prose-table:bg-emerald-950 prose-table:rounded-md prose-table:m-0">
    </article>
  </div>

  <% if message.metadata&.dig("references").present? && message.role != "user" %>
    <div class="mt-4 space-y-2">
      <h4 class="font-medium text-sm text-gray-300">References</h4>
      <div class="bg-gray-800 rounded-md p-3 border border-gray-700">
        <ul class="space-y-2 text-sm">
          <% message.metadata["references"].each do |reference| %>
            <li class="flex items-start gap-2">
              <%= icon "document-text", class: "h-4 w-4 mt-0.5 text-gray-400 flex-shrink-0" %>
              <a href="<%= reference['url'] %>" target="_blank" class="text-blue-400 hover:underline break-all flex-1">
                <%= reference['text'] %>
                <span class="inline-block ml-1 text-gray-400">
                  <%= icon "arrow-top-right-on-square", class: "h-3 w-3 inline" %>
                </span>
              </a>
            </li>
          <% end %>
        </ul>
      </div>
    </div>
  <% end %>

  <% if message.images.attached? %>
    <div class="mt-2 space-y-2">
      <% message.images.each do |image| %>
        <%= render "messages/image", image: image %>
      <% end %>
    </div>
  <% end %>

  <% if message.documents.attached? %>
    <div class="attachments mt-2 space-y-2">
      <% message.documents.each do |document| %>
        <%= render "messages/document", document: document %>
      <% end %>
    </div>
  <% end %>

  <% if message.files.attached? %>
    <div class="attachments mt-2 space-y-2">
      <% message.files.each do |file| %>
        <%= render "messages/document", document: file %>
      <% end %>
    </div>
  <% end %>

  <%= render "messages/tool_result", message: message %>
</div>