<%# Context Selection Component - Allows users to select clients, partners, and client documents for conversation context %>
<%= form_with(model: [@conversation, @conversation.conversation_contexts.build], 
              url: conversation_conversation_contexts_path(@conversation),
              data: { action: "turbo:submit-start->loading#showLoading turbo:submit-end->loading#hideLoading", controller: "loading auto-submit" },
              class: "context-selection-form") do |form| %>
  
  <!-- Context Selection Controls -->
  <div class="space-y-3">
    <!-- Filter Dropdowns -->
    <div class="grid grid-cols-1 gap-3">
      <div class="grid grid-cols-2 gap-3">
        <%= render 'shared/filter_dropdown', 
            form: form, 
            field: :client_ids, 
            label: "Clients",
            icon_name: "heart-handshake",
            collection: @clients, 
            selected: @conversation.conversation_contexts.where(contextual_type: 'Client').pluck(:contextual_id),
            options: { 
              value_method: :id, 
              label_method: :name,
              placeholder: "Select clients...",
              hide_select_all: true,
              width_full: true
            } %>

        <%= render 'shared/filter_dropdown', 
            form: form, 
            field: :partner_ids, 
            label: "Partners",
            icon_name: "package",
            collection: @partners, 
            selected: @conversation.conversation_contexts.where(contextual_type: 'Partner').pluck(:contextual_id),
            options: { 
              value_method: :id, 
              label_method: :name,
              placeholder: "Select partners...",
              hide_select_all: true,
              width_full: true
            } %>
      </div>

      <%= render 'shared/filter_dropdown', 
          form: form, 
          field: :client_document_ids, 
          label: "Client Documents",
          icon_name: "file-text",
          collection: @available_client_documents, 
          selected: @conversation.conversation_contexts.where(contextual_type: 'ClientDocument').pluck(:contextual_id),
          options: { 
            value_method: :id, 
            label_method: :display_name,
            placeholder: "Select client documents...",
            hide_select_all: true,
            width_full: true,
            display_spinner: true
          } %>
    </div>
    
    <!-- Date Range Selection -->
    <div class="pt-2 border-t border-gray-700">
      <div class="grid grid-cols-2 gap-3">
        <div>
          <label class="block text-sm font-medium text-gray-300 mb-1">
            Start Date
          </label>
          <%= form.date_field :context_start_date,
              value: @conversation.context_settings.fetch("context_start_date", nil),
              class: "w-full px-3 py-2 text-sm bg-gray-700 border border-gray-600 rounded-md text-gray-200 focus:outline-none focus:ring-2 focus:ring-primary-400 focus:border-primary-400",
              data: { action: "auto-submit#submit" } %>
        </div>
        
        <div>
          <label class="block text-sm font-medium text-gray-300 mb-1">
            End Date
          </label>
          <%= form.date_field :context_end_date,
              value: @conversation.context_settings.fetch("context_end_date", nil),
              class: "w-full px-3 py-2 text-sm bg-gray-700 border border-gray-600 rounded-md text-gray-200 focus:outline-none focus:ring-2 focus:ring-primary-400 focus:border-primary-400",
              data: { action: "auto-submit#submit" } %>
        </div>
      </div>
    </div>
    
    <!-- Include Activities Toggle -->
    <div class="pt-2 border-t border-gray-700">
      <div class="flex items-center">
        <label class="flex items-center space-x-2 cursor-pointer">
          <%= form.check_box :include_activities,
              checked: @conversation.context_settings.fetch("include_activities", false),
              class: "h-4 w-4 rounded border-gray-600 bg-gray-700 text-primary-600 focus:ring-primary-400",
              data: { action: "auto-submit#submit" } %>
          <span class="text-sm font-medium text-gray-300">
            Include Activities
          </span>
        </label>
      </div>
    </div>
    
    <!-- View Context Button -->
    <div class="pt-2">
      <%= link_to context_conversation_path(@conversation),
          target: "_top",
          class: "w-full inline-flex items-center justify-center gap-2 px-3 py-2 text-sm font-medium text-gray-200 bg-gray-800 hover:bg-gray-700 border border-gray-800 hover:border-gray-500 rounded-md transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-400 focus:ring-offset-1 focus:ring-offset-gray-800 hover:text-white group",
          title: "View selected context details" do %>
        <%= icon "scan-eye", library: "lucide", class: "h-4 w-4 text-gray-400 group-hover:text-gray-200 transition-colors duration-200" %>
        <span>View Context</span>
      <% end %>
    </div>
  </div>
<% end %>