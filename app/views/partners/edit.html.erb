<% content_for :title, "#{@partner.persisted? ? 'Edit' : 'New'} Partner" %>

<div class="w-full h-screen flex flex-col">
  <% if @partner.errors.any? %>
    <%= alerting :error, @partner.errors.full_messages.join(", ") %>
  <% end %>

  <header class="border-b border-gray-800 bg-gray-900 shadow-sm">
    <div class="max-w-7xl px-4 h-16 flex items-center sm:px-6 2xl:px-12 mx-auto">
      <div class="flex flex-col md:flex-row md:items-center md:justify-between w-full">
        <div class="flex items-end space-x-2">
          <h1 class="text-xl font-semibold text-white">
            <%= @partner.persisted? ? "Edit Partner" : "New Partner" %>
          </h1>
          <% if @partner.persisted? %>
            <span class="inline-block border border-primary-700 rounded-md px-1.5 py-0.5 text-xs font-medium text-primary-300 tracking-wide">
              ID: <%= @partner.id %>
            </span>
          <% end %>
        </div>

        <div class="flex items-center space-x-4">
          <% if @partner.persisted? %>
            <%= link_to partner_path(@partner, section: @current_section), 
              class: "inline-flex items-center gap-x-2 rounded-full bg-primary-900 px-4 py-2 text-sm font-medium text-primary-300 hover:bg-primary-800 transition-all duration-200 ease-in-out shadow-sm hover:shadow-md" do %>
              <%= icon "arrow-left", class: "h-4 w-4" %>
              <span>Back to partner</span>
            <% end %>
          <% end %>
        </div>
      </div>
    </div>
  </header>

  <div class="grow overflow-y-scroll bg-gray-950">
    <div class="mx-auto max-w-7xl px-4 sm:px-6 2xl:px-12 py-6">
      <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
        <!-- Navigation Sidebar -->
        <div class="lg:col-span-1">
          <div class="bg-gray-900 rounded-lg shadow-sm border border-gray-800 p-4 sticky top-6">
            <nav class="space-y-2">
              <% @sections.each do |section| %>
                <% is_active = @current_section == section[:key] || (@current_section.present? && !@sections.any? { |s| s[:key] == @current_section } && section[:key] == 'overview') %>
                <%= link_to section[:label], edit_partner_path(@partner, section: section[:key]), class: "block w-full text-left px-3 py-2 rounded-md text-sm font-medium transition-colors #{is_active ? 'bg-primary-800 text-primary-300' : 'text-gray-300 hover:bg-gray-800'}" %>
              <% end %>
            </nav>
          </div>
        </div>

        <!-- Main Content Area -->
        <div class="lg:col-span-3">
          <%= form_with(model: @partner, local: true, multipart: true) do |form| %>
            <div class="space-y-6">
              <% case @current_section %>
              <% when 'overview' %>
                <%= render "partners/form_sections/form_basics", form: form, partner: @partner %>
                <%= render "partners/form_sections/partner_social_media_links", form: form, partner: @partner %>
                <%= render "partners/form_sections/form_details", form: form, partner: @partner %>
              <% when 'testimonials' %>
                <%= render "partners/form_sections/partner_quotes", form: form, partner: @partner %>
                <%= render "partners/form_sections/partner_use_cases", form: form, partner: @partner %>
              <% when 'documents' %>
                <%= render "partners/form_sections/partner_documents", form: form, partner: @partner %>
              <% else %>
                <%= render "partners/form_sections/form_basics", form: form, partner: @partner %>
              <% end %>

              <!-- Form Actions -->
              <div class="bg-gray-900 overflow-hidden rounded-xl border border-gray-800 shadow-sm sticky bottom-0 z-10">
                <div class="px-6 py-5 bg-gradient-to-r from-gray-800 to-gray-900">
                  <div class="flex items-center justify-between">
                    <!-- Left side - Form status/info -->
                    <div class="flex items-center space-x-4">
                      <% if @partner.persisted? %>
                        <div class="flex items-center text-sm text-gray-400">
                          <%= icon "clock", class: "mr-1.5 h-4 w-4" %>
                          Last updated <%= time_ago_in_words(@partner.updated_at) %> ago
                        </div>
                      <% else %>
                        <div class="flex items-center text-sm text-gray-400">
                          <%= icon "plus-circle", class: "mr-1.5 h-4 w-4" %>
                          Creating new partner
                        </div>
                      <% end %>
                    </div>

                    <!-- Right side - Action buttons -->
                    <div class="flex items-center space-x-3">
                      <%= link_to @partner.persisted? ? partner_path(@partner, section: @current_section) : partners_path, 
                                  class: "inline-flex items-center px-5 py-2.5 border border-gray-600 shadow-sm text-sm font-medium rounded-lg text-gray-300 bg-gray-800 hover:bg-gray-700 hover:border-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-all duration-200 ease-in-out" do %>
                        <%= icon "x-mark", class: "mr-2 h-4 w-4" %>
                        Cancel
                      <% end %>
                      
                      <% if @partner.persisted? %>
                        <%= form.submit "Update Partner", 
                                        class: "cursor-pointer inline-flex items-center px-6 py-2.5 border border-transparent shadow-sm text-sm font-medium rounded-lg text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-all duration-200 ease-in-out transform hover:scale-[1.02] active:scale-[0.98]",
                                        data: { disable_with: "#{icon('arrow-path', class: 'animate-spin mr-2 h-4 w-4')} Updating...".html_safe } %>
                      <% else %>
                        <%= form.submit "Create Partner", 
                                        class: "cursor-pointer inline-flex items-center px-6 py-2.5 border border-transparent shadow-sm text-sm font-medium rounded-lg text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-all duration-200 ease-in-out transform hover:scale-[1.02] active:scale-[0.98]",
                                        data: { disable_with: "#{icon('arrow-path', class: 'animate-spin mr-2 h-4 w-4')} Creating...".html_safe } %>
                      <% end %>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          <% end %>
        </div>
      </div>
    </div>
  </div>
</div>
