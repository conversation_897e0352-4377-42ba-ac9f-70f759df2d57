<% content_for :title, "Partner: #{@partner.name}" %>

<div class="w-full h-full flex flex-col bg-gray-950">
  <% if notice.present? %>
    <%= alerting :info, notice %>
  <% end %>

  <header class="border-b border-gray-800 bg-gray-900 shadow-sm sticky top-0 z-10">
    <div class="max-w-7xl px-4 h-16 flex items-center sm:px-6 2xl:px-12 mx-auto">
      <div class="flex items-center justify-between w-full">
        <div class="flex items-center space-x-3">
          <%= image_tag "feedmob-logo-full-white-1000px.png", class: "mx-auto h-7 w-auto", alt: "feedmob logo" %>
          <div class="h-6 border-r border-gray-700"></div>
          <div class="flex items-center">
            <span class="text-base font-medium text-white"><%= @partner.name %></span>
            <span class="ml-2.5 inline-block border border-primary-700 rounded-md px-2 py-0.5 text-xs font-medium text-primary-300 tracking-wide">
              partner details
            </span>
          </div>
        </div>

        <div class="flex items-center space-x-3">
          <div class="flex items-center space-x-3">
            <%= link_to partners_path, 
              class: "inline-flex items-center gap-x-2 rounded-full bg-gray-800 px-4 py-2 text-sm font-medium text-gray-300 hover:bg-gray-700 transition-all duration-200 ease-in-out shadow-sm hover:shadow-md" do %>
              <%= icon "arrow-left", class: "h-4 w-4" %>
              <span>Back to Partners</span>
            <% end %>
          </div>

          <% current_section_data = @sections.find { |s| s[:key] == @current_section } %>
          <% if current_section_data&.fetch(:editable, true) %>
            <div class="flex items-center space-x-3">
              <%= link_to edit_partner_path(@partner, section: @current_section), 
                class: "inline-flex items-center gap-x-2 rounded-full bg-primary-800 px-4 py-2 text-sm font-medium text-primary-300 hover:bg-primary-700 transition-all duration-200 ease-in-out shadow-sm hover:shadow-md" do %>
                <%= icon "pencil", class: "h-4 w-4" %>
                <span>Edit</span>
              <% end %>
            </div>
          <% end %>

          <!-- Audit logs button - subtle and less prominent -->
          <div class="flex items-center">
            <%= link_to audit_logs_path(model: "Partner", model_id: @partner.id), 
              class: "p-2 text-gray-500 hover:text-gray-300 transition-colors duration-200",
              title: "View audit logs" do %>
              <%= icon "document-text", class: "h-4 w-4" %>
            <% end %>
          </div>
        </div>
      </div>
    </div>
  </header>

  <div class="grow overflow-y-auto">
    <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-6">
      <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
        <!-- Navigation Sidebar -->
        <div class="lg:col-span-1">
          <div class="bg-gray-900 rounded-lg shadow-sm border border-gray-800 p-4 sticky top-6">
            <nav class="space-y-2">
              <% @sections.each do |section| %>
                <%= link_to section[:label], partner_path(@partner, section: section[:key]), class: "block w-full text-left px-3 py-2 rounded-md text-sm font-medium transition-colors #{@current_section == section[:key] ? 'bg-primary-800 text-primary-300' : 'text-gray-300 hover:bg-gray-800'}" %>
              <% end %>
            </nav>
          </div>
        </div>

        <!-- Main Content Area -->
        <div class="lg:col-span-3">
          <div class="space-y-6">
            <% case @current_section %>
            <% when 'overview' %>
              <%= render 'partners/show/basic_info' %>
              <%= render 'partners/show/partner_details' if @partner.partner_detail.present? %>
            <% when 'testimonials' %>
              <%= render 'partners/show/testimonials' %>
              <%= render 'partners/show/use_cases' %>
            <% when 'documents' %>
              <%= render 'partners/show/documents' %>
            <% else %>
              <%= render 'partners/show/basic_info' %>
              <%= render 'partners/show/partner_details' if @partner.partner_detail.present? %>
            <% end %>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
