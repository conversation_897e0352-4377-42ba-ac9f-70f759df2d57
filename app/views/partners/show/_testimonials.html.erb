<div class="bg-white dark:bg-gray-900 overflow-hidden rounded-xl border border-gray-200 dark:border-gray-800 shadow-sm">
  <div class="px-4 py-3 border-b border-gray-200 dark:border-gray-800">
    <div class="flex items-center justify-between">
      <h3 class="text-base font-medium text-gray-900 dark:text-white flex items-center">
        <%= icon "chat-bubble-left-right", class: "mr-2 h-4 w-4" %>
        Testimonials
      </h3>
      <% if @partner.partner_quotes.any? %>
        <span class="inline-flex items-center rounded-full bg-blue-100 dark:bg-blue-900/50 px-2 py-0.5 text-xs font-medium text-blue-700 dark:text-blue-300">
          <%= @partner.partner_quotes.count %>
        </span>
      <% end %>
    </div>
  </div>
  <div class="px-2">
    <% if @partner.partner_quotes.any? %>
      <div class="space-y-6 py-3">
        <% @partner.partner_quotes.each_with_index do |quote, index| %>
          <div class="px-2">
            <blockquote class="p-6 bg-gray-50 dark:bg-gray-800 rounded-lg border-l-4 border-primary-500">
              <div class="absolute -top-2 -left-2 text-primary-400">
                <svg class="w-8 h-8" fill="currentColor" viewBox="0 0 32 32">
                  <path d="M9.352 4C4.456 7.456 1 13.12 1 19.36c0 5.088 3.072 8.064 6.624 8.064 3.36 0 5.856-2.688 5.856-5.856 0-3.168-2.208-5.472-5.088-5.472-.576 0-1.344.096-1.536.192.48-3.264 3.552-7.104 6.624-9.024L9.352 4zm16.512 0c-4.8 3.456-8.256 9.12-8.256 15.36 0 5.088 3.072 8.064 6.624 8.064 3.264 0 5.856-2.688 5.856-5.856 0-3.168-2.304-5.472-5.184-5.472-.576 0-1.248.096-1.44.192.48-3.264 3.456-7.104 6.528-9.024L25.864 4z"/>
                </svg>
              </div>
              <p class="text-base italic text-gray-700 dark:text-gray-300 mb-4 leading-relaxed relative z-10">
                <%= quote.quote_text %>
              </p>
              <footer class="text-sm text-gray-500 dark:text-gray-400 flex items-center">
                <div class="h-10 w-10 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center text-blue-700 dark:text-blue-300 font-medium mr-3">
                  <%= quote.full_name[0].upcase %>
                </div>
                <div>
                  <div class="font-medium text-gray-900 dark:text-white"><%= quote.full_name %></div>
                  <% if quote.job_title.present? && quote.company_name.present? %>
                    <div><%= quote.job_title %>, <%= quote.company_name %></div>
                  <% elsif quote.job_title.present? %>
                    <div><%= quote.job_title %></div>
                  <% elsif quote.company_name.present? %>
                    <div><%= quote.company_name %></div>
                  <% end %>
                </div>
              </footer>
            </blockquote>
          </div>
        <% end %>
      </div>
    <% else %>
      <div class="text-center py-6">
        <p class="text-xs text-gray-500 dark:text-gray-400">This partner has no testimonials</p>
      </div>
    <% end %>
  </div>
</div>