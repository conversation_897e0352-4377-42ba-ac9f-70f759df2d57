<div class="bg-gray-900 overflow-hidden rounded-xl border border-gray-800 shadow-sm">
  <div class="px-4 py-3 border-b border-gray-800">
    <div class="flex items-center justify-between">
      <h3 class="text-base font-medium text-white flex items-center">
        <%= icon "light-bulb", class: "mr-2 h-4 w-4" %>
        Use Cases
      </h3>
      <% if @partner.partner_use_cases.any? %>
        <span class="inline-flex items-center rounded-full bg-blue-900/50 px-2 py-0.5 text-xs font-medium text-blue-300">
          <%= @partner.partner_use_cases.count %>
        </span>
      <% end %>
    </div>
  </div>
  <div class="px-2">
    <% if @partner.partner_use_cases.any? %>
      <div class="space-y-4 py-3" data-controller="toggle-content">
        <% @partner.partner_use_cases.each_with_index do |use_case, index| %>
          <div class="px-2">
            <div class="bg-gray-800 rounded-lg border border-gray-700 overflow-hidden">
              <div class="p-4 cursor-pointer hover:bg-gray-750 transition-colors"
                   data-action="click->toggle-content#toggle"
                   data-toggle-content-target="trigger"
                   data-index="<%= index %>">
                <div class="flex justify-between items-start">
                  <div class="flex-1">
                    <h4 class="text-base font-semibold text-white mb-1">
                      <%= use_case.title %>
                    </h4>
                    <p class="text-sm text-gray-400">
                      Click to expand details
                    </p>
                  </div>
                  <div class="flex-shrink-0 ml-4">
                    <svg xmlns="http://www.w3.org/2000/svg" 
                         class="h-5 w-5 text-gray-400 transition-transform" 
                         fill="none" 
                         viewBox="0 0 24 24" 
                         stroke="currentColor" 
                         data-toggle-content-target="icon" 
                         data-index="<%= index %>">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                    </svg>
                  </div>
                </div>
              </div>
              <div class="hidden border-t border-gray-700"
                   data-toggle-content-target="content"
                   data-index="<%= index %>">
                <div class="p-4 bg-gray-850">
                  <div class="prose prose-sm max-w-none text-gray-300">
                    <%= simple_format use_case.description %>
                  </div>
                </div>
              </div>
            </div>
          </div>
        <% end %>
      </div>
    <% else %>
      <div class="text-center py-6">
        <p class="text-xs text-gray-400">This partner has no use cases defined</p>
      </div>
    <% end %>
  </div>
</div>