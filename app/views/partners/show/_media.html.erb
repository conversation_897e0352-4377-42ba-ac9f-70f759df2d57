<% if @partner.partner_media.any? %>
  <div class="bg-gray-900 overflow-hidden rounded-xl border border-gray-800 shadow-sm">
    <div class="px-6 py-4 border-b border-gray-800">
      <div class="flex items-center justify-between">
        <h3 class="text-lg font-medium text-white flex items-center">
          <%= icon "photo", class: "mr-2 h-5 w-5 text-pink-400" %>
          Media
        </h3>
        <span class="inline-flex items-center rounded-full bg-pink-900/50 px-3 py-1 text-sm font-medium text-pink-300">
          <%= pluralize(@partner.partner_media.count, 'media file') %>
        </span>
      </div>
    </div>
    
    <div class="divide-y divide-gray-800">
      <% @partner.partner_media.each do |media| %>
        <div class="px-6 py-4 hover:bg-gray-800 transition-colors">
          <div class="flex items-start justify-between">
            <div class="flex items-start space-x-3 flex-1">
              <div class="flex-shrink-0 mt-1">
                <% case media.media_type %>
                <% when "video" %>
                  <%= icon "film", class: "h-5 w-5 text-red-400" %>
                <% when "image" %>
                  <%= icon "photo", class: "h-5 w-5 text-pink-400" %>
                <% when "audio" %>
                  <%= icon "musical-note", class: "h-5 w-5 text-purple-400" %>
                <% else %>
                  <%= icon "document", class: "h-5 w-5 text-gray-400" %>
                <% end %>
              </div>
              
              <div class="flex-1 min-w-0">
                <div class="flex items-center space-x-2 mb-1">
                  <h4 class="text-sm font-medium text-white truncate">
                    <%= media.name %>
                  </h4>
                  
                  <% if media.media_type.present? %>
                    <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-gray-800 text-gray-300 capitalize">
                      <%= media.media_type %>
                    </span>
                  <% end %>
                </div>
                
                <% if media.description.present? %>
                  <p class="text-xs text-gray-400 mb-2">
                    <%= truncate(media.description, length: 100) %>
                  </p>
                <% end %>
                
                <div class="flex items-center space-x-4 text-xs text-gray-400">
                  <% if media.file.attached? %>
                    <span class="flex items-center">
                      <%= icon "paper-clip", class: "mr-1 h-3 w-3" %>
                      <%= number_to_human_size(media.file.blob.byte_size) %>
                    </span>
                  <% elsif media.url.present? %>
                    <span class="flex items-center">
                      <%= icon "globe-alt", class: "mr-1 h-3 w-3" %>
                      External media
                    </span>
                  <% end %>
                  <span class="flex items-center">
                    <%= icon "calendar", class: "mr-1 h-3 w-3" %>
                    Added <%= time_ago_in_words(media.created_at) %> ago
                  </span>
                </div>
              </div>
            </div>
            
            <div class="flex items-center space-x-2 ml-4">
              <% if media.file.attached? %>
                <% if media.media_type == "image" && media.file.representable? %>
                  <%= link_to rails_blob_path(media.file, disposition: "inline"), 
                             target: "_blank",
                             class: "inline-flex items-center px-3 py-1.5 border border-gray-600 text-xs font-medium rounded-md text-gray-300 bg-gray-800 hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-primary-500",
                             title: "View media" do %>
                    <%= icon "eye", class: "mr-1.5 h-3.5 w-3.5" %>
                    View
                  <% end %>
                <% end %>
                
                <%= link_to rails_blob_path(media.file, disposition: "attachment"), 
                           class: "inline-flex items-center px-3 py-1.5 border border-pink-600 text-xs font-medium rounded-md text-pink-300 bg-pink-800 hover:bg-pink-700 focus:outline-none focus:ring-2 focus:ring-pink-500",
                           title: "Download media" do %>
                  <%= icon "arrow-down-tray", class: "mr-1.5 h-3.5 w-3.5" %>
                  Download
                <% end %>
              <% elsif media.url.present? %>
                <%= link_to safe_url(media.url), 
                           target: "_blank", 
                           class: "inline-flex items-center px-3 py-1.5 border border-pink-600 text-xs font-medium rounded-md text-pink-300 bg-pink-800 hover:bg-pink-700 focus:outline-none focus:ring-2 focus:ring-pink-500",
                           title: "View media" do %>
                  <%= icon "arrow-top-right-on-square", class: "mr-1.5 h-3.5 w-3.5" %>
                  View
                <% end %>
              <% end %>
            </div>
          </div>
        </div>
      <% end %>
    </div>
  </div>
<% else %>
  <div class="bg-gray-900 overflow-hidden rounded-xl border border-gray-800 shadow-sm">
    <div class="px-6 py-4 border-b border-gray-800">
      <h3 class="text-lg font-medium text-white flex items-center">
        <%= icon "photo", class: "mr-2 h-5 w-5 text-pink-400" %>
        Media
      </h3>
    </div>
    <div class="text-center py-12">
      <%= icon "photo", class: "h-12 w-12 text-gray-600 mb-4 mx-auto" %>
      <h3 class="font-medium text-white text-base mb-2">No media files</h3>
      <p class="text-sm text-gray-400 max-w-sm mx-auto">
        Partner media files, images, videos, and promotional materials will appear here once they're uploaded.
      </p>
    </div>
  </div>
<% end %>