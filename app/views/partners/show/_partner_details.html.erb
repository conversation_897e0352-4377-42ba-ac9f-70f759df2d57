<% if @partner.partner_detail.present? %>
  <div class="bg-white dark:bg-gray-900 overflow-hidden rounded-xl border border-gray-200 dark:border-gray-800 shadow-sm">
    <div class="px-4 py-3 border-b border-gray-200 dark:border-gray-800">
      <h3 class="text-base font-medium text-gray-900 dark:text-white flex items-center">
        <%= icon "clipboard-document-check", class: "mr-2 h-4 w-4" %>
        Partner Details
      </h3>
    </div>
    <div class="px-6 py-5">
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <!-- Core Details -->
        <div class="space-y-5">
          <h4 class="font-medium text-white text-sm pb-1 border-b border-gray-700">Business Information</h4>
          
          <% if @partner.partner_detail.company_size.present? %>
            <div>
              <dt class="text-xs font-medium text-gray-400 uppercase">Company Size</dt>
              <dd class="mt-1 text-sm text-white">
                <%= @partner.partner_detail.company_size %>
              </dd>
            </div>
          <% end %>

          <% if @partner.partner_detail.billing_type.present? %>
            <div>
              <dt class="text-xs font-medium text-gray-400 uppercase">Billing Type</dt>
              <dd class="mt-1 text-sm text-white">
                <%= @partner.partner_detail.billing_type %>
              </dd>
            </div>
          <% end %>

          <% if @partner.partner_detail.payment_term.present? %>
            <div>
              <dt class="text-xs font-medium text-gray-400 uppercase">Payment Terms</dt>
              <dd class="mt-1 text-sm text-white">
                <%= @partner.partner_detail.payment_term %>
              </dd>
            </div>
          <% end %>
          
          <% if @partner.partner_detail.minimum_budget.present? %>
            <div>
              <dt class="text-xs font-medium text-gray-400 uppercase">Minimum Budget</dt>
              <dd class="mt-1 text-sm text-white">
                <%= @partner.partner_detail.minimum_budget %>
              </dd>
            </div>
          <% end %>
          
          <% if @partner.partner_detail.poc_location.present? %>
            <div>
              <dt class="text-xs font-medium text-gray-400 uppercase">POC Location</dt>
              <dd class="mt-1 text-sm text-white">
                <%= @partner.partner_detail.poc_location %>
              </dd>
            </div>
          <% end %>
        </div>

        <!-- Technical Details -->
        <div class="space-y-5">
          <h4 class="font-medium text-white text-sm pb-1 border-b border-gray-700">Technical Capabilities</h4>
          
          <% if @partner.partner_detail.os_and_scale.present? %>
            <div>
              <dt class="text-xs font-medium text-gray-400 uppercase">OS & Scale</dt>
              <dd class="mt-1 text-sm text-white">
                <%= @partner.partner_detail.os_and_scale %>
              </dd>
            </div>
          <% end %>
          
          <% if @partner.partner_detail.requested_link_type.present? %>
            <div>
              <dt class="text-xs font-medium text-gray-400 uppercase">Requested Link Type</dt>
              <dd class="mt-1 text-sm text-white">
                <%= @partner.partner_detail.requested_link_type %>
              </dd>
            </div>
          <% end %>
          
          <% if @partner.partner_detail.reporting_timezone.present? %>
            <div>
              <dt class="text-xs font-medium text-gray-400 uppercase">Reporting Timezone</dt>
              <dd class="mt-1 text-sm text-white">
                <%= @partner.partner_detail.reporting_timezone %>
              </dd>
            </div>
          <% end %>
          
          <div class="grid grid-cols-2 gap-4">
            <div>
              <dt class="text-xs font-medium text-gray-400 uppercase">SKAN Compatible</dt>
              <dd class="mt-1 text-sm">
                <% if @partner.partner_detail.skan_compatible %>
                  <span class="inline-flex items-center rounded-full bg-green-900/50 px-2 py-0.5 text-xs font-medium text-green-300">
                    <%= icon "check-circle", class: "mr-1 h-3.5 w-3.5" %>Yes
                  </span>
                <% else %>
                  <span class="inline-flex items-center rounded-full bg-gray-800 px-2 py-0.5 text-xs font-medium text-gray-300">
                    <%= icon "x-circle", class: "mr-1 h-3.5 w-3.5" %>No
                  </span>
                <% end %>
              </dd>
            </div>
            
            <div>
              <dt class="text-xs font-medium text-gray-400 uppercase">Dashboard</dt>
              <dd class="mt-1 text-sm">
                <% if @partner.partner_detail.dashboard_availability %>
                  <span class="inline-flex items-center rounded-full bg-green-900/50 px-2 py-0.5 text-xs font-medium text-green-300">
                    <%= icon "check-circle", class: "mr-1 h-3.5 w-3.5" %>Available
                  </span>
                <% else %>
                  <span class="inline-flex items-center rounded-full bg-gray-800 px-2 py-0.5 text-xs font-medium text-gray-300">
                    <%= icon "x-circle", class: "mr-1 h-3.5 w-3.5" %>Unavailable
                  </span>
                <% end %>
              </dd>
            </div>
          </div>
        </div>

        <!-- Additional Details (Collapsible) -->
        <div class="md:col-span-2" data-controller="toggle-content">
          <div class="hidden space-y-5" data-toggle-content-target="content" data-index="additionalDetails">
            <h4 class="font-medium text-white text-sm pb-1 border-b border-gray-700">Additional Information</h4>
            
            <% if @partner.partner_detail.top_performing_geos.present? %>
              <div>
                <dt class="text-xs font-medium text-gray-400 uppercase">Top Performing Geos</dt>
                <dd class="mt-1 text-sm text-white">
                  <%= @partner.partner_detail.top_performing_geos %>
                </dd>
              </div>
            <% end %>
            
            <% if @partner.partner_detail.ownership_for_traffic.present? %>
              <div>
                <dt class="text-xs font-medium text-gray-400 uppercase">Traffic Ownership</dt>
                <dd class="mt-1 text-sm text-white">
                  <%= @partner.partner_detail.ownership_for_traffic %>
                </dd>
              </div>
            <% end %>
            
            <% if @partner.partner_detail.macro_mapping.present? %>
              <div>
                <dt class="text-xs font-medium text-gray-400 uppercase">Macro Mapping</dt>
                <dd class="mt-1 text-sm text-white">
                  <%= @partner.partner_detail.macro_mapping %>
                </dd>
              </div>
            <% end %>
            
            <% if @partner.partner_detail.add_on_service.present? %>
              <div>
                <dt class="text-xs font-medium text-gray-400 uppercase">Add-on Services</dt>
                <dd class="mt-1 text-sm text-white">
                  <%= @partner.partner_detail.add_on_service %>
                </dd>
              </div>
            <% end %>
            
            <% if @partner.partner_detail.net_spend_sources.present? %>
              <div>
                <dt class="text-xs font-medium text-gray-400 uppercase">Net Spend Sources</dt>
                <dd class="mt-1 text-sm text-white">
                  <%= @partner.partner_detail.net_spend_sources %>
                </dd>
              </div>
            <% end %>

            <% if @partner.partner_detail.creative_guideline.present? %>
              <div>
                <dt class="text-xs font-medium text-gray-400 uppercase">Creative Guidelines</dt>
                <dd class="mt-1 text-sm text-white">
                  <%= @partner.partner_detail.creative_guideline %>
                </dd>
              </div>
            <% end %>
            
            <!-- Long text fields in collapsible sections -->
            <% if @partner.partner_detail.media_description.present? %>
              <div class="pt-2">
                <dt class="text-xs font-medium text-gray-400 uppercase mb-2">Media Description</dt>
                <dd class="mt-1 text-sm text-white bg-gray-800 p-3 rounded-md">
                  <%= simple_format @partner.partner_detail.media_description %>
                </dd>
              </div>
            <% end %>
            
            <% if @partner.partner_detail.user_flow_description.present? %>
              <div class="pt-2">
                <dt class="text-xs font-medium text-gray-400 uppercase mb-2">User Flow Description</dt>
                <dd class="mt-1 text-sm text-white bg-gray-800 p-3 rounded-md">
                  <%= simple_format @partner.partner_detail.user_flow_description %>
                </dd>
              </div>
            <% end %>
            
            <% if @partner.partner_detail.benchmark.present? %>
              <div class="pt-2">
                <dt class="text-xs font-medium text-gray-400 uppercase mb-2">Benchmark</dt>
                <dd class="mt-1 text-sm text-white bg-gray-800 p-3 rounded-md">
                  <%= simple_format @partner.partner_detail.benchmark %>
                </dd>
              </div>
            <% end %>
            
            <% if @partner.partner_detail.targeting_capacity.present? %>
              <div class="pt-2">
                <dt class="text-xs font-medium text-gray-400 uppercase mb-2">Targeting Capacity</dt>
                <dd class="mt-1 text-sm text-white bg-gray-800 p-3 rounded-md">
                  <%= simple_format @partner.partner_detail.targeting_capacity %>
                </dd>
              </div>
            <% end %>
            
            <% if @partner.partner_detail.unique_selling_point.present? %>
              <div class="pt-2">
                <dt class="text-xs font-medium text-gray-400 uppercase mb-2">Unique Selling Point</dt>
                <dd class="mt-1 text-sm text-white bg-gray-800 p-3 rounded-md">
                  <%= simple_format @partner.partner_detail.unique_selling_point %>
                </dd>
              </div>
            <% end %>
            
            <% if @partner.partner_detail.incentive_program.present? %>
              <div class="pt-2">
                <dt class="text-xs font-medium text-gray-400 uppercase mb-2">Incentive Program</dt>
                <dd class="mt-1 text-sm text-white bg-gray-800 p-3 rounded-md">
                  <%= simple_format @partner.partner_detail.incentive_program %>
                </dd>
              </div>
            <% end %>
            
            <% if @partner.partner_detail.other_notes.present? %>
              <div class="pt-2">
                <dt class="text-xs font-medium text-gray-400 uppercase mb-2">Other Notes</dt>
                <dd class="mt-1 text-sm text-white bg-gray-800 p-3 rounded-md">
                  <%= simple_format @partner.partner_detail.other_notes %>
                </dd>
              </div>
            <% end %>
          </div>
          
          <button type="button" class="mt-4 text-sm text-primary-400 hover:text-primary-300 flex items-center" data-action="click->toggle-content#toggle" data-toggle-content-target="trigger" data-index="additionalDetails">
            <span data-toggle-content-target="buttonText" data-index="additionalDetails">Show more details</span>
            <svg xmlns="http://www.w3.org/2000/svg" class="ml-1 h-4 w-4 transition-transform" fill="none" viewBox="0 0 24 24" stroke="currentColor" data-toggle-content-target="icon" data-index="additionalDetails">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
            </svg>
          </button>
        </div>
      </div>
    </div>
  </div>
<% end %>