<!-- Main info card -->
<div class="bg-white dark:bg-gray-900 overflow-hidden rounded-xl border border-gray-200 dark:border-gray-800 shadow-sm">
  <div class="px-4 py-3 border-b border-gray-200 dark:border-gray-800 flex justify-between items-center">
    <div class="flex items-center gap-3">
      <% if @partner.logo.attached? %>
        <% bg_class = @partner.logo_bg_light? ? "bg-white" : "bg-gray-800" %>
        <%= image_tag @partner.logo.variant(format: :webp), 
                      class: "h-10 rounded-lg object-contain #{bg_class} px-2.5 py-2 border border-gray-300 dark:border-gray-700", 
                      alt: "#{@partner.name} logo" %>
      <% else %>
        <div class="flex-shrink-0 h-10 w-10 flex items-center justify-center rounded-lg bg-primary-900 text-primary-300 text-base font-medium">
          <%= @partner.name[0..1].upcase %>
        </div>
      <% end %>

      <div>
        <h3 class="text-base font-medium text-gray-900 dark:text-white"><%= @partner.name %></h3>
        <div class="flex items-center mt-1">
          <span class="text-xs text-gray-500 dark:text-gray-400 font-mono">ID: <%= @partner.id %></span>
          <% if @partner.is_test %>
            <span class="ml-2 inline-flex items-center rounded-full bg-purple-100 dark:bg-purple-900/50 px-2 py-0.5 text-xs font-medium text-purple-700 dark:text-purple-300">
              <%= icon "beaker", class: "mr-1 h-3 w-3" %>Test Partner
            </span>
          <% end %>
        </div>
      </div>
    </div>
    
    <% case @partner.status %>
    <% when "running" %>
      <span class="inline-flex items-center rounded-full bg-green-100 dark:bg-green-900/50 px-3 py-1 text-sm font-medium text-green-700 dark:text-green-300">
        <%= icon "play-circle", class: "mr-1.5 h-4 w-4" %>Running
      </span>
    <% when "onboarding" %>
      <span class="inline-flex items-center rounded-full bg-blue-100 dark:bg-blue-900/50 px-3 py-1 text-sm font-medium text-blue-700 dark:text-blue-300">
        <%= icon "clipboard-document-list", class: "mr-1.5 h-4 w-4" %>Onboarding
      </span>
    <% when "paused" %>
      <span class="inline-flex items-center rounded-full bg-yellow-100 dark:bg-yellow-900/50 px-3 py-1 text-sm font-medium text-yellow-700 dark:text-yellow-300">
        <%= icon "pause-circle", class: "mr-1.5 h-4 w-4" %>Paused
      </span>
    <% when "archived" %>
      <span class="inline-flex items-center rounded-full bg-gray-100 dark:bg-gray-700/50 px-3 py-1 text-sm font-medium text-gray-700 dark:text-gray-400">
        <%= icon "archive-box", class: "mr-1.5 h-4 w-4" %>Archived
      </span>
    <% end %>
  </div>
  
  <div class="px-6 py-5">
    <dl class="grid grid-cols-1 sm:grid-cols-2 gap-x-6 gap-y-5">
      <% if @partner.email.present? %>
        <div>
          <dt class="text-xs font-medium text-gray-400 uppercase tracking-wide flex items-center">
            <%= icon "envelope", class: "mr-1.5 h-3.5 w-3.5" %>Email
          </dt>
          <dd class="mt-1.5 text-sm text-white">
            <a href="mailto:<%= @partner.email %>" class="text-primary-400 hover:text-primary-300">
              <%= @partner.email %>
            </a>
          </dd>
        </div>
      <% end %>

      <% if @partner.website.present? %>
        <div>
          <dt class="text-xs font-medium text-gray-400 uppercase tracking-wide flex items-center">
            <%= icon "globe-alt", class: "mr-1.5 h-3.5 w-3.5" %>Website
          </dt>
          <dd class="mt-1.5 text-sm text-white flex items-center">
            <a href="<%= @partner.website %>" target="_blank" class="inline-flex items-center text-primary-400 hover:text-primary-300">
              <span class="truncate"><%= @partner.website.gsub(/^https?:\/\/(www\.)?/, '') %></span>
              <%= icon "arrow-top-right-on-square", class: "ml-1 h-3.5 w-3.5" %>
            </a>
            
            <!-- Sync button -->
            <%= button_to sync_from_website_partner_path(@partner), 
                        method: :post, 
                        class: "ml-3 inline-flex items-center rounded-md border border-gray-700 bg-gray-800 px-2.5 py-1 text-xs font-medium text-gray-300 shadow-sm hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2",
                        title: "Sync data from website",
                        data: { turbo_confirm: "This will attempt to extract partner data from the website. Continue?" } do %>
              <%= icon "arrow-path", class: "h-3.5 w-3.5 mr-1" %>
              Sync
            <% end %>
          </dd>
        </div>
      <% end %>

      <div>
        <dt class="text-xs font-medium text-gray-400 uppercase tracking-wide flex items-center">
          <%= icon "identification", class: "mr-1.5 h-3.5 w-3.5" %>Legacy ID
        </dt>
        <dd class="mt-1.5 text-sm text-white">
          <%= link_to legacy_url("vendors", @partner.legacy_id), target: "_blank", class: "inline-flex items-center gap-x-1 text-primary-400 hover:text-primary-300 font-mono" do %>
            <span><%= @partner.legacy_id %></span>
            <%= icon "arrow-top-right-on-square", class: "h-3.5 w-3.5" %>
          <% end %>
        </dd>
      </div>

      <div>
        <dt class="text-xs font-medium text-gray-400 uppercase tracking-wide flex items-center">
          <%= icon "calendar", class: "mr-1.5 h-3.5 w-3.5" %>Created
        </dt>
        <dd class="mt-1.5 text-sm text-white">
          <%= @partner.created_at.strftime("%B %d, %Y") %>
          <span class="ml-1.5 text-xs text-gray-400">(<%= time_ago_in_words @partner.created_at %> ago)</span>
        </dd>
      </div>

      <div>
        <dt class="text-xs font-medium text-gray-400 uppercase tracking-wide flex items-center">
          <%= icon "clock", class: "mr-1.5 h-3.5 w-3.5" %>Updated
        </dt>
        <dd class="mt-1.5 text-sm text-white">
          <%= @partner.updated_at.strftime("%B %d, %Y") %>
          <span class="ml-1.5 text-xs text-gray-400">(<%= time_ago_in_words @partner.updated_at %> ago)</span>
        </dd>
      </div>

      <% if @partner.partner_categories.any? %>
        <div class="sm:col-span-2">
          <dt class="text-xs font-medium text-gray-400 uppercase tracking-wide flex items-center">
            <%= icon "tag", class: "mr-1.5 h-3.5 w-3.5" %>Categories
            <% if @partner.main_category.present? %>
              <span class="ml-2 text-xs text-primary-400">(Main category highlighted)</span>
            <% end %>
          </dt>
          <dd class="mt-2">
            <div class="flex flex-wrap gap-2">
              <% @partner.partner_categories.each do |category| %>
                <% if category == @partner.main_category %>
                  <span class="inline-flex items-center rounded-full bg-primary-800 px-3 py-1 text-sm font-medium text-primary-300 border-2 border-primary-600 shadow-sm">
                    <%= icon "star", class: "mr-1.5 h-3.5 w-3.5" %>
                    <%= category.name %>
                    <span class="ml-1.5 text-xs opacity-75">(Main)</span>
                  </span>
                <% else %>
                  <span class="inline-flex items-center rounded-full bg-gray-800 px-3 py-1 text-sm font-medium text-gray-300">
                    <%= category.name %>
                  </span>
                <% end %>
              <% end %>
            </div>
          </dd>
        </div>
      <% end %>

      <% if @partner.partner_social_media_links.active.any? %>
        <div class="col-span-full pt-6 border-t border-gray-800">
          <dt class="text-xs font-medium text-gray-400 uppercase tracking-wide flex items-center mb-4">
            <%= icon "share", class: "mr-1.5 h-3.5 w-3.5" %>Social Media
          </dt>
          <div class="flex flex-wrap gap-3">
            <% @partner.partner_social_media_links.active.each do |social_link| %>
              <% platform_config = social_media_platform_config(social_link.platform) %>
              <a href="<%= social_link.url %>" 
                target="_blank" 
                class="inline-flex items-center gap-2 px-3 py-2 rounded-lg border border-gray-700 bg-gray-800 text-sm font-medium text-gray-300 hover:bg-gray-700 hover:border-gray-600 transition-colors"
                title="Visit <%= social_link.platform.humanize %> profile">
                <div class="flex-shrink-0 w-4 h-4 <%= platform_config[:color] %>">
                  <%= platform_config[:icon] %>
                </div>
                <span class="truncate max-w-[120px]"><%= social_link.platform.humanize %></span>
                <%= icon "arrow-top-right-on-square", class: "h-3.5 w-3.5 flex-shrink-0" %>
              </a>
            <% end %>
          </div>
        </div>
      <% end %>
    </dl>
  </div>
</div>

<!-- About card (simplified) -->
<% if @partner.headline.present? %>
  <div class="bg-gray-900 overflow-hidden rounded-xl border border-gray-800 shadow-sm">
    <div class="px-4 py-3 border-b border-gray-200 dark:border-gray-800">
      <h3 class="text-base font-medium text-gray-900 dark:text-white flex items-center">
        <%= icon "information-circle", class: "mr-2 h-4 w-4" %>
        About
      </h3>
    </div>
    <div class="px-6 py-5" data-controller="toggle-content">
      <div class="mb-3">
        <p class="text-base font-medium text-white leading-relaxed">
          <%= @partner.headline %>
        </p>
      </div>
      
      <% if @partner.description.present? %>
        <div class="hidden prose prose-sm max-w-none text-gray-300 pt-3 border-t border-gray-800 mt-2" data-toggle-content-target="content" data-index="description">
          <%= simple_format @partner.description %>
        </div>
        <button type="button" class="mt-1 text-sm text-primary-400 hover:text-primary-300 flex items-center" data-action="click->toggle-content#toggle" data-toggle-content-target="trigger" data-index="description">
          <span data-toggle-content-target="buttonText" data-index="description">Show full description</span>
          <svg xmlns="http://www.w3.org/2000/svg" class="ml-1 h-4 w-4 transition-transform" fill="none" viewBox="0 0 24 24" stroke="currentColor" data-toggle-content-target="icon" data-index="description">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
          </svg>
        </button>
      <% end %>
    </div>
  </div>
<% end %>