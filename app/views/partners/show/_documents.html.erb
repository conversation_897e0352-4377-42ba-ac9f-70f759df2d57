<div class="bg-white dark:bg-gray-900 overflow-hidden rounded-xl border border-gray-200 dark:border-gray-800 shadow-sm">
  <div class="px-4 py-3 border-b border-gray-200 dark:border-gray-800">
    <div class="flex items-center justify-between">
      <h3 class="text-base font-medium text-gray-900 dark:text-white flex items-center">
        <%= icon "document-duplicate", class: "mr-2 h-4 w-4" %>
        Documents
      </h3>
      <% if @partner.partner_documents.any? %>
        <span class="inline-flex items-center rounded-full bg-blue-100 dark:bg-blue-900/50 px-2 py-0.5 text-xs font-medium text-blue-700 dark:text-blue-300">
          <%= @partner.partner_documents.count %>
        </span>
      <% end %>
    </div>
  </div>
  <div class="px-2">
    <% if @partner.partner_documents.any? %>
      <div class="divide-y divide-gray-200 dark:divide-gray-800">
        <% @partner.partner_documents.each do |document| %>
          <div class="py-3 px-2 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
            <div class="flex items-start justify-between">
              <div class="flex items-start space-x-3 flex-1 min-w-0">
                <div class="flex-shrink-0 mt-1">
                  <% if document.category.present? %>
                    <% case document.category.downcase %>
                    <% when 'contract', 'agreement' %>
                      <%= icon "document-text", class: "h-5 w-5 text-red-400" %>
                    <% when 'media kit', 'presentation' %>
                      <%= icon "presentation-chart-bar", class: "h-5 w-5 text-purple-400" %>
                    <% when 'technical', 'integration' %>
                      <%= icon "cog-6-tooth", class: "h-5 w-5 text-green-400" %>
                    <% else %>
                      <%= icon "document-text", class: "h-5 w-5 text-blue-400" %>
                    <% end %>
                  <% else %>
                    <%= icon "document-text", class: "h-5 w-5 text-blue-400" %>
                  <% end %>
                </div>
                
                <div class="flex-1 min-w-0">
                  <div class="flex items-center space-x-2 mb-1">
                    <h4 class="text-sm font-medium text-gray-900 dark:text-white truncate flex-1 min-w-0">
                      <% if document.title.present? %>
                        <span title="<%= document.title %>"><%= document.title %></span>
                      <% elsif document.file.attached? %>
                        <span title="<%= document.file.filename %>"><%= document.file.filename %></span>
                      <% else %>
                        Untitled Document
                      <% end %>
                    </h4>
                    
                    <% if document.category.present? %>
                      <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 flex-shrink-0">
                        <%= document.category %>
                      </span>
                    <% end %>
                  </div>
                  
                  <div class="flex items-center space-x-4 text-xs text-gray-500 dark:text-gray-400">
                    <% if document.file.attached? %>
                      <span class="flex items-center flex-shrink-0">
                        <%= icon "paper-clip", class: "mr-1 h-3 w-3" %>
                        <%= number_to_human_size(document.file.blob.byte_size) %>
                      </span>
                      <span class="flex items-center flex-shrink-0">
                        <%= icon "calendar", class: "mr-1 h-3 w-3" %>
                        Uploaded <%= time_ago_in_words(document.created_at) %> ago
                      </span>
                    <% end %>
                  </div>
                </div>
              </div>
              
              <div class="flex items-center space-x-2 ml-4 flex-shrink-0">
                <% if document.file.attached? %>
                  <%= link_to rails_blob_path(document.file, disposition: "inline"), 
                             target: "_blank",
                             class: "inline-flex items-center px-3 py-1.5 border border-gray-300 dark:border-gray-600 text-xs font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-primary-500",
                             title: "View document" do %>
                    <%= icon "eye", class: "mr-1.5 h-3.5 w-3.5" %>
                    View
                  <% end %>
                  
                  <%= link_to rails_blob_path(document.file, disposition: "attachment"), 
                             class: "inline-flex items-center px-3 py-1.5 border border-primary-600 text-xs font-medium rounded-md text-primary-300 bg-primary-800 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500",
                             title: "Download document" do %>
                    <%= icon "arrow-down-tray", class: "mr-1.5 h-3.5 w-3.5" %>
                    Download
                  <% end %>
                <% end %>
              </div>
            </div>
          </div>
        <% end %>
      </div>
    <% else %>
      <div class="text-center py-6">
        <p class="text-xs text-gray-500 dark:text-gray-400">This partner has no documents uploaded</p>
      </div>
    <% end %>
  </div>
</div>