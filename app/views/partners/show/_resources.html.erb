<% if @partner.partner_resources.any? %>
  <div class="bg-gray-900 overflow-hidden rounded-xl border border-gray-800 shadow-sm">
    <div class="px-6 py-4 border-b border-gray-800">
      <div class="flex items-center justify-between">
        <h3 class="text-lg font-medium text-white flex items-center">
          <%= icon "link", class: "mr-2 h-5 w-5 text-teal-400" %>
          Resources
        </h3>
        <span class="inline-flex items-center rounded-full bg-teal-900/50 px-3 py-1 text-sm font-medium text-teal-300">
          <%= pluralize(@partner.partner_resources.count, 'resource') %>
        </span>
      </div>
    </div>
    
    <div class="divide-y divide-gray-800">
      <% @partner.partner_resources.each do |resource| %>
        <div class="px-6 py-4 hover:bg-gray-800 transition-colors">
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-3 flex-1">
              <div class="flex-shrink-0">
                <% if resource.file.attached? %>
                  <%= icon "document-arrow-down", class: "h-5 w-5 text-teal-400" %>
                <% else %>
                  <%= icon "link", class: "h-5 w-5 text-teal-400" %>
                <% end %>
              </div>
              
              <div class="flex-1 min-w-0">
                <h4 class="text-sm font-medium text-white truncate">
                  <%= resource.name %>
                </h4>
                <div class="flex items-center space-x-4 text-xs text-gray-400 mt-1">
                  <% if resource.file.attached? %>
                    <span class="flex items-center">
                      <%= icon "paper-clip", class: "mr-1 h-3 w-3" %>
                      File attachment
                    </span>
                  <% elsif resource.url.present? %>
                    <span class="flex items-center">
                      <%= icon "globe-alt", class: "mr-1 h-3 w-3" %>
                      External link
                    </span>
                  <% end %>
                  <span class="flex items-center">
                    <%= icon "calendar", class: "mr-1 h-3 w-3" %>
                    Added <%= time_ago_in_words(resource.created_at) %> ago
                  </span>
                </div>
              </div>
            </div>
            
            <div class="flex-shrink-0 ml-4">
              <% if resource.file.attached? %>
                <%= link_to rails_blob_path(resource.file, disposition: "attachment"), 
                           class: "inline-flex items-center px-3 py-1.5 border border-teal-600 text-xs font-medium rounded-md text-teal-300 bg-teal-800 hover:bg-teal-700 focus:outline-none focus:ring-2 focus:ring-teal-500",
                           title: "Download resource" do %>
                  <%= icon "arrow-down-tray", class: "mr-1.5 h-3.5 w-3.5" %>
                  Download
                <% end %>
              <% elsif resource.url.present? %>
                <%= link_to safe_url(resource.url), 
                           target: "_blank", 
                           class: "inline-flex items-center px-3 py-1.5 border border-teal-600 text-xs font-medium rounded-md text-teal-300 bg-teal-800 hover:bg-teal-700 focus:outline-none focus:ring-2 focus:ring-teal-500",
                           title: "Visit resource" do %>
                  <%= icon "arrow-top-right-on-square", class: "mr-1.5 h-3.5 w-3.5" %>
                  Visit
                <% end %>
              <% end %>
            </div>
          </div>
        </div>
      <% end %>
    </div>
  </div>
<% else %>
  <div class="bg-gray-900 overflow-hidden rounded-xl border border-gray-800 shadow-sm">
    <div class="px-6 py-4 border-b border-gray-800">
      <h3 class="text-lg font-medium text-white flex items-center">
        <%= icon "link", class: "mr-2 h-5 w-5 text-teal-400" %>
        Resources
      </h3>
    </div>
    <div class="text-center py-12">
      <%= icon "link", class: "h-12 w-12 text-gray-600 mb-4 mx-auto" %>
      <h3 class="font-medium text-white text-base mb-2">No resources available</h3>
      <p class="text-sm text-gray-400 max-w-sm mx-auto">
        Partner resources, links, and additional materials will appear here once they're added.
      </p>
    </div>
  </div>
<% end %>