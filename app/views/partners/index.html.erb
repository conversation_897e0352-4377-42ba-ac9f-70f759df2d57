<% content_for :title, "Partners" %>

<% if notice.present? %>
  <%= alerting :info, notice %>
<% end %>

<header class="border-b border-gray-800 bg-gray-900 shadow-sm sticky top-0 z-10">
  <div class="max-w-8xl px-4 h-14 flex items-center sm:px-6 2xl:px-12 mx-auto">
    <div class="flex items-center justify-between w-full">
      <div class="flex items-center space-x-2">
        <%= image_tag "feedmob-logo-full-white-1000px.png", class: "mx-auto h-7 w-auto", alt: "feedmob logo" %>
        <span class="inline-block border border-primary-700 rounded-md px-1.5 py-0.5 text-xs font-medium text-primary-300 tracking-wide">
          partners
        </span>
      </div>
    </div>
  </div>
</header>

<div class="mx-auto max-w-8xl px-4 sm:px-6 2xl:px-12 py-4">
  <!-- Search and filters card -->
  <div class="bg-gray-900 rounded-lg shadow-sm border border-gray-800 mb-4 p-3">
    <%= form_with model: @query, scope: :query, url: partners_path, method: :get, data: { controller: "auto-submit" } do |form| %>
      <div class="flex flex-wrap gap-3 items-center">
        <!-- Search input -->
        <div class="relative rounded-md shadow-sm flex-grow min-w-[200px] max-w-sm">
          <%= form.text_field :name_cont, 
            class: "block w-full rounded-md border-0 py-2 px-3 text-white ring-1 ring-inset ring-gray-700 placeholder:text-gray-500 focus:ring-2 focus:ring-inset focus:ring-primary-500 bg-gray-800 sm:text-sm sm:leading-6", 
            placeholder: "Search by partner name" %>
          <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3">
            <%= icon "magnifying-glass", class: "h-4 w-4 text-gray-500" %>
          </div>
        </div>

        <!-- Status filter -->
        <div class="flex items-center gap-2">
          <label for="query_status" class="text-sm font-medium text-gray-300 whitespace-nowrap">Status:</label>
          <%= form.select :status,
            options_for_select([
              ["All", ""],
              ["Running", "running"],
              ["Onboarding", "onboarding"],
              ["Paused", "paused"],
              ["Archived", "archived"]
            ], @query.status),
            {},
            class: "block rounded-md border-0 py-1.5 pl-3 pr-8 text-white ring-1 ring-inset ring-gray-700 focus:ring-2 focus:ring-primary-500 bg-gray-800 sm:text-sm sm:leading-6",
            data: { action: "change->auto-submit#submit" }
          %>
        </div>

        <!-- Category filter -->
        <div class="flex items-center gap-2">
          <label for="query_category_id" class="text-sm font-medium text-gray-300 whitespace-nowrap">Category:</label>
          <%= form.select :category_id,
            options_for_select(
              [["All Categories", ""]] + PartnerCategory.order(:name).map { |c| [c.name, c.id] },
              @query.category_id
            ),
            {},
            class: "block rounded-md border-0 py-1.5 pl-3 pr-8 text-white ring-1 ring-inset ring-gray-700 focus:ring-2 focus:ring-primary-500 bg-gray-800 sm:text-sm sm:leading-6",
            data: { action: "change->auto-submit#submit" }
          %>
        </div>

        <!-- Hide Test Partners -->
        <label class="flex items-center space-x-2 cursor-pointer">
          <%= form.check_box :hide_test_data,
              class: "h-4 w-4 rounded border-gray-600 text-primary-600 focus:ring-primary-400 bg-gray-700",
              data: { action: "change->auto-submit#submit" } %>
          <span class="text-sm font-medium text-gray-300 whitespace-nowrap">
            Hide Test Partners
          </span>
        </label>

        <div class="ml-auto text-xs text-gray-400">
          <% if @partners.any? %>
            <span class="font-medium text-gray-300"><%= @partners.count %></span> 
            <%= 'partner'.pluralize(@partners.count) %> found
          <% else %>
            No partners found
            <% if @query.status.present? || @query.name_cont.present? || @query.category_id.present? %>
              matching filters
            <% end %>
          <% end %>
        </div>
      </div>

      <%= form.submit "Filter", class: "hidden" %>
    <% end %>
  </div>

  <!-- Card Grid Layout -->
  <% if @partners.any? %>
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
      <% @partners.each do |partner| %>
        <%= link_to partner, class: "block" do %>
          <div class="bg-gray-900 rounded-lg shadow-sm border border-gray-700 overflow-hidden hover:shadow-md hover:border-gray-600 transition-all duration-200 h-full flex flex-col cursor-pointer">
            <div class="p-4 flex-1 flex flex-col">
              <!-- Header section -->
              <div class="flex items-center justify-between mb-4 min-h-[40px]">
                <div class="flex items-center space-x-3 flex-1 min-w-0 mr-4">
                  <% if partner.logo.attached? %>
                    <% bg_class = partner.logo_bg_light? ? "bg-white" : "bg-gray-800" %>
                    <div class="flex-shrink-0 h-10 flex items-center justify-center rounded-md overflow-hidden <%= bg_class %> p-1.5 border border-gray-800">
                      <%= image_tag partner.logo.variant(format: :webp), class: "max-h-8 w-auto max-w-20 object-contain" %>
                    </div>
                  <% else %>
                    <div class="flex-shrink-0 h-10 w-10 flex items-center justify-center rounded-md bg-primary-900 text-primary-300 text-base font-medium">
                      <%= partner.name[0..1].upcase %>
                    </div>
                  <% end %>

                  <div class="min-w-0 flex-1">
                    <h3 class="text-sm font-medium text-white truncate">
                      <%= partner.name %>
                    </h3>
                    <% if partner.is_test %>
                      <p class="text-xs text-gray-400">Test Partner</p>
                    <% end %>
                  </div>
                </div>
                
                <div class="flex-shrink-0">
                  <% case partner.status %>
                  <% when "running" %>
                    <span class="inline-flex items-center rounded-full bg-green-900/50 px-2 py-0.5 text-xs font-medium text-green-300 whitespace-nowrap">
                      <%= icon "play-circle", class: "mr-1 h-3 w-3" %>Running
                    </span>
                  <% when "onboarding" %>
                    <span class="inline-flex items-center rounded-full bg-blue-900/50 px-2 py-0.5 text-xs font-medium text-blue-300 whitespace-nowrap">
                      <%= icon "clipboard-document-list", class: "mr-1 h-3 w-3" %>Onboarding
                    </span>
                  <% when "paused" %>
                    <span class="inline-flex items-center rounded-full bg-yellow-900/50 px-2 py-0.5 text-xs font-medium text-yellow-300 whitespace-nowrap">
                      <%= icon "pause-circle", class: "mr-1 h-3 w-3" %>Paused
                    </span>
                  <% when "archived" %>
                    <span class="inline-flex items-center rounded-full bg-gray-700/50 px-2 py-0.5 text-xs font-medium text-gray-400 whitespace-nowrap">
                      <%= icon "archive-box", class: "mr-1 h-3 w-3" %>Archived
                    </span>
                  <% else %>
                    <span class="inline-flex items-center rounded-full bg-gray-700/50 px-2 py-0.5 text-xs font-medium text-gray-400 whitespace-nowrap">
                      <%= partner.status.capitalize %>
                    </span>
                  <% end %>
                </div>
              </div>
              
              <!-- Content section - grows to fill available space -->
              <div class="flex-1 flex flex-col space-y-4">
                <!-- Headline section -->
                <div class="h-10">
                  <% if partner.headline.present? %>
                    <p class="text-xs text-gray-400 line-clamp-2 leading-relaxed">
                      <%= partner.headline %>
                    </p>
                  <% end %>
                </div>
                
                <!-- Categories section -->
                <div class="flex-1">
                  <div class="flex flex-wrap gap-1 max-h-12 overflow-hidden">
                    <% if partner.partner_categories.any? %>
                      <% 
                        # Ensure main category is always included
                        categories_to_show = []
                        if partner.main_category
                          categories_to_show << partner.main_category
                          remaining_categories = partner.partner_categories.where.not(id: partner.main_category.id).take(2)
                          categories_to_show.concat(remaining_categories)
                        else
                          categories_to_show = partner.partner_categories.take(3)
                        end
                      %>
                      
                      <% categories_to_show.each do |category| %>
                        <% if category == partner.main_category %>
                          <span class="inline-flex items-center rounded-full bg-primary-800 px-2 py-0.5 text-xs font-medium text-primary-300 border border-primary-600 flex-shrink-0">
                            <%= icon "star", class: "mr-1 h-2.5 w-2.5" %>
                            <%= category.name %>
                          </span>
                        <% else %>
                          <span class="inline-flex items-center rounded-full bg-gray-800 px-2 py-0.5 text-xs font-medium text-gray-300 flex-shrink-0">
                            <%= category.name %>
                          </span>
                        <% end %>
                      <% end %>
                      
                      <% if partner.partner_categories.count > 3 %>
                        <span class="inline-flex items-center rounded-full bg-gray-800 px-2 py-0.5 text-xs font-medium text-gray-300 flex-shrink-0">
                          +<%= partner.partner_categories.count - 3 %>
                        </span>
                      <% end %>
                    <% else %>
                      <span class="text-xs text-gray-500">No categories</span>
                    <% end %>
                  </div>
                </div>
              </div>
              
              <!-- Footer section - always at bottom -->
              <div class="flex items-center justify-between text-xs text-gray-400 mt-4 pt-3 border-t border-gray-800">
                <div class="flex items-center space-x-2">
                  <div class="flex items-center">
                    <%= icon "fire", class: "h-3.5 w-3.5 mr-1 text-yellow-400" %>
                    <% active_campaigns_count = partner.click_urls.joins(:campaign).where(campaigns: { active: true }).distinct.count %>
                    <span><%= pluralize(active_campaigns_count, 'active campaign') %></span>
                  </div>
                  
                  <% if partner.partner_documents.any? || partner.partner_resources.any? %>
                    <span class="text-gray-700">•</span>
                    
                    <div class="flex items-center space-x-1">
                      <% if partner.partner_documents.any? %>
                        <span class="inline-flex items-center text-xs">
                          <%= icon "document", class: "h-3.5 w-3.5 mr-1 text-indigo-400" %>
                          <span class="font-medium"><%= partner.partner_documents.count %></span>
                        </span>
                      <% end %>
                      
                      <% if partner.partner_resources.any? %>
                        <span class="inline-flex items-center text-xs">
                          <%= icon "link", class: "h-3.5 w-3.5 mr-1 text-purple-400" %>
                          <span class="font-medium"><%= partner.partner_resources.count %></span>
                        </span>
                      <% end %>
                    </div>
                  <% end %>
                </div>
                
                <div class="flex items-center space-x-2">
                  <% if partner.website.present? %>
                    <span onclick="event.stopPropagation(); window.open('<%= partner.website %>', '_blank')" class="text-gray-400 hover:text-gray-300 transition-colors duration-200 p-1 rounded-md hover:bg-gray-800 cursor-pointer">
                      <%= icon "globe-alt", class: "h-4 w-4" %>
                      <span class="sr-only">Visit <%= partner.name %> website</span>
                    </span>
                  <% end %>
                </div>
              </div>
            </div>
          </div>
        <% end %>
      <% end %>
    </div>
  <% else %>
    <div class="bg-gray-900 rounded-lg shadow-sm border border-gray-800 p-8">
      <div class="flex flex-col items-center justify-center text-center">
        <%= icon "face-frown", class: "h-12 w-12 text-gray-600 mb-3" %>
        <h3 class="font-medium text-white text-base mb-1">No partners found</h3>
        <p class="text-sm text-gray-400">Try adjusting your search or filter criteria</p>
      </div>
    </div>
  <% end %>
</div>
