<% content_for :title, "Sub Budget: #{@sub_client_budget.name}" %>

<div class="w-full h-full flex flex-col bg-gray-50 dark:bg-gray-950">
  <!-- Header -->
  <header class="bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-800 shadow-sm">
    <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
      <div class="flex items-center justify-between py-6">
        <div class="flex items-center space-x-4">
          <div class="flex-shrink-0">
            <div class="h-10 w-10 rounded-lg bg-primary-100 dark:bg-primary-900 flex items-center justify-center">
              <%= icon "currency-dollar", class: "h-5 w-5 text-primary-600 dark:text-primary-400" %>
            </div>
          </div>
          <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
              <%= @sub_client_budget.name %>
            </h1>
            <p class="text-sm text-gray-500 dark:text-gray-400">
              Sub Budget for <%= link_to @sub_client_budget.client_budget.client.name,
                                         @sub_client_budget.client_budget.client,
                                         class: "text-primary-600 dark:text-primary-400 hover:underline" %>
            </p>
          </div>
        </div>

        <div class="flex items-center space-x-3">
          <%= link_to @sub_client_budget.client_budget.client,
                      class: "inline-flex items-center gap-x-2 rounded-full bg-primary-100 dark:bg-primary-900 px-4 py-2 text-sm font-medium text-primary-700 dark:text-primary-300 hover:bg-primary-200 dark:hover:bg-primary-800 transition-all duration-200 ease-in-out shadow-sm hover:shadow-md" do %>
            <%= icon "arrow-left", class: "h-4 w-4" %>
            <span>Back to Client</span>
          <% end %>
        </div>
      </div>
    </div>
  </header>

  <div class="grow overflow-y-auto">
    <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-6">
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Main Content -->
        <div class="lg:col-span-2 space-y-6">
          <%= render "sub_client_budgets/show/usage_summary" %>
          <%= render "sub_client_budgets/show/partner_breakdown" %>
          <%= render "sub_client_budgets/show/campaign_breakdown" %>
          <%= render "sub_client_budgets/show/country_breakdown" %>



          <!-- Associated Campaigns -->
          <div class="bg-white dark:bg-gray-900 overflow-hidden rounded-xl border border-gray-200 dark:border-gray-800 shadow-sm">
            <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-800">
              <div class="flex items-center justify-between">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white flex items-center">
                  <%= icon "megaphone", class: "mr-2 h-5 w-5 text-green-400" %>
                  Associated Campaigns
                  <% if @sub_client_budget.campaigns.any? %>
                    <span class="ml-2 inline-flex items-center rounded-full bg-green-100 dark:bg-green-900/50 px-2 py-0.5 text-xs font-medium text-green-700 dark:text-green-300">
                      <%= @sub_client_budget.campaigns.count %>
                    </span>
                  <% end %>
                </h3>
                <% if @sub_client_budget.campaigns.count > 5 %>
                   <button id="toggle-campaigns" class="inline-flex items-center px-3 py-1 text-xs font-medium text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 hover:bg-gray-50 dark:hover:bg-gray-800 rounded-md transition-all duration-200">
                     <span id="toggle-text">Show All</span>
                     <%= icon "chevron-down", class: "ml-1 h-3 w-3", id: "toggle-icon" %>
                   </button>
                 <% end %>
              </div>
            </div>
            <div class="px-2">
              <% if @sub_client_budget.campaigns.any? %>
                <% campaigns = @sub_client_budget.campaigns %>
                <% show_limit = 5 %>
                <ul role="list" class="divide-y divide-gray-200 dark:divide-gray-800">
                  <% campaigns.first(show_limit).each do |campaign| %>
                    <li class="py-4 px-4">
                      <div class="flex items-center justify-between">
                        <div class="min-w-0 flex-1">
                          <div class="text-sm font-medium text-gray-900 dark:text-white">
                            <%= link_to campaign.name, campaign, class: "hover:text-primary-600 dark:hover:text-primary-400" %>
                          </div>
                          <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                            Campaign ID: <%= campaign.legacy_id %>
                          </div>
                        </div>
                        <div class="flex-shrink-0">
                          <span class="inline-flex items-center rounded-full bg-gray-100 dark:bg-gray-800 px-2 py-0.5 text-xs font-medium text-gray-700 dark:text-gray-300">
                            Campaign
                          </span>
                        </div>
                      </div>
                    </li>
                  <% end %>
                  <% if campaigns.count > show_limit %>
                    <div id="additional-campaigns" class="hidden">
                      <% campaigns.drop(show_limit).each do |campaign| %>
                        <li class="py-4 px-4">
                          <div class="flex items-center justify-between">
                            <div class="min-w-0 flex-1">
                              <div class="text-sm font-medium text-gray-900 dark:text-white">
                                <%= link_to campaign.name, campaign, class: "hover:text-primary-600 dark:hover:text-primary-400" %>
                              </div>
                              <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                Campaign ID: <%= campaign.legacy_id %>
                              </div>
                            </div>
                            <div class="flex-shrink-0">
                              <span class="inline-flex items-center rounded-full bg-gray-100 dark:bg-gray-800 px-2 py-0.5 text-xs font-medium text-gray-700 dark:text-gray-300">
                                Campaign
                              </span>
                            </div>
                          </div>
                        </li>
                      <% end %>
                    </div>
                  <% end %>
                </ul>
              <% else %>
                <div class="text-center py-12">
                  <div class="text-gray-400">
                    <%= icon "megaphone", class: "h-12 w-12 mx-auto mb-4 opacity-60" %>
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No Campaigns Associated</h3>
                    <p class="text-sm text-gray-500 dark:text-gray-400">
                      This sub budget doesn't have any campaigns associated with it yet.
                    </p>
                  </div>
                </div>
              <% end %>
            </div>
          </div>

          <% if @sub_client_budget.campaigns.count > 5 %>
            <script>
              document.addEventListener('DOMContentLoaded', function() {
                const toggleButton = document.getElementById('toggle-campaigns');
                const additionalCampaigns = document.getElementById('additional-campaigns');
                const toggleText = document.getElementById('toggle-text');
                const toggleIcon = document.getElementById('toggle-icon');
                
                if (toggleButton && additionalCampaigns) {
                  toggleButton.addEventListener('click', function() {
                    const isHidden = additionalCampaigns.classList.contains('hidden');
                    
                    if (isHidden) {
                      additionalCampaigns.classList.remove('hidden');
                      toggleText.textContent = 'Show Less';
                      toggleIcon.style.transform = 'rotate(180deg)';
                    } else {
                      additionalCampaigns.classList.add('hidden');
                      toggleText.textContent = 'Show All';
                      toggleIcon.style.transform = 'rotate(0deg)';
                    }
                  });
                }
              });
            </script>
          <% end %>
        </div>

        <!-- Sidebar -->
        <div class="space-y-4">
          <!-- Quick Stats -->
          <div class="bg-white dark:bg-gray-900 overflow-hidden rounded-xl border border-gray-200 dark:border-gray-800 shadow-sm">
            <div class="px-4 py-3 border-b border-gray-200 dark:border-gray-800">
              <h3 class="text-base font-medium text-gray-900 dark:text-white flex items-center">
                <%= icon "chart-bar", class: "mr-2 h-4 w-4" %>
                Quick Stats
              </h3>
            </div>
            <div class="px-4 py-4">
              <dl class="space-y-4">
                <div>
                  <dt class="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">
                    Budget Amount
                  </dt>
                  <dd class="mt-1 text-lg font-semibold text-gray-900 dark:text-white">
                    $<%= number_with_delimiter(@sub_client_budget.amount.to_i) %>
                  </dd>
                </div>
                <div>
                  <dt class="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">
                    Campaign Count
                  </dt>
                  <dd class="mt-1 text-lg font-semibold text-gray-900 dark:text-white">
                    <%= @sub_client_budget.campaigns.count %>
                  </dd>
                </div>
                <div>
                  <dt class="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">
                    Created
                  </dt>
                  <dd class="mt-1 text-sm text-gray-900 dark:text-white">
                    <%= @sub_client_budget.created_at.strftime("%b %d, %Y") %>
                  </dd>
                </div>
              </dl>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
