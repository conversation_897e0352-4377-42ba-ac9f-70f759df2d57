<%# Sub Budget Usage Summary Component %>
<% if @usage_summary.present? %>
  <div class="bg-white dark:bg-gray-900 overflow-hidden rounded-xl border border-gray-200 dark:border-gray-800 shadow-sm">
    <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-800">
      <h3 class="text-lg font-medium text-gray-900 dark:text-white flex items-center">
        <%= icon "chart-pie", class: "mr-2 h-5 w-5 text-primary-400" %>
        Budget Usage Summary
        <span class="ml-2 text-sm font-normal text-gray-500 dark:text-gray-400">
          (Last 6 months)
        </span>
      </h3>
    </div>
    
    <div class="px-6 py-5">
      <!-- Budget Overview Stats -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <div class="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-4">
          <div class="text-sm font-medium text-gray-500 dark:text-gray-400">Budget Amount</div>
          <div class="text-2xl font-bold text-gray-900 dark:text-white">
            $<%= number_with_delimiter(@usage_summary[:budget_amount].to_i) %>
          </div>
        </div>
        
        <div class="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-4">
          <div class="text-sm font-medium text-gray-500 dark:text-gray-400">Total Spent</div>
          <div class="text-2xl font-bold text-gray-900 dark:text-white">
            $<%= number_with_delimiter(@usage_summary[:total_spent].to_i) %>
          </div>
        </div>
        
        <div class="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-4">
          <div class="text-sm font-medium text-gray-500 dark:text-gray-400">Usage Rate</div>
          <div class="text-2xl font-bold <%= @usage_summary[:usage_percentage] > 90 ? 'text-red-600 dark:text-red-400' : @usage_summary[:usage_percentage] > 75 ? 'text-yellow-600 dark:text-yellow-400' : 'text-green-600 dark:text-green-400' %>">
            <%= @usage_summary[:usage_percentage] %>%
          </div>
        </div>
      </div>

      <!-- Progress Bar -->
      <div class="mb-6">
        <div class="flex justify-between text-sm text-gray-600 dark:text-gray-400 mb-2">
          <span>Budget Utilization</span>
          <span><%= @usage_summary[:usage_percentage] %>% used</span>
        </div>
        <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3">
          <div class="<%= @usage_summary[:usage_percentage] > 90 ? 'bg-red-500' : @usage_summary[:usage_percentage] > 75 ? 'bg-yellow-500' : 'bg-green-500' %> h-3 rounded-full transition-all duration-300" 
               style="width: <%= [@usage_summary[:usage_percentage], 100].min %>%"></div>
        </div>
        <div class="flex justify-between text-xs text-gray-500 dark:text-gray-400 mt-1">
          <span>$0</span>
          <span>$<%= number_with_delimiter(@usage_summary[:budget_amount].to_i) %></span>
        </div>
      </div>

      <!-- Monthly Trend Chart -->
      <% if @trend_data.present? %>
        <div class="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-4 mb-6">
          <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-3">Monthly Spend Trend</h4>
          <%= line_chart @trend_data,
              prefix: "$",
              thousands: ",",
              height: "250px",
              colors: ["#8B5CF6"],
              curve: false,
              library: {
                plugins: {
                  legend: {
                    display: false
                  }
                },
                scales: {
                  x: {
                    grid: {
                      display: false
                    },
                    ticks: {
                      maxRotation: 0,
                      minRotation: 0
                    }
                  },
                  y: {
                    beginAtZero: true,
                    grid: {
                      color: 'rgba(156, 163, 175, 0.1)'
                    }
                  }
                }
              } %>
        </div>
      <% end %>

      <!-- Additional Info -->
      <div class="text-xs text-gray-500 dark:text-gray-400">
        <div class="flex justify-between">
          <span>Period: <%= @usage_summary[:period_start].strftime("%b %d, %Y") %> - <%= @usage_summary[:period_end].strftime("%b %d, %Y") %></span>
          <span>Associated Campaigns: <%= @usage_summary[:campaigns_count] %></span>
        </div>
      </div>
    </div>
  </div>
<% else %>
  <!-- No Usage Data Available -->
  <div class="bg-white dark:bg-gray-900 overflow-hidden rounded-xl border-2 border-dashed border-gray-200 dark:border-gray-700">
    <div class="px-6 py-12 text-center">
      <div class="text-gray-400">
        <%= icon "chart-pie", class: "h-12 w-12 mx-auto mb-4 opacity-60" %>
        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No Usage Data</h3>
        <p class="text-sm mb-6">No spend data available for this sub budget.</p>
      </div>
    </div>
  </div>
<% end %>
