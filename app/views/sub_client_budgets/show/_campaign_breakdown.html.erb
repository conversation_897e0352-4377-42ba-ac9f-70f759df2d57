<% # Campaign Breakdown Component %>
<% if @campaign_breakdown.present? && @campaign_breakdown.any? %>
  <div class="bg-white dark:bg-gray-900 overflow-hidden rounded-xl border border-gray-200 dark:border-gray-800 shadow-sm">
    <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-800">
      <h3 class="text-lg font-medium text-gray-900 dark:text-white flex items-center">
        <%= icon "chart-bar", class: "mr-2 h-5 w-5 text-primary-400" %>
        Campaign Spend Breakdown
        <span class="ml-2 text-sm font-normal text-gray-500 dark:text-gray-400">
          (<%= @sub_client_budget.client_budget.start_date.strftime("%b %d, %Y") %> - <%= @sub_client_budget.client_budget.end_date.strftime("%b %d, %Y") %>)
        </span>
      </h3>
    </div>

    <div class="px-6 py-5">
      <!-- Pie Chart for Campaign Distribution -->
      <div class="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-4 mb-6">
        <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-3">Spend Distribution</h4>
        <% pie_data = @campaign_breakdown.map { |item| [item[:campaign_name], item[:spend_amount]] } %>
        <%= pie_chart pie_data,
                      prefix: "$",
                      thousands: ",",
                      height: "300px",
                      colors: [
                        "#3B82F6", "#10B981", "#F59E0B", "#EF4444", "#8B5CF6",
                        "#EC4899", "#14B8A6", "#F43F5E", "#6366F1", "#84CC16",
                      ],
                      library: {
                        plugins: {
                          legend: {
                            position: "bottom",
                            labels: {
                              usePointStyle: true,
                              padding: 15,
                              boxWidth: 12,
                            },
                          },
                        },
                      } %>
      </div>

      <!-- Detailed Campaign List -->
      <div class="space-y-3">
        <h4 class="text-sm font-medium text-gray-900 dark:text-white">Campaign Details</h4>
        <% @campaign_breakdown.each_with_index do |campaign, index| %>
          <div class="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
            <div class="flex items-center justify-between mb-2">
              <div class="flex items-center">
                <div class="w-3 h-3 rounded-full mr-3"
                     style="background-color: <%= ["#3B82F6", "#10B981", "#F59E0B", "#EF4444", "#8B5CF6", "#EC4899", "#14B8A6", "#F43F5E", "#6366F1", "#84CC16"][index % 10] %>"></div>
                <h5 class="text-sm font-medium text-gray-900 dark:text-white truncate">
                  <%= campaign[:campaign_name] %>
                </h5>
              </div>
              <div class="text-right">
                <div class="text-sm font-semibold text-gray-900 dark:text-white">
                  $<%= number_with_delimiter(campaign[:spend_amount].round(2)) %>
                </div>
                <div class="text-xs text-gray-500 dark:text-gray-400">
                  <%= campaign[:percentage] %>% of total
                </div>
              </div>
            </div>

            <!-- Progress bar for this campaign -->
            <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mb-2">
              <div class="h-2 rounded-full transition-all duration-300"
                   style="width: <%= campaign[:percentage] %>%; background-color: <%= ["#3B82F6", "#10B981", "#F59E0B", "#EF4444", "#8B5CF6", "#EC4899", "#14B8A6", "#F43F5E", "#6366F1", "#84CC16"][index % 10] %>"></div>
            </div>

            <!-- Budget utilization for this campaign -->
            <div class="flex justify-between text-xs text-gray-500 dark:text-gray-400">
              <span>Budget Utilization: <%= campaign[:budget_utilization] %>%</span>
              <span>Campaign ID: <%= campaign[:campaign_id] %></span>
            </div>
          </div>
        <% end %>
      </div>

      <!-- Summary Stats -->
      <div class="mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
        <div class="grid grid-cols-2 gap-4 text-sm">
          <div>
            <span class="text-gray-500 dark:text-gray-400">Total Campaigns:</span>
            <span class="font-medium text-gray-900 dark:text-white ml-2"><%= @campaign_breakdown.length %></span>
          </div>
          <div>
            <span class="text-gray-500 dark:text-gray-400">Total Spend:</span>
            <span class="font-medium text-gray-900 dark:text-white ml-2">
              $<%= number_with_delimiter(@campaign_breakdown.sum { |c| c[:spend_amount] }.round(2)) %>
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
<% else %>
  <!-- No Campaign Data Available -->
  <div class="bg-white dark:bg-gray-900 overflow-hidden rounded-xl border-2 border-dashed border-gray-200 dark:border-gray-700">
    <div class="px-6 py-12 text-center">
      <div class="text-gray-400">
        <%= icon "chart-bar", class: "h-12 w-12 mx-auto mb-4 opacity-60" %>
        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No Campaign Data</h3>
        <p class="text-sm mb-6">No campaigns are associated with this sub budget or no spend data is available.</p>
      </div>
    </div>
  </div>
<% end %>
