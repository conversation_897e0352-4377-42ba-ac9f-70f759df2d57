<div class="bg-white dark:bg-gray-900 overflow-hidden rounded-xl border border-gray-200 dark:border-gray-800 shadow-sm">
  <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-800">
    <h3 class="text-lg font-medium text-gray-900 dark:text-white flex items-center">
      <%= icon "globe-alt", class: "mr-2 h-5 w-5 text-blue-400" %>
      Country Spend Breakdown
      <% if @country_breakdown.any? %>
        <span class="ml-2 inline-flex items-center rounded-full bg-blue-100 dark:bg-blue-900/50 px-2 py-0.5 text-xs font-medium text-blue-700 dark:text-blue-300">
          <%= @country_breakdown.count %>
        </span>
      <% end %>
    </h3>
  </div>
  <div class="px-6 py-5">
    <% if @country_breakdown.any? %>
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Pie Chart -->
        <div class="flex justify-center items-center">
          <div class="relative">
            <canvas id="countryBreakdownChart" width="300" height="300" class="max-w-full cursor-pointer"></canvas>
            <div id="chartTooltip" class="absolute bg-gray-900 text-white text-xs rounded py-1 px-2 pointer-events-none opacity-0 transition-opacity duration-200 z-10">
              <div id="tooltipContent"></div>
            </div>
          </div>
        </div>

        <!-- Country List -->
        <div class="space-y-4">
          <% @country_breakdown.each_with_index do |country, index| %>
            <% colors = ["#3B82F6", "#10B981", "#F59E0B", "#EF4444", "#8B5CF6", "#06B6D4", "#84CC16", "#F97316"] %>
            <% color = colors[index % colors.length] %>
            <div class="flex items-center justify-between py-3 border-b border-gray-100 dark:border-gray-800 last:border-b-0">
              <div class="flex-1 min-w-0">
                <div class="flex items-center space-x-3">
                  <div class="flex-shrink-0">
                    <div class="h-4 w-4 rounded-full" style="background-color: <%= color %>"></div>
                  </div>
                  <div class="flex-1 min-w-0">
                    <p class="text-sm font-medium text-gray-900 dark:text-white truncate">
                      <%= country[:country_code] %>
                    </p>
                    <div class="flex items-center space-x-2 text-xs text-gray-500 dark:text-gray-400">
                      <span><%= pluralize(country[:campaigns_count], "campaign") %></span>
                      <span>•</span>
                      <span><%= country[:budget_utilization] %>% of budget</span>
                    </div>
                  </div>
                </div>
              </div>
              <div class="flex-shrink-0 text-right">
                 <div class="text-sm font-semibold text-gray-900 dark:text-white">
                   $<%= number_with_delimiter(country[:spend_amount].round(2)) %>
                 </div>
                 <div class="text-xs text-gray-500 dark:text-gray-400">
                   <%= country[:percentage] %>%
                 </div>
               </div>
            </div>
          <% end %>
        </div>
      </div>

      <script>
        document.addEventListener('DOMContentLoaded', function() {
          const canvas = document.getElementById('countryBreakdownChart');
          const tooltip = document.getElementById('chartTooltip');
          const tooltipContent = document.getElementById('tooltipContent');
          if (!canvas || !tooltip || !tooltipContent) return;

          const ctx = canvas.getContext('2d');
          const data = JSON.parse('<%= raw @country_breakdown.to_json %>');
          const colors = ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6', '#06B6D4', '#84CC16', '#F97316'];

          // Store slice information for tooltip
          const slices = [];

          let currentAngle = -Math.PI / 2; // Start from top
          const centerX = canvas.width / 2;
          const centerY = canvas.height / 2;
          const radius = Math.min(centerX, centerY) - 20;

          // Clear canvas
          ctx.clearRect(0, 0, canvas.width, canvas.height);

          // Draw pie slices and store slice data
          data.forEach((item, index) => {
            const sliceAngle = (parseFloat(item.percentage) / 100) * 2 * Math.PI;
            const color = colors[index % colors.length];

            // Store slice information
            slices.push({
              startAngle: currentAngle,
              endAngle: currentAngle + sliceAngle,
              color: color,
              data: item
            });

            // Draw slice
            ctx.beginPath();
            ctx.moveTo(centerX, centerY);
            ctx.arc(centerX, centerY, radius, currentAngle, currentAngle + sliceAngle);
            ctx.closePath();
            ctx.fillStyle = color;
            ctx.fill();

            // Draw border
            ctx.strokeStyle = '#ffffff';
            ctx.lineWidth = 2;
            ctx.stroke();

            currentAngle += sliceAngle;
          });

          // Mouse move event for tooltip
          canvas.addEventListener('mousemove', function(e) {
            const rect = canvas.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;

            // Convert to canvas coordinates
            const canvasX = x * (canvas.width / rect.width);
            const canvasY = y * (canvas.height / rect.height);

            // Calculate distance from center
            const dx = canvasX - centerX;
            const dy = canvasY - centerY;
            const distance = Math.sqrt(dx * dx + dy * dy);

            if (distance <= radius) {
              // Calculate angle
              let angle = Math.atan2(dy, dx);
              // Normalize angle to match our pie chart (starting from top)
              angle = angle + Math.PI / 2;
              if (angle < 0) angle += 2 * Math.PI;
              if (angle >= 2 * Math.PI) angle -= 2 * Math.PI;

              // Find which slice the mouse is over
              let hoveredSlice = null;
              for (let slice of slices) {
                let startAngle = slice.startAngle + Math.PI / 2;
                let endAngle = slice.endAngle + Math.PI / 2;

                // Normalize angles
                if (startAngle < 0) startAngle += 2 * Math.PI;
                if (endAngle < 0) endAngle += 2 * Math.PI;
                if (startAngle >= 2 * Math.PI) startAngle -= 2 * Math.PI;
                if (endAngle >= 2 * Math.PI) endAngle -= 2 * Math.PI;

                // Handle angle wrapping
                if (startAngle > endAngle) {
                  if (angle >= startAngle || angle <= endAngle) {
                    hoveredSlice = slice;
                    break;
                  }
                } else {
                  if (angle >= startAngle && angle <= endAngle) {
                    hoveredSlice = slice;
                    break;
                  }
                }
              }

              if (hoveredSlice) {
                // Show tooltip
                const item = hoveredSlice.data;
                tooltipContent.innerHTML = `
                   <div class="font-semibold">${item.country_code}</div>
                   <div>$${parseFloat(item.spend_amount).toLocaleString('en-US', {minimumFractionDigits: 2, maximumFractionDigits: 2})}</div>
                   <div>${item.percentage}% of total</div>
                   <div>${item.campaigns_count} campaigns</div>
                 `;

                tooltip.style.left = (e.clientX - rect.left + 10) + 'px';
                tooltip.style.top = (e.clientY - rect.top - 10) + 'px';
                tooltip.classList.remove('opacity-0');
                tooltip.classList.add('opacity-100');

                canvas.style.cursor = 'pointer';
              } else {
                tooltip.classList.remove('opacity-100');
                tooltip.classList.add('opacity-0');
                canvas.style.cursor = 'default';
              }
            } else {
              tooltip.classList.remove('opacity-100');
              tooltip.classList.add('opacity-0');
              canvas.style.cursor = 'default';
            }
          });

          // Hide tooltip when mouse leaves canvas
          canvas.addEventListener('mouseleave', function() {
            tooltip.classList.remove('opacity-100');
            tooltip.classList.add('opacity-0');
            canvas.style.cursor = 'default';
          });
        });
      </script>
    <% else %>
      <div class="text-center py-12">
        <div class="text-gray-400">
          <%= icon "globe-alt", class: "h-12 w-12 mx-auto mb-4 opacity-60" %>
          <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No Country Data Available</h3>
          <p class="text-sm text-gray-500 dark:text-gray-400">
            Country breakdown will appear here when spend data is available.
          </p>
        </div>
      </div>
    <% end %>
  </div>
</div>