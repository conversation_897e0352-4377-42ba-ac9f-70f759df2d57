<div data-controller="dropdown" class="relative">
  <button type="button"
          data-action="dropdown#toggle"
          class="inline-flex items-center px-3 py-2 text-sm font-medium text-emerald-300 hover:text-emerald-100 bg-emerald-800/30 rounded-md hover:bg-emerald-800/50 transition-colors duration-150">
    Export Aggregated
    <%= icon "arrow-down-tray", set: "mini", class: "-mr-1 ml-1.5 h-5 w-5 text-emerald-500" %>
  </button>

  <div data-dropdown-target="menu"
      class="hidden absolute right-0 z-50 mt-2 w-72 origin-top-right rounded-md bg-gray-800 p-2 shadow-lg ring-1 ring-gray-700">
    <%= form_with scope: :query, url: export_aggregated_campaign_spends_path(format: :csv), method: :get do |export_form| %>
      <% @query.attributes.each do |attr, value| %>
        <%= render partial: 'shared/export_hidden_fields',
                  locals: { form: export_form, attr: attr, value: value } %>
      <% end %>

      <div class="py-1 justify-center">
        <label for="export_type" class="block text-sm font-medium text-gray-200">Select Summary Type</label>
        <select id="export_type" 
                name="query[export_type]" 
                class="mt-1 block w-full pl-3 pr-8 py-2 text-base bg-gray-700 text-gray-100 border border-gray-600 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md">
          <% summary_options = [
            { type: 'client', label: 'By client' },
            { type: 'partner', label: 'By partner' },
            { type: 'campaign', label: 'By campaign' }
          ] %>
          <% summary_options.each do |option| %>
            <option value="<%= option[:type] %>"><%= option[:label] %></option>
          <% end %>
        </select>
      </div>

      <div class="mt-2 py-1">
        <button type="submit"
                class="w-full px-4 py-2 text-sm font-medium text-white bg-primary-600 rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500">
          Download CSV
        </button>
      </div>
    <% end %>
  </div>
</div>