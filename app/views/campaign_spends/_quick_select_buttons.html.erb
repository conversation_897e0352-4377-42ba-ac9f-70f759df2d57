<div class="grid grid-cols-2 gap-2 pt-2">
  <button type="button"
          data-action="click->date-range#setYesterday"
          class="px-3 py-2 text-xs font-medium text-gray-200 bg-gray-700 rounded-md hover:bg-gray-600 transition-colors">
    Yesterday
  </button>
  <button type="button"
          data-action="click->date-range#setLast7Days"
          class="px-3 py-2 text-xs font-medium text-gray-200 bg-gray-700 rounded-md hover:bg-gray-600 transition-colors">
    Last 7 Days
  </button>
  <button type="button"
          data-action="click->date-range#setCurrentMonth"
          class="px-3 py-2 text-xs font-medium text-gray-200 bg-gray-700 rounded-md hover:bg-gray-600 transition-colors">
    Current Month
  </button>
  <button type="button"
          data-action="click->date-range#setLastMonth"
          class="px-3 py-2 text-xs font-medium text-gray-200 bg-gray-700 rounded-md hover:bg-gray-600 transition-colors">
    Last Month
  </button>
</div>

<!-- Add Apply button -->
<div class="pt-3 border-t border-gray-700">
  <button type="button"
          data-action="click->date-range#syncToMainForm"
          class="w-full px-4 py-2 text-sm font-medium text-white bg-primary-600 rounded-md hover:bg-primary-700 transition-colors">
    Apply
  </button>
</div>