<% content_for :title, "What's New - FeedMob Assistant" %>

<div class="max-w-4xl mx-auto px-4 py-8 sm:px-6 lg:px-8">
  <!-- Page Header -->
  <div class="mb-8">
    <div class="flex items-center space-x-4 mb-4">
      <%= image_tag "feedmob-logo-full-white-1000px.png", class: "h-8 w-auto", alt: "FeedMob logo" %>
      <div class="h-6 border-r border-gray-700"></div>
      <h1 class="text-3xl font-bold text-white">What's New</h1>
    </div>
    <p class="text-gray-400">Progress reports from the trenches. No fluff, just what we've shipped.</p>
  </div>

  <!-- Updates Timeline -->
  <div class="space-y-8">
    <!-- Tab-Based Client & Partner Interface -->
    <div class="bg-gray-800 rounded-lg border border-gray-700 p-6">
      <div class="flex items-center justify-between mb-4">
        <div class="flex items-center space-x-3">
          <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-emerald-100 text-emerald-800 dark:bg-emerald-800 dark:text-emerald-100">
            UX Overhaul
          </span>
          <h2 class="text-xl font-semibold text-white">Tab-Based Client & Partner Interface</h2>
        </div>
        <time class="text-sm text-gray-400">July 2, 2025</time>
      </div>
      <p class="text-gray-300 mb-4">
        Client and partner detail pages now use a <span class="text-emerald-400 font-semibold">clean tab-based interface</span>. 
        No more scrolling through endless sections - everything is organized into logical tabs for faster navigation and less visual clutter.
      </p>
      
      <div class="mt-4 p-3 bg-emerald-900/20 border border-emerald-700 rounded-lg">
        <p class="text-emerald-300 text-sm">
          <strong>Much Cleaner:</strong> Information is now grouped logically, making it easier to find what you need without overwhelming single-page layouts.
        </p>
      </div>
    </div>

    <!-- Campaign Spend Sorting Enhancement -->
    <div class="bg-gray-800 rounded-lg border border-gray-700 p-6">
      <div class="flex items-center justify-between mb-4">
        <div class="flex items-center space-x-3">
          <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800 dark:bg-indigo-800 dark:text-indigo-100">
            Enhancement
          </span>
          <h2 class="text-xl font-semibold text-white">
            <%= link_to "Campaign Spends Sorting", campaign_spends_path(query: { sort_by: "revenue_desc" }), class: "hover:text-indigo-400 transition-colors" %>
          </h2>
        </div>
        <time class="text-sm text-gray-400">June 23, 2025</time>
      </div>
      <p class="text-gray-300">
        You can now <span class="text-indigo-400 font-semibold">sort campaign spends by revenue</span> in your femini views. 
        See your highest-performing campaigns at the top where they belong.
      </p>
    </div>

    <!-- Margin Display Improvement -->
    <div class="bg-gray-800 rounded-lg border border-gray-700 p-6">
      <div class="flex items-center justify-between mb-4">
        <div class="flex items-center space-x-3">
          <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-teal-100 text-teal-800 dark:bg-teal-800 dark:text-teal-100">
            UX Fix
          </span>
          <h2 class="text-xl font-semibold text-white">Improved Margin Display</h2>
        </div>
        <time class="text-sm text-gray-400">June 23, 2025</time>
      </div>
      <p class="text-gray-300">
        Margin values in workspaces now display as <span class="text-teal-400 font-semibold">percentages instead of decimals</span>. 
        No more squinting at "0.23" when you want to see "23".
      </p>
    </div>

    <!-- Media Plan Context Integration -->
    <div class="bg-gray-800 rounded-lg border border-gray-700 p-6">
      <div class="flex items-center justify-between mb-4">
        <div class="flex items-center space-x-3">
          <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100">
            New Feature
          </span>
          <h2 class="text-xl font-semibold text-white">
            <%= link_to "Media Plan Context Integration", new_conversation_path, class: "hover:text-green-400 transition-colors" %>
          </h2>
        </div>
        <time class="text-sm text-gray-400">June 18, 2025</time>
      </div>
      <p class="text-gray-300 mb-4">
        The AI Assistant can now understand and reference <span class="text-green-400 font-semibold">media plan context</span> during conversations. 
        Ask questions about campaign strategies, budget allocations, and performance targets with the assistant having full context of your media plans.
      </p>
      
      <!-- First Supported Client -->
      <div class="bg-gray-900/50 rounded-lg border border-gray-600 p-4 mb-4">
        <h3 class="text-lg font-semibold text-white mb-3 flex items-center">
          <%= icon "file-text", library: "lucide", class: "h-5 w-5 text-green-400 mr-2" %>
          First Supported Client
        </h3>
        <div class="flex items-center space-x-3">
          <span class="inline-block w-3 h-3 bg-green-400 rounded-full flex-shrink-0"></span>
          <span class="text-gray-300 font-medium">Mistplay</span>
          <span class="text-gray-500 text-sm">- Full media plan integration active</span>
        </div>
      </div>
      
      <div class="mt-4 p-3 bg-green-900/20 border border-green-700 rounded-lg">
        <p class="text-green-300 text-sm">
          <strong>Coming Soon:</strong> We're rolling out media plan context for additional clients. The foundation is built - now it's about scaling the integration.
        </p>
      </div>
      
      <div class="mt-3 p-3 bg-yellow-900/20 border border-yellow-700 rounded-lg">
        <p class="text-yellow-300 text-sm">
          <strong>Need Your Help:</strong> To expand this feature, I need the correct media plan URLs connected to each client. If you have access to media plans that should be integrated, please share the URLs so we can enable this context for more conversations.
        </p>
      </div>
      
      <!-- Media Plan Screenshot -->
      <div class="mt-4 space-y-2">
        <p class="text-sm text-gray-400 text-center">Media Plan Context in Action</p>
        <%= image_tag "screenshots/client-media-plan.png", class: "w-full rounded-lg border border-gray-600", alt: "AI assistant with media plan context integration" %>
      </div>
    </div>

    <!-- AI Workspaces Enhancement -->
    <div class="bg-gray-800 rounded-lg border border-gray-700 p-6">
      <div class="flex items-center justify-between mb-4">
        <div class="flex items-center space-x-3">
          <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100">
            UX Improvement
          </span>
          <h2 class="text-xl font-semibold text-white">
            <%= link_to "AI Workspaces - Cleaner Default View", new_workspace_path, class: "hover:text-blue-400 transition-colors" %>
          </h2>
        </div>
        <time class="text-sm text-gray-400">June 18, 2025</time>
      </div>
      <p class="text-gray-300 mb-4">
        <span class="text-blue-400 font-semibold">Power Mode</span> now starts with a focused set of columns instead of overwhelming you with everything at once. 
        Less visual noise, faster loading, easier to scan.
      </p>
      
      <!-- Default Columns -->
      <div class="bg-gray-900/50 rounded-lg border border-gray-600 p-4 mb-4">
        <h3 class="text-lg font-semibold text-white mb-3 flex items-center">
          <%= icon "columns-4", library: "lucide", class: "h-5 w-5 text-blue-400 mr-2" %>
          Default Columns
        </h3>
        <div class="grid grid-cols-2 md:grid-cols-4 gap-2 text-sm text-gray-300">
          <% ["Date", "Campaign Name", "Vendor Name", "Click", "Install", "Gross Spend", "Net Spend", "Revenue"].each do |column| %>
            <div class="flex items-center">
              <span class="inline-block w-2 h-2 bg-blue-400 rounded-full mr-2 flex-shrink-0"></span>
              <span><%= column %></span>
            </div>
          <% end %>
        </div>
      </div>
      
      <div class="mt-4 p-3 bg-blue-900/20 border border-blue-700 rounded-lg">
        <p class="text-blue-300 text-sm">
          <strong>Still Flexible:</strong> Add any additional columns you need - we just stopped showing 20+ columns by default.
        </p>
      </div>
    </div>

    <!-- Client & Partner Management -->
    <div class="bg-gray-800 rounded-lg border border-gray-700 p-6">
      <div class="flex items-center justify-between mb-4">
        <div class="flex items-center space-x-3">
          <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800 dark:bg-orange-800 dark:text-orange-100">
            Alpha Release
          </span>
          <h2 class="text-xl font-semibold text-white">Client & Partner Management</h2>
        </div>
        <time class="text-sm text-gray-400">June 17, 2025</time>
      </div>
      <p class="text-gray-300 mb-6">
        We've shipped the <span class="text-orange-400 font-semibold">first working version</span> of our client and partner management system. 
        <span class="text-orange-400 font-semibold">Alpha release</span> means we're iterating based on real usage.
      </p>
      
      <!-- Feature Details -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        <% 
          client_features = [
            "Basic Information",
            "SDK Event Mappings", 
            "Monthly Budgets",
            "Linked Documents",
            "Direct Partners",
            "Fraud Platforms",
            "Client Reports",
            "Slack Integration",
            "Gong Conversations",
            "Firefly Meetings"
          ]
          
          partner_features = [
            "Basic Information",
            "Multi-Categorization",
            "Slack Integration",
            "Gong Integration", 
            "Firefly Integration"
          ]
        %>
        
        <!-- Client Management Features -->
        <div class="bg-gray-900/50 rounded-lg border border-gray-600 p-4">
          <h3 class="text-lg font-semibold text-white mb-3 flex items-center">
            <%= icon "heart-handshake", library: "lucide", class: "h-5 w-5 text-orange-400 mr-2" %>
            <%= link_to "Client Management", clients_path, class: "hover:text-orange-400 transition-colors" %>
          </h3>
          <ul class="space-y-2 text-sm text-gray-300">
            <% client_features.each do |feature| %>
              <li class="flex items-start">
                <span class="inline-block w-2 h-2 bg-orange-400 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                <span><%= feature %></span>
              </li>
            <% end %>
          </ul>
        </div>

        <!-- Partner Management Features -->
        <div class="bg-gray-900/50 rounded-lg border border-gray-600 p-4">
          <h3 class="text-lg font-semibold text-white mb-3 flex items-center">
            <%= icon "package", library: "lucide", class: "h-5 w-5 text-orange-400 mr-2" %>
            <%= link_to "Partner Management", partners_path, class: "hover:text-orange-400 transition-colors" %>
          </h3>
          <ul class="space-y-2 text-sm text-gray-300">
            <% partner_features.each do |feature| %>
              <li class="flex items-start">
                <span class="inline-block w-2 h-2 bg-orange-400 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                <span><%= feature %></span>
              </li>
            <% end %>
          </ul>
        </div>
      </div>
      
      <!-- Screenshots -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
        <div class="space-y-2">
          <p class="text-sm text-gray-400 text-center">Client Management Dashboard</p>
          <%= image_tag "screenshots/clients-overview.png", class: "w-full rounded-lg border border-gray-600", alt: "Clients management interface showing active clients" %>
        </div>
        <div class="space-y-2">
          <p class="text-sm text-gray-400 text-center">Partner Network Overview</p>
          <%= image_tag "screenshots/partners-overview.png", class: "w-full rounded-lg border border-gray-600", alt: "Partners management interface showing running partners" %>
        </div>
      </div>
      
      <div class="mt-4 p-3 bg-orange-900/20 border border-orange-700 rounded-lg">
        <p class="text-orange-300 text-sm">
          <strong>Why Alpha:</strong> The foundation is solid, but we're still refining the experience. Better to get feedback now than polish in isolation.
        </p>
      </div>
    </div>

    <!-- Enhanced AI Assistant -->
    <div class="bg-gray-800 rounded-lg border border-gray-700 p-6">
      <div class="flex items-center justify-between mb-4">
        <div class="flex items-center space-x-3">
          <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800 dark:bg-purple-800 dark:text-purple-100">
            AI Enhancement
          </span>
          <h2 class="text-xl font-semibold text-white">
            <%= link_to "Context-Aware AI Assistant", new_conversation_path, class: "hover:text-purple-400 transition-colors" %>
          </h2>
        </div>
        <time class="text-sm text-gray-400">June 17, 2025</time>
      </div>
      <p class="text-gray-300 mb-4">
        We've taken a <span class="text-purple-400 font-semibold">different approach</span> to how our chatbot understands context. 
        No more "I'm sorry, I don't understand" responses when you're clearly talking about client data it should know about.
      </p>
      
      <!-- AI Assistant Screenshots -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
        <div class="space-y-2">
          <p class="text-sm text-gray-400 text-center">Context-Aware Client Selection</p>
          <%= image_tag "screenshots/client-selector.png", class: "w-full rounded-lg border border-gray-600", alt: "AI assistant client selection interface" %>
        </div>
        <div class="space-y-2">
          <p class="text-sm text-gray-400 text-center">Enhanced Context Management</p>
          <%= image_tag "screenshots/context-view.png", class: "w-full rounded-lg border border-gray-600", alt: "AI assistant context view showing client details" %>
        </div>
        <div class="space-y-2">
          <p class="text-sm text-gray-400 text-center">Slack Integration Shortcut</p>
          <%= image_tag "screenshots/slack-shortcut.png", class: "w-full rounded-lg border border-gray-600", alt: "Slack shortcut integration" %>
        </div>
      </div>
    </div>
  </div>
</div>