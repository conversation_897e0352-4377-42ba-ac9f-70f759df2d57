class SubClientBudgetsController < ApplicationController
  before_action :set_sub_client_budget, only: [ :show ]

  def show
    ahoy.track "Viewed Sub Client Budget", sub_client_budget_id: @sub_client_budget.id

    # Get usage summary and breakdown data
    @usage_summary = @sub_client_budget.usage_summary(months_back: 6)
    @campaign_breakdown = @usage_summary[:campaign_breakdown]
    @partner_breakdown = @sub_client_budget.partner_spend_breakdown
    @trend_data = @sub_client_budget.trend_data(months_back: 6)
  end

  private

  def set_sub_client_budget
    @sub_client_budget = SubClientBudget.find(params[:id])
  end
end
