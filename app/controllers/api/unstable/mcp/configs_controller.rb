# app/controllers/api/unstable/mcp/configs_controller.rb
class Api::Unstable::Mcp::ConfigsController < ApplicationController
  # GET /api/unstable/entities/mcp/configs
  def index
    user = current_user

    config = {
      "mcpServers" => {
        "feedmob-femini-reporting" => {
          "command" => "npx",
          "args": [ "-y", "@feedmob/femini_reporting" ],
          "env": {
            "FEMINI_API_URL": "https://assistant.feedmob.ai",
            "FEMINI_API_TOKEN": user.token
          }
        }
      }
    }

    render json: JSON.pretty_generate(config)
  end
end
