# app/controllers/api/unstable/mcp/campaign_spends_controller.rb
class Api::Unstable::Mcp::CampaignSpendsController < ApplicationController
  # GET /api/unstable/mcp/campaign_spends/index
  def index
    results = CampaignSpendsApiMcpQuery.new(campaign_spends_api_query_params).results

    render json: results
  end


  private

  def campaign_spends_api_query_params
    params.permit(
      :date_lteq,
      :date_gteq,
      groups: [],
      metrics: [],
      legacy_client_id_in: [],
      legacy_partner_id_in: [],
      legacy_campaign_id_in: [],
      legacy_click_url_id_in: [],
    ).to_h
  end
end
