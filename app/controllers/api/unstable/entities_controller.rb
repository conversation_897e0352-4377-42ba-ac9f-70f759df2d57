# app/controllers/api/unstable/entities_controller.rb
class Api::Unstable::EntitiesController < ApplicationController
  # GET /api/unstable/entities/search
  def search
    result = {}
    params[:q].split(",").each do |key|
      clients = Client.search_by_name(key)
      partners = Partner.search_by_name(key)
      campaigns = Campaign.where("name ILIKE ?", "%#{key}%")
      click_url = ClickUrl.find_by_id(key.to_i) if key.match?(/\A\d{5}\z/)

      if clients.any?
        result[:clients] ||= []
        clients.map do |client|
          campaign_ids = Campaign.where(client_id: client.legacy_id).pluck(:id)
          result[:clients] << {
            name: client.name,
            legacy_id: client.legacy_id,
            status: client.active ? "active" : "inactive",
            click_urls: ClickUrl.visible.where(campaign_id: campaign_ids).map { |cu| get_click_url_model(cu) }
          }
        end
      end

      if partners.any?
        result[:partners] ||= []
        partners.map do |partner|
          result[:partners] << {
            name: partner.name,
            legacy_id: partner.legacy_id,
            status: partner.status,
            click_urls: ClickUrl.visible.where(partner_id: partners.pluck(:legacy_id)).map { |cu| get_click_url_model(cu) }
          }
        end
      end

      if campaigns.any?
        result[:campaigns] ||= []
        campaigns.map do |campaign|
          result[:campaigns] << {
            name: campaign.name,
            legacy_id: campaign.legacy_id,
            status: campaign.status,
            click_urls: ClickUrl.visible.where(campaign_id: campaigns.pluck(:legacy_id)).map { |cu| get_click_url_model(cu) }
          }
        end
      end

      if click_url.present?
        result[:click_urls] ||= []
        result[:click_urls] << get_click_url_model(click_url)
      end
    end

    render json: result
  end

  def get_click_url_model(click_url)
    { id: click_url.id, status: click_url.status, link_type: click_url.link_type }
  end
end
