class PartnersController < ApplicationController
  before_action :set_partner, only: %i[ show edit update destroy sync_from_website ]

  # GET /partners or /partners.json
  def index
    ahoy.track "Viewed Partners"

    @query = PartnersQuery.new(
      params.fetch(:query, {}).permit(:name_cont, :status, :hide_test_data, :category_id)
    )
    respond_to do |format|
      format.html do
        @partners = @query.results
      end
      format.json do
        @partners = @query.results
      end
    end
  end

  # GET /partners/search.json
  def search
    partners_with_spends = Partner.search_by_name(params[:q])
                                  .where(id: CampaignSpend.select(:partner_id).distinct)

    _, @partners = pagy(partners_with_spends)
    render json: @partners.map { |partner| { id: partner.legacy_id, name: partner.name } }
  end

  # GET /partners/1 or /partners/1.json
  def show
    ahoy.track "Viewed Partner", partner_id: @partner.id
    @current_section = params[:section] || "overview"
    @sections = [
      { key: "overview", label: "Overview", editable: true },
      { key: "documents", label: "Documents", editable: true },
      { key: "testimonials", label: "Testimonials", editable: true }
    ]
  end

  # GET /partners/1/legacy
  def legacy
    @partner = Partner.find_by!(legacy_id: params[:id])
    render :show
  end

  # GET /partners/new
  def new
    @partner = Partner.new
  end

  # GET /partners/1/edit
  def edit
    ahoy.track "Editing Partner", partner_id: @partner.id
    @current_section = params[:section] || "overview"
    @sections = [
      { key: "overview", label: "Overview" },
      { key: "documents", label: "Documents" },
      { key: "testimonials", label: "Testimonials" }
    ]
  end

  # POST /partners
  def create
    @partner = Partner.new(partner_params)

    if @partner.save
      redirect_to @partner, notice: "Partner was successfully created."
    else
      render :new, status: :unprocessable_entity
    end
  end

  # PATCH/PUT /partners/1
  def update
    if @partner.update(partner_params)
      redirect_to @partner, notice: "Partner was successfully updated."
    else
      render :edit, status: :unprocessable_entity
    end
  end

  # DELETE /partners/1 or /partners/1.json
  def destroy
    @partner.destroy!

    respond_to do |format|
      format.html { redirect_to partners_path, status: :see_other, notice: "Partner was successfully destroyed." }
      format.json { head :no_content }
    end
  end

  def sync_from_website
    SyncExternal::PartnersFromWebsiteJob.perform_later(partner_id: @partner.id)
    redirect_to @partner, notice: "Website sync job has been queued. Data will be updated shortly."
  end

  private

    def set_partner
      @partner = Partner.find(params.expect(:id))
    end

    def partner_params
      params.require(:partner).permit(
        :name, :status, :email, :website, :adjust_id, :slug, :headline,
        :description, :preferred_logo_background, :is_test, :logo, :main_category_id,
        category_ids: [],
        partner_detail_attributes: [
          :id, :company_size, :poc_location, :billing_type, :payment_term,
          :minimum_budget, :reporting_timezone, :os_and_scale,
          :top_performing_geos, :media_description, :benchmark,
          :unique_selling_point, :dashboard_availability, :reporting_practice,
          :skan_compatible, :link_test_ability, :incrementality_test_capacity,
          :multiple_reward_capacity, :appsflyer_agency_approval, :other_notes,
          :add_on_service, :creative_guideline, :incentive_program,
          :macro_mapping, :net_spend_sources, :ownership_for_traffic,
          :requested_link_type, :targeting_capacity, :user_flow_description
        ],
        partner_documents_attributes: [ :id, :title, :category, :file, :_destroy ],
        partner_media_attributes: [ :id, :name, :description, :media_type, :url, :file, :_destroy ],
        partner_quotes_attributes: [ :id, :quote_text, :full_name, :job_title, :company_name, :_destroy ],
        partner_resources_attributes: [ :id, :name, :url, :file, :_destroy ],
        partner_use_cases_attributes: [ :id, :title, :description, :_destroy ],
        partner_social_media_links_attributes: [
          :id, :platform, :url, :active, :_destroy
        ]
      )
    end
end
