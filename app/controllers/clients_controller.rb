class ClientsController < ApplicationController
  before_action :set_client, only: %i[ show edit update destroy sync_from_website ]

  # GET /clients
  def index
    ahoy.track "Viewed Clients"

    @query = ClientsQuery.new(
      params.fetch(:query, {}).permit(:name_cont, :hide_test_data, :active_status)
    )
    @clients = @query.results.includes(
      :client_reports,
      :mobile_apps,
      :campaigns
    )
  end

  # GET /clients/search.json
  def search
    clients_with_spends = Client.search_by_name(params[:q])
                                .where(id: CampaignSpend.select(:client_id).distinct)

    _, @clients = pagy(clients_with_spends)
    render json: @clients.map { |client| { id: client.legacy_id, name: client.name } }
  end

  # GET /clients/1 or /clients/1.json
  def show
    ahoy.track "Viewed Client", client_id: @client.id
    @current_section = params[:section] || "overview"
    @sections = [
      { key: "overview", label: "Overview", editable: true },
      { key: "events", label: "Events", editable: true },
      { key: "creatives", label: "Creatives", editable: true },
      { key: "budgets", label: "Budgets", editable: false },
      { key: "performance", label: "Performance", editable: false },
      { key: "documents", label: "Documents", editable: true },
      { key: "integrations", label: "Integrations", editable: true },
      { key: "client_reports", label: "Client Reports", editable: true },
      { key: "mobile_apps", label: "Mobile Apps", editable: false },
      { key: "communication", label: "Communication", editable: false }
    ]

    # 为Performance tab准备数据
    if @current_section == "performance"
      prepare_performance_data
    end
  end

  # GET /clients/1/legacy
  def legacy
    @client = Client.find_by!(legacy_id: params[:id])
    render :show
  end

  # GET /clients/new
  def new
    @client = Client.new
  end

  def create
    @client = Client.new(client_params)

    if @client.save
      redirect_to @client, notice: "Client was successfully created."
    else
      render :new, status: :unprocessable_entity
    end
  end

  def edit
    ahoy.track "Editing Client", client_id: @client.id
    @current_section = params[:section] || "overview"
    @sections = [
      { key: "overview", label: "Overview" },
      { key: "events", label: "Events" },
      { key: "creatives", label: "Creatives" },
      { key: "documents", label: "Documents" },
      { key: "integrations", label: "Integrations" },
      { key: "client_reports", label: "Client Reports" }
    ]
  end

  def update
    if @client.update(client_params)
      redirect_to @client, notice: "Client was successfully updated."
    else
      render :edit, status: :unprocessable_entity
    end
  end

  # DELETE /clients/1 or /clients/1.json
  def destroy
    @client.destroy!

    respond_to do |format|
      format.html { redirect_to clients_path, status: :see_other, notice: "Client was successfully destroyed." }
      format.json { head :no_content }
    end
  end

  def sync_from_website
    SyncExternal::ClientsFromWebsiteJob.perform_later(client_id: @client.id)
    redirect_to @client, notice: "Website sync job has been queued. Data will be updated shortly."
  end

  private
    def set_client
      @client = Client.find(params.expect(:id))
    end

    def prepare_performance_data
      start_date = params[:start_date]
      end_date = params[:end_date]

      if start_date.present? && end_date.present?
        @budget_usage = @client.budget_usage_summary(start_date: start_date, end_date: end_date)
        @monthly_budget_breakdown = @client.monthly_budget_spend_breakdown(start_date: start_date, end_date: end_date)
        @monthly_budget_chart_data = @client.monthly_budget_chart_data(start_date: start_date, end_date: end_date)
        date_range = start_date.to_date..end_date.to_date
      else
        @budget_usage = @client.budget_usage_summary(months_back: 2)
        @monthly_budget_breakdown = @client.monthly_budget_spend_breakdown(months_back: 2)
        @monthly_budget_chart_data = @client.monthly_budget_chart_data(months_back: 2)
        date_range = 2.months.ago.beginning_of_month..Date.current.end_of_month
      end

      # 获取Partner分解数据
      @partner_breakdown = extract_partner_breakdown_data(@monthly_budget_breakdown)

      # 获取Country分解数据
      @country_breakdown = get_country_breakdown_data(date_range)
    end

    private

    def extract_partner_breakdown_data(monthly_breakdown)
      partner_totals = {}
      total_spend = 0

      monthly_breakdown.each do |month|
        month[:partner_breakdown].each do |partner|
          partner_name = partner[:partner_name]
          spend_amount = partner[:spend_amount]

          partner_totals[partner_name] ||= { spend_amount: 0 }
          partner_totals[partner_name][:spend_amount] += spend_amount
          total_spend += spend_amount
        end
      end

      partner_totals.map do |partner_name, data|
        # 通过CampaignMonthlySpendSummary获取该partner的campaigns数量
        campaigns_count = CampaignMonthlySpendSummary.joins(:partner, :campaign)
                                                    .where(
                                                      legacy_client_id: @client.legacy_id,
                                                      partners: { name: partner_name }
                                                    )
                                                    .distinct
                                                    .count(:legacy_campaign_id)

        {
          partner_name: partner_name,
          spend_amount: data[:spend_amount],
          percentage: total_spend > 0 ? (data[:spend_amount] / total_spend * 100).round(1) : 0,
          campaigns_count: campaigns_count
        }
      end.sort_by { |p| -p[:spend_amount] }
    end

    def get_country_breakdown_data(date_range)
      country_data = CampaignMonthlySpendSummary.where(
        legacy_client_id: @client.legacy_id,
        month_date: date_range
      ).joins("JOIN campaigns ON campaigns.legacy_id = campaign_monthly_spend_summaries.legacy_campaign_id")
       .group("campaigns.country_code")
       .sum(:adjusted_gross_spend)

      total_spend = country_data.values.sum
      return [] if total_spend == 0

      country_data.map do |country_code, spend_amount|
        campaigns_count = @client.campaigns.where(country_code: country_code).count

        {
          country_code: country_code,
          spend_amount: spend_amount,
          percentage: (spend_amount / total_spend * 100).round(1),
          campaigns_count: campaigns_count
        }
      end.sort_by { |c| -c[:spend_amount] }
    end

    def client_params
      params.require(:client).permit(
        :name,
        :headline,
        :website,
        :email_suffix,
        :is_test,
        :preferred_logo_background,
        :logo,
        :active,
        client_detail_attributes: [
          :id, :contact_name, :account_executive_user_id, :account_manager_user_id,
          :preferred_communication_channel, :client_persona, :target_audience,
          :user_behavior_notes, :biggest_obstacles, :noteworthy_restrictions, :user_flow_description, :additional_notes,
          :_destroy, supported_geos: []
        ],
        client_budgets_attributes: [
          :id, :amount, :start_date, :end_date, :_destroy
        ],
        client_events_attributes: [
          :id, :app_sdk_event, :feedmob_event, :description, :goal_cpa,
          :cvr_goal_from_install, :is_primary_kpi, :_destroy
        ],
        client_reports_attributes: [
          :id, :report_name, :report_type, :unique_keys, :date_keys, :rolling_insert, :active, :_destroy
        ],
        client_creatives_attributes: [
          :id, :name, :url, :file, :_destroy
        ],
        client_interests_attributes: [
          :id, :interest_category, :interest_name, :relevance_score, :_destroy
        ],
        client_deal_score_attributes: [
          :id, :budget_potential_score, :margin_potential_score, :brand_value_score,
          :mission_alignment_score, :partner_openness_score, :tracking_ease_score,
          :attribution_simplicity_score, :uniqueness_score, :_destroy
        ],
        client_direct_partners_attributes: [
          :id, :partner_id, :relationship_type, :_destroy
        ],
        client_social_media_links_attributes: [
          :id, :platform, :url, :active, :_destroy
        ],
        client_track_parties_attributes: [
          :id, :track_party_id, :access_url, :account_username, :_destroy
        ],
        client_documents_attributes: [
          :id, :title, :category, :url, :file, :assistant_read, :assistant_read_sheet_names, :_destroy
        ],
        client_fraud_platforms_attributes: [
          :id, :fraud_platform_id, :_destroy
        ],
        attribution_windows_attributes: [
          :id, :duration, :window_type, :probabilistic_enabled, :tracking_method, :_destroy
        ]
      )
    end
end
