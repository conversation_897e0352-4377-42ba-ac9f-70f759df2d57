class ClientsController < ApplicationController
  before_action :set_client, only: %i[ show edit update destroy sync_from_website ]

  # GET /clients
  def index
    ahoy.track "Viewed Clients"

    @query = ClientsQuery.new(
      params.fetch(:query, {}).permit(:name_cont, :hide_test_data, :active_status)
    )
    @clients = @query.results
  end

  # GET /clients/search.json
  def search
    clients_with_spends = Client.search_by_name(params[:q])
                                .where(id: CampaignSpend.select(:client_id).distinct)

    _, @clients = pagy(clients_with_spends)
    render json: @clients.map { |client| { id: client.legacy_id, name: client.name } }
  end

  # GET /clients/1 or /clients/1.json
  def show
    ahoy.track "Viewed Client", client_id: @client.id

    # Get budget usage data for the client
    start_date = params[:start_date]
    end_date = params[:end_date]

    if start_date.present? && end_date.present?
      @budget_usage = @client.budget_usage_summary(start_date: start_date, end_date: end_date)
      @budget_trend = @client.budget_trend_data(start_date: start_date, end_date: end_date)
      @partner_breakdown = @client.partner_spend_breakdown(start_date: start_date, end_date: end_date)
    else
      @budget_usage = @client.budget_usage_summary(months_back: 6)
      @budget_trend = @client.budget_trend_data(months_back: 6)
      @partner_breakdown = @client.partner_spend_breakdown(months_back: 6)
    end

    @daily_spend_prediction = @client.predict_month_end_spend
    @current_month_daily_spends = @client.current_month_daily_spends
  end

  # GET /clients/1/legacy
  def legacy
    @client = Client.find_by!(legacy_id: params[:id])
    render :show
  end

  # GET /clients/1/budget_data
  def budget_data
    start_date = params[:start_date]
    end_date = params[:end_date]

    if start_date.present? && end_date.present?
      @budget_usage = @client.budget_usage_summary(start_date: start_date, end_date: end_date)
      @budget_trend = @client.budget_trend_data(start_date: start_date, end_date: end_date)
      @partner_breakdown = @client.partner_spend_breakdown(start_date: start_date, end_date: end_date)
    else
      @budget_usage = @client.budget_usage_summary(months_back: 6)
      @budget_trend = @client.budget_trend_data(months_back: 6)
      @partner_breakdown = @client.partner_spend_breakdown(months_back: 6)
    end

    render partial: "clients/show/budget_data"
  end

  # GET /clients/new
  def new
    @client = Client.new
  end

  def create
    @client = Client.new(client_params)

    if @client.save
      redirect_to @client, notice: "Client was successfully created."
    else
      render :new, status: :unprocessable_entity
    end
  end

  def edit
    ahoy.track "Editing Client", client_id: @client.id
  end

  def update
    if @client.update(client_params)
      redirect_to @client, notice: "Client was successfully updated."
    else
      render :edit, status: :unprocessable_entity
    end
  end

  # DELETE /clients/1 or /clients/1.json
  def destroy
    @client.destroy!

    respond_to do |format|
      format.html { redirect_to clients_path, status: :see_other, notice: "Client was successfully destroyed." }
      format.json { head :no_content }
    end
  end

  def sync_from_website
    SyncExternal::ClientsFromWebsiteJob.perform_later(client_id: @client.id)
    redirect_to @client, notice: "Website sync job has been queued. Data will be updated shortly."
  end

  private
    def set_client
      @client = Client.find(params.expect(:id))
    end

    def client_params
      params.require(:client).permit(
        :name,
        :headline,
        :website,
        :email_suffix,
        :is_test,
        :preferred_logo_background,
        :logo,
        :active,
        client_detail_attributes: [
          :id, :contact_name, :account_executive_user_id, :account_manager_user_id,
          :preferred_communication_channel, :client_persona, :target_audience,
          :user_behavior_notes, :biggest_obstacles, :noteworthy_restrictions, :user_flow_description, :additional_notes,
          :_destroy, supported_geos: []
        ],
        client_budgets_attributes: [
          :id, :amount, :start_date, :end_date, :_destroy
        ],
        client_events_attributes: [
          :id, :app_sdk_event, :feedmob_event, :description, :goal_cpa,
          :cvr_goal_from_install, :is_primary_kpi, :_destroy
        ],
        client_reports_attributes: [
          :id, :report_name, :report_type, :unique_keys, :date_keys, :rolling_insert, :active, :_destroy
        ],
        client_creatives_attributes: [
          :id, :name, :url, :file, :_destroy
        ],
        client_interests_attributes: [
          :id, :interest_category, :interest_name, :relevance_score, :_destroy
        ],
        client_deal_score_attributes: [
          :id, :budget_potential_score, :margin_potential_score, :brand_value_score,
          :mission_alignment_score, :partner_openness_score, :tracking_ease_score,
          :attribution_simplicity_score, :uniqueness_score, :_destroy
        ],
        client_direct_partners_attributes: [
          :id, :partner_id, :relationship_type, :_destroy
        ],
        client_social_media_links_attributes: [
          :id, :platform, :url, :active, :_destroy
        ],
        client_track_parties_attributes: [
          :id, :track_party_id, :access_url, :account_username, :_destroy
        ],
        client_documents_attributes: [
          :id, :title, :category, :url, :file, :assistant_read, :assistant_read_sheet_names, :_destroy
        ],
        client_fraud_platforms_attributes: [
          :id, :fraud_platform_id, :_destroy
        ],
        attribution_windows_attributes: [
          :id, :duration, :window_type, :probabilistic_enabled, :tracking_method, :_destroy
        ]
      )
    end
end
