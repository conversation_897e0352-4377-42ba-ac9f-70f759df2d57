class ConversationContextsController < ApplicationController
  before_action :set_conversation

  def create
    update_conversation_context
    redirect_to @conversation
  end

  def destroy
    @context = @conversation.conversation_contexts.find(params[:id])
    @context.destroy
    redirect_to @conversation
  end

  private

  def set_conversation
    @conversation = Conversation.find(params[:conversation_id])
  end

  def context_params
    params.require(:conversation_context).permit(:include_activities, :context_start_date, :context_end_date, client_ids: [], partner_ids: [], client_document_ids: [])
  end

  def update_conversation_context
    # Get the new contextual objects
    new_contextuais = []

    context_settings = @conversation.context_settings
    context_settings[:include_activities] = context_params[:include_activities] == "1"
    context_settings[:context_start_date] = context_params[:context_start_date] if context_params[:context_start_date].present?
    context_settings[:context_end_date] = context_params[:context_end_date] if context_params[:context_end_date].present?
    @conversation.update(context_settings: context_settings)

    if context_params[:client_ids].present?
      context_params[:client_ids].reject(&:blank?).each do |client_id|
        new_contextuais << Client.find(client_id)
      end
    end

    if context_params[:partner_ids].present?
      context_params[:partner_ids].reject(&:blank?).each do |partner_id|
        new_contextuais << Partner.find(partner_id)
      end
    end

    if context_params[:client_document_ids].present?
      context_params[:client_document_ids].reject(&:blank?).each do |client_document_id|
        new_contextuais << ClientDocument.find(client_document_id)
      end
    end

    # Remove contexts that are no longer needed
    existing_contexts = @conversation.conversation_contexts.includes(:contextual)
    existing_contexts.each do |context|
      unless new_contextuais.include?(context.contextual)
        context.destroy
      end
    end

    # Add new contexts that don't already exist
    existing_contextuais = existing_contexts.map(&:contextual)
    new_contextuais.each do |contextual|
      unless existing_contextuais.include?(contextual)
        @conversation.conversation_contexts.create!(contextual: contextual)
      end
    end
  end
end
