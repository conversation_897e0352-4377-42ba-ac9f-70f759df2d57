class ConversationsController < ApplicationController
  before_action :set_conversation, only: [ :show, :update, :destroy, :context ]

  def new
    create
  end

  def create
    authorize Conversation
    find_or_create_conversation
    redirect_to @conversation
  end

  def show
    @assistants = [
      [ "FeedAI Planner (WIP)", "planner" ]
    ]

    @model_options = [
      [ "Professional", "claude_4_0_sonnet" ],
      [ "Standard", "claude_3_5_haiku" ],
      [ "Reasoning", "claude_3_7_sonnet_thinking" ],
      [ "Experiment", "meta_maverick_17b" ]
    ]

    @clients = Client.active.order(:name)
    @partners = Partner.active.order(:name)
    @available_client_documents = ClientDocument.readable_by_assistant.where(
      client: @conversation.clients
    ) | @conversation.client_documents

    authorize @conversation
  end

  def context
    authorize @conversation

    @context = @conversation.formatted_context
  end

  # PATCH/PUT /conversations/1 or /conversations/1.json
  def update
    authorize @conversation

    streaming = true
    if conversation_params[:model] == "meta_maverick_17b"
      streaming = false
    end

    if @conversation.update(conversation_params.merge(streaming: streaming))
      redirect_to @conversation
    else
      head :unprocessable_entity
    end
  end

  def destroy
    authorize @conversation

    @conversation.destroy
    redirect_to conversations_path
  end

  private

  def set_conversation
    @conversation = Conversation.find(params[:id])
  end

  def conversation_params
    params.require(:conversation).permit(:assistant, :model, :profile)
  end

  def find_or_create_conversation
    @conversation = current_user.conversations.where(source: "web").last
    if @conversation && @conversation.messages.empty?
      # Nothing
    else
      @conversation = Conversation.create(
        user: current_user,
        source: "web",
        streaming: true
      )
    end
  end
end
