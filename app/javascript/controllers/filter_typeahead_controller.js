import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["input", "item"]

  filterItems() {
    const searchTerm = this.inputTarget.value.toLowerCase().trim().replace(/_/g, ' ')
    
    this.itemTargets.forEach(item => {
      const label = item.closest('div').querySelector('label')
      const text = label.textContent.toLowerCase().trim().replace(/_/g, ' ')
      const match = text.includes(searchTerm)
      item.closest('div').style.display = match ? '' : 'none'
    })

    // Update select all state after filtering
    const filterCounterController = this.element.querySelector('[data-controller="filter-counter"]')?.controller
    if (filterCounterController) {
      filterCounterController.updateSelectAllState()
    }
  }
}
