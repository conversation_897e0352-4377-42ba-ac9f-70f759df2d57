class ClientBudgetQuery
  include ActiveModel::Model
  include ActiveModel::Attributes

  attribute :client
  attribute :months_back, :integer, default: 6
  attribute :start_date, :date
  attribute :end_date, :date

  def initialize(client, **options)
    @client = client
    super(options)
  end

  def budget_usage_summary
    calculate_date_range

    budgets = fetch_budgets
    monthly_spends = fetch_monthly_spends

    total_budget = budgets.sum(:amount)
    total_spent = monthly_spends.sum(:adjusted_gross_spend) || 0
    usage_percentage = total_budget > 0 ? (total_spent / total_budget * 100).round(2) : 0

    {
      total_budget: total_budget,
      total_spent: total_spent,
      remaining_budget: total_budget - total_spent,
      usage_percentage: usage_percentage,
      period_start: @start_date,
      period_end: @end_date,
      budgets_count: budgets.count,
      monthly_breakdown: monthly_breakdown(monthly_spends)
    }
  end

  def monthly_budget_spend_breakdown
    calculate_date_range

    months_range = (@start_date..@end_date).map(&:beginning_of_month).uniq
    monthly_spends = fetch_monthly_spends
    budgets = fetch_budgets

    months_range.map do |month|
      month_key = month.strftime("%Y-%m")
      month_label = month.strftime("%b %Y")
      month_spends = monthly_spends.where(month_date: month)
      total_spend = month_spends.sum(:adjusted_gross_spend) || 0
      partner_breakdown = get_monthly_partner_breakdown(month_spends)
      monthly_budget = calculate_monthly_budget_for_month(month, budgets)

      {
        month: month_key,
        month_label: month_label,
        budget: monthly_budget.round(2),
        total_spend: total_spend.round(2),
        partner_breakdown: partner_breakdown
      }
    end
  end

  def monthly_budget_chart_data
    monthly_breakdown = monthly_budget_spend_breakdown
    chart_data = []

    # Total Budget data
    total_budget_data = monthly_breakdown.map do |month|
      [ month[:month_label], month[:budget] ]
    end
    chart_data << {
      name: "Total Budget",
      data: total_budget_data,
      color: "#5959AB",  # Light gray
      stack: "total_budget"
    }

    # Total Spent data
    total_spent_data = monthly_breakdown.map do |month|
      [ month[:month_label], month[:total_spend] ]
    end
    chart_data << {
      name: "Total Spent",
      data: total_spent_data,
      color: "#FCA5A5",  # Light red
      stack: "total_spent"
    }

    # Partner breakdown data
    add_partner_data_to_chart(chart_data, monthly_breakdown)

    # Prediction data
    add_prediction_data_to_chart(chart_data, monthly_breakdown)

    {
      data: chart_data,
      budget_labels: monthly_breakdown.map { |month| month[:budget].round(0) },
      prediction_data: predict_month_end_spend
    }
  end

  def predict_month_end_spend
    current_month_start = Date.current.beginning_of_month
    current_month_spend = CampaignMonthlySpendSummary.where(
      legacy_client_id: @client.legacy_id,
      month_date: current_month_start
    ).sum(:adjusted_gross_spend)

    days_passed = Date.current.day
    days_in_month = Date.current.end_of_month.day

    if days_passed <= 5
      return {
        current_total: current_month_spend,
        predicted_total: nil,
        average_daily: nil,
        days_remaining: days_in_month - days_passed,
        prediction_method: "No prediction (Early month)",
        confidence: "N/A",
        recent_trend: []
      }
    end

    if current_month_spend > 0 && days_passed > 5
      daily_rate = current_month_spend / days_passed
      predicted_total = (daily_rate * days_in_month).round(2)

      confidence = if days_passed >= 20
        "High"
      elsif days_passed >= 10
        "Medium"
      else
        "Low"
      end

      {
        current_total: current_month_spend,
        predicted_total: predicted_total,
        average_daily: daily_rate.round(2),
        days_remaining: days_in_month - days_passed,
        prediction_method: "Current Month Linear",
        confidence: confidence,
        recent_trend: []
      }
    else
      {
        current_total: current_month_spend,
        predicted_total: 0,
        average_daily: 0,
        days_remaining: days_in_month - days_passed,
        prediction_method: "No spend data",
        confidence: "Low",
        recent_trend: []
      }
    end
  end

  private

  def calculate_date_range
    if start_date && end_date
      @start_date = start_date.to_date
      @end_date = end_date.to_date
    else
      @end_date = Date.current - 1.day
      @start_date = (Date.current - months_back.months).beginning_of_month
    end
  end

  def fetch_budgets
    @client.client_budgets.where(
      "(start_date <= ? AND end_date >= ?) OR (start_date >= ? AND start_date <= ?)",
      @end_date, @start_date, @start_date, @end_date
    )
  end

  def fetch_monthly_spends
    CampaignMonthlySpendSummary.where(
      legacy_client_id: @client.legacy_id,
      month_date: @start_date..@end_date
    )
  end

  def calculate_monthly_budget_for_month(month, budgets)
    month_start = month.beginning_of_month
    month_end = month.end_of_month
    total_budget_for_month = 0

    budgets.each do |budget|
      overlap_start = [ budget.start_date, month_start ].max
      overlap_end = [ budget.end_date, month_end ].min

      if overlap_start <= overlap_end
        budget_total_days = (budget.end_date - budget.start_date + 1).to_i
        overlap_days = (overlap_end - overlap_start + 1).to_i
        monthly_allocation = (budget.amount * overlap_days / budget_total_days)
        total_budget_for_month += monthly_allocation
      end
    end

    total_budget_for_month
  end

  def get_monthly_partner_breakdown(month_spends)
    return [] if month_spends.empty?

    partner_totals = month_spends.group(:legacy_partner_id).sum(:adjusted_gross_spend)
    total_spend = partner_totals.values.sum
    return [] if total_spend == 0

    partner_totals.map do |partner_id, spend_amount|
      partner = Partner.find_by(legacy_id: partner_id)
      next unless partner

      {
        partner_id: partner_id,
        partner_name: partner.name,
        spend_amount: spend_amount.round(2),
        percentage: ((spend_amount / total_spend) * 100).round(2)
      }
    end.compact.sort_by { |item| -item[:spend_amount] }
  end

  def add_partner_data_to_chart(chart_data, monthly_breakdown)
    # Get all unique partners from monthly breakdown (no total sorting)
    all_partners = Set.new
    monthly_breakdown.each do |month|
      month[:partner_breakdown].each do |partner|
        all_partners.add(partner[:partner_name])
      end
    end
    sorted_partners = all_partners.to_a

    partner_colors = [ "#3B82F6", "#10B981", "#F59E0B", "#EF4444", "#8B5CF6", "#F97316", "#06B6D4", "#84CC16", "#F472B6", "#A78BFA" ]

    sorted_partners.each_with_index do |partner_name, index|
      partner_data = monthly_breakdown.map do |month|
        partner_spend = month[:partner_breakdown].find { |p| p[:partner_name] == partner_name }
        [ month[:month_label], partner_spend ? partner_spend[:spend_amount] : 0 ]
      end

      chart_data << {
        name: partner_name,
        data: partner_data,
        color: partner_colors[index % partner_colors.length],
        stack: "partner_breakdown"
      }
    end
  end

  def add_prediction_data_to_chart(chart_data, monthly_breakdown)
    prediction_data = predict_month_end_spend
    current_month_label = Date.current.strftime("%b %Y")

    if monthly_breakdown.any? { |month| month[:month_label] == current_month_label } &&
       prediction_data[:predicted_total].present? && prediction_data[:predicted_total] > 0

      predicted_spend_data = monthly_breakdown.map do |month|
        if month[:month_label] == current_month_label
          [ month[:month_label], prediction_data[:predicted_total] ]
        else
          [ month[:month_label], 0 ]
        end
      end

      chart_data << {
        name: "Predicted Spend",
        data: predicted_spend_data,
        color: "#FDE047",  # Yellow
        stack: "predicted_spend"
      }
    end
  end
end
