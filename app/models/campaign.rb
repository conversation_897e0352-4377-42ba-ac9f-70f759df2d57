class Campaign < ApplicationRecord
  belongs_to :client
  belongs_to :mobile_app, optional: true

  has_many :click_urls, dependent: :destroy
  has_many :monthly_campaign_spend_adjustments, dependent: :destroy
  has_many :sub_client_budget_campaigns, dependent: :destroy
  has_many :sub_client_budgets, through: :sub_client_budget_campaigns

  before_save :set_country_code

  scope :active_within, ->(start_date, end_date) {
    DailyCampaignSpend.where(
      "spend_date >= ? AND spend_date <= ?", start_date, end_date
    ).distinct.pluck(:legacy_campaign_id).then do |campaign_ids|
      where(legacy_id: campaign_ids)
    end
  }

  def display_name
    name.gsub(/_/, " ")
  end

  private

  def set_country_code
    self.country_code = CountryCode.from_campaign_name(name)
  end
end
