module ContextBuilder
  class CampaignPerformanceBuilder
    include BuilderUtilities

    def initialize(client, start_date: 1.year.ago.beginning_of_year, end_date: Date.current)
      @client = client
      @start_date = start_date.to_date
      @end_date = end_date.to_date
    end

    def build
      campaign_data = fetch_campaign_data

      return nil if campaign_data.empty?

      # Check if total gross spend meets minimum threshold
      totals = calculate_totals(campaign_data)
      return nil if totals[:gross_spend] < 5

      sections = []
      sections << build_client_summary(campaign_data)
      sections << build_campaign_breakdown(campaign_data)
      sections << build_partner_breakdown(campaign_data)
      sections << build_country_breakdown(campaign_data)
      sections << build_monthly_performance(campaign_data)
      sections << build_weekly_performance(campaign_data)

      sections.join("\n")
    end

    private

    attr_reader :client

    def fetch_campaign_data
      query_params = {
        date_lteq: @end_date.to_s,
        date_gteq: @start_date.to_s,
        legacy_client_id_in: [ client.legacy_id ],
        sort_by: "date_asc"
      }

      CampaignSpendsQuery.new(query_params).results
        .includes(:campaign, :partner, :click_url, :client)
    end

    def build_client_summary(campaign_data)
      return "" if campaign_data.empty?

      totals = calculate_totals(campaign_data)
      first_spend = campaign_data.first&.spend_date
      last_spend = campaign_data.last&.spend_date
      days_active = campaign_data.map(&:spend_date).uniq.count

      # Get unique campaigns, partners, and countries
      unique_campaigns = campaign_data.map { |d| d.campaign&.legacy_id }.compact.uniq.count
      unique_partners = campaign_data.map { |d| d.partner&.name }.compact.uniq.count
      unique_countries = campaign_data.map { |d| d.campaign&.country_code }.compact.uniq.count

      # Get mobile apps
      mobile_apps = campaign_data.map { |d| d.campaign&.mobile_app&.name }.compact.uniq
      platforms = campaign_data.map { |d| d.campaign&.mobile_app&.platform }.compact.uniq

      <<~SECTION
        ## Client Performance Summary - #{client.name}

        **Analysis Period:** #{@start_date.strftime('%b %d, %Y')} - #{@end_date.strftime('%b %d, %Y')}

        | Metric | Value |
        |--------|-------|
        | Client Name | #{client.name} |
        | Client ID | #{client.legacy_id} |
        | Mobile Apps | #{mobile_apps.join(', ')} |
        | Platforms | #{platforms.join(', ')} |
        | Total Gross Spend | $#{format_currency(totals[:gross_spend])} |
        | Total Net Spend | $#{format_currency(totals[:net_spend])} |
        | Total Revenue | $#{format_currency(totals[:revenue])} |
        | Average Margin | #{totals[:avg_margin]}% |
        | Total Impressions | #{totals[:impressions]} |
        | Total Clicks | #{totals[:clicks]} |
        | Total Installs | #{totals[:installs]} |
        | Average CVR | #{totals[:avg_cvr]}% |
        | Active Campaigns | #{unique_campaigns} |
        | Active Partners | #{unique_partners} |
        | Active Countries | #{unique_countries} |
        | First Spend Date | #{first_spend&.strftime('%b %d, %Y') || 'N/A'} |
        | Last Spend Date | #{last_spend&.strftime('%b %d, %Y') || 'N/A'} |
        | Days Active | #{days_active} |
        | Avg Daily Spend | $#{format_currency(totals[:gross_spend] / [ days_active, 1 ].max)} |
      SECTION
    end

    def build_campaign_breakdown(campaign_data)
      campaign_data_grouped = group_by_campaign(campaign_data)
      return "" if campaign_data_grouped.empty?

      campaign_rows = campaign_data_grouped.map do |campaign_name, data|
        totals = calculate_totals(data)
        days_active = data.map(&:spend_date).uniq.count
        partners_count = data.map { |d| d.partner&.name }.compact.uniq.count
        countries = data.map { |d| d.campaign&.country_code }.compact.uniq.join(", ")
        first_date = data.map(&:spend_date).min&.strftime("%b %d")
        last_date = data.map(&:spend_date).max&.strftime("%b %d")
        campaign_id = data.first&.campaign&.legacy_id

        "| #{campaign_name} (#{campaign_id}) | #{countries} | $#{format_currency(totals[:gross_spend])} | $#{format_currency(totals[:revenue])} | #{totals[:avg_margin]}% | #{totals[:impressions]} | #{totals[:clicks]} | #{totals[:installs]} | #{totals[:avg_cvr]}% | #{partners_count} | #{days_active} | #{first_date} - #{last_date} |"
      end

      <<~SECTION
        ## Performance by Campaign

        | Campaign | Countries | Gross Spend | Revenue | Margin | Impressions | Clicks | Installs | CVR | Partners | Active Days | Date Range |
        |----------|-----------|-------------|---------|--------|-------------|--------|----------|-----|----------|-------------|------------|
        #{campaign_rows.join("\n")}
      SECTION
    end

    def build_partner_breakdown(campaign_data)
      partner_data = group_by_partner(campaign_data)
      return "" if partner_data.empty?

      partner_rows = partner_data.map do |partner_name, data|
        totals = calculate_totals(data)
        campaigns_count = data.map { |d| d.campaign&.legacy_id }.compact.uniq.count
        countries = data.map { |d| d.campaign&.country_code }.compact.uniq.join(", ")
        days_active = data.map(&:spend_date).uniq.count
        first_date = data.map(&:spend_date).min&.strftime("%b %d")
        last_date = data.map(&:spend_date).max&.strftime("%b %d")

        "| #{partner_name} | #{campaigns_count} | #{countries} | $#{format_currency(totals[:gross_spend])} | $#{format_currency(totals[:revenue])} | #{totals[:avg_margin]}% | #{totals[:impressions]} | #{totals[:clicks]} | #{totals[:installs]} | #{totals[:avg_cvr]}% | #{days_active} | #{first_date} - #{last_date} |"
      end

      <<~SECTION
        ## Performance by Partner

        | Partner | Campaigns | Countries | Gross Spend | Revenue | Margin | Impressions | Clicks | Installs | CVR | Active Days | Date Range |
        |---------|-----------|-----------|-------------|---------|--------|-------------|--------|----------|-----|-------------|------------|
        #{partner_rows.join("\n")}
      SECTION
    end

    def build_country_breakdown(campaign_data)
      country_data = group_by_country(campaign_data)
      return "" if country_data.empty?

      country_rows = country_data.map do |country_code, data|
        totals = calculate_totals(data)
        campaigns_count = data.map { |d| d.campaign&.legacy_id }.compact.uniq.count
        partners_count = data.map { |d| d.partner&.name }.compact.uniq.count
        days_active = data.map(&:spend_date).uniq.count
        first_date = data.map(&:spend_date).min&.strftime("%b %d")
        last_date = data.map(&:spend_date).max&.strftime("%b %d")

        "| #{country_code || 'Unknown'} | #{campaigns_count} | #{partners_count} | $#{format_currency(totals[:gross_spend])} | $#{format_currency(totals[:revenue])} | #{totals[:avg_margin]}% | #{totals[:impressions]} | #{totals[:clicks]} | #{totals[:installs]} | #{totals[:avg_cvr]}% | #{days_active} | #{first_date} - #{last_date} |"
      end

      <<~SECTION
        ## Performance by Country

        | Country | Campaigns | Partners | Gross Spend | Revenue | Margin | Impressions | Clicks | Installs | CVR | Active Days | Date Range |
        |---------|-----------|----------|-------------|---------|--------|-------------|--------|----------|-----|-------------|------------|
        #{country_rows.join("\n")}
      SECTION
    end

    def build_monthly_performance(campaign_data)
      monthly_data = group_by_period(campaign_data, :month)
      return "" if monthly_data.empty?

      sections = [ "## Monthly Performance Breakdown" ]

      monthly_data.each do |period, data|
        month_name = period.strftime("%B %Y")
        sections << build_monthly_detail(month_name, data)
      end

      sections.join("\n")
    end

    def build_monthly_detail(month_name, data)
      totals = calculate_totals(data)
      days_active = data.map(&:spend_date).uniq.count

      # Campaign breakdown for this month
      campaign_data_grouped = group_by_campaign(data)
      campaign_rows = campaign_data_grouped.take(5).map do |campaign_name, campaign_month_data|
        campaign_totals = calculate_totals(campaign_month_data)
        campaign_id = campaign_month_data.first&.campaign&.legacy_id
        "| #{campaign_name} (#{campaign_id}) | $#{format_currency(campaign_totals[:gross_spend])} | #{campaign_totals[:avg_margin]}% | #{campaign_totals[:installs]} | #{campaign_totals[:avg_cvr]}% |"
      end

      # Partner breakdown for this month
      partner_data = group_by_partner(data)
      partner_rows = partner_data.take(5).map do |partner_name, partner_month_data|
        partner_totals = calculate_totals(partner_month_data)
        "| #{partner_name} | $#{format_currency(partner_totals[:gross_spend])} | #{partner_totals[:avg_margin]}% | #{partner_totals[:installs]} | #{partner_totals[:avg_cvr]}% |"
      end

      # Country breakdown for this month
      country_data = group_by_country(data)
      country_rows = country_data.map do |country_code, country_month_data|
        country_totals = calculate_totals(country_month_data)
        "| #{country_code || 'Unknown'} | $#{format_currency(country_totals[:gross_spend])} | #{country_totals[:avg_margin]}% | #{country_totals[:installs]} | #{country_totals[:avg_cvr]}% |"
      end

      <<~SECTION

        ### #{month_name}
        **Overall:** $#{format_currency(totals[:gross_spend])} spend, #{totals[:avg_margin]}% margin, #{totals[:avg_cvr]}% CVR, #{days_active} active days

        **Top Campaigns:**
        | Campaign | Gross Spend | Margin | Installs | CVR |
        |----------|-------------|--------|----------|-----|
        #{campaign_rows.join("\n")}

        **Top Partners:**
        | Partner | Gross Spend | Margin | Installs | CVR |
        |---------|-------------|--------|----------|-----|
        #{partner_rows.join("\n")}

        **By Country:**
        | Country | Gross Spend | Margin | Installs | CVR |
        |---------|-------------|--------|----------|-----|
        #{country_rows.join("\n")}
      SECTION
    end

    def build_weekly_performance(campaign_data)
      # Show last 8 weeks to keep it manageable
      recent_data = campaign_data.select { |d| d.spend_date >= (@end_date - 8.weeks).to_date }
      weekly_data = group_by_period(recent_data, :week)

      return "" if weekly_data.empty?

      sections = [ "## Weekly Performance Breakdown (Last 8 Weeks)" ]

      weekly_data.each do |period, data|
        week_start = period.beginning_of_week.strftime("%b %d")
        week_end = period.end_of_week.strftime("%b %d")
        week_label = "#{week_start} - #{week_end}"
        sections << build_weekly_detail(week_label, data)
      end

      sections.join("\n")
    end

    def build_weekly_detail(week_label, data)
      totals = calculate_totals(data)
      days_active = data.map(&:spend_date).uniq.count

      # Top campaigns for this week
      campaign_data_grouped = group_by_campaign(data)
      campaign_rows = campaign_data_grouped.take(3).map do |campaign_name, campaign_week_data|
        campaign_totals = calculate_totals(campaign_week_data)
        campaign_id = campaign_week_data.first&.campaign&.legacy_id
        "| #{campaign_name} (#{campaign_id}) | $#{format_currency(campaign_totals[:gross_spend])} | #{campaign_totals[:avg_margin]}% | #{campaign_totals[:installs]} |"
      end

      # Top partners for this week
      partner_data = group_by_partner(data)
      partner_rows = partner_data.take(3).map do |partner_name, partner_week_data|
        partner_totals = calculate_totals(partner_week_data)
        "| #{partner_name} | $#{format_currency(partner_totals[:gross_spend])} | #{partner_totals[:avg_margin]}% | #{partner_totals[:installs]} |"
      end

      <<~SECTION

        ### #{week_label}
        **Overall:** $#{format_currency(totals[:gross_spend])} spend, #{totals[:avg_margin]}% margin, #{totals[:avg_cvr]}% CVR, #{days_active} active days

        **Top Campaigns:**
        | Campaign | Gross Spend | Margin | Installs |
        |----------|-------------|--------|----------|
        #{campaign_rows.join("\n")}

        **Top Partners:**
        | Partner | Gross Spend | Margin | Installs |
        |---------|-------------|--------|----------|
        #{partner_rows.join("\n")}
      SECTION
    end

    def group_by_campaign(data)
      grouped = {}
      data.each do |spend|
        campaign_name = spend.campaign&.display_name || "Unknown Campaign"
        grouped[campaign_name] ||= []
        grouped[campaign_name] << spend
      end
      grouped.sort_by { |name, data| -calculate_totals(data)[:gross_spend] }.to_h
    end

    def group_by_partner(data)
      grouped = {}
      data.each do |spend|
        partner_name = spend.partner&.name || "Unknown Partner"
        grouped[partner_name] ||= []
        grouped[partner_name] << spend
      end
      grouped.sort_by { |name, data| -calculate_totals(data)[:gross_spend] }.to_h
    end

    def group_by_country(data)
      grouped = {}
      data.each do |spend|
        country_code = spend.campaign&.country_code || "Unknown"
        grouped[country_code] ||= []
        grouped[country_code] << spend
      end
      grouped.sort_by { |code, data| -calculate_totals(data)[:gross_spend] }.to_h
    end

    def group_by_period(data, period_type)
      grouped = {}

      data.each do |spend|
        key = case period_type
        when :month
                spend.spend_date.beginning_of_month
        when :week
                spend.spend_date.beginning_of_week
        when :day
                spend.spend_date
        end

        grouped[key] ||= []
        grouped[key] << spend
      end

      grouped.sort.to_h
    end

    def calculate_totals(data)
      return default_totals if data.empty?

      total_gross = data.sum { |d| d.gross_spend || 0 }
      total_net = data.sum { |d| d.adjusted_net_spend || 0 }
      total_revenue = data.sum { |d| d.revenue || 0 }
      total_impressions = data.sum { |d| d.impression_count || 0 }
      total_clicks = data.sum { |d| d.click_count || 0 }
      total_installs = data.sum { |d| d.install_count || 0 }

      avg_margin = total_gross > 0 ? ((total_gross - total_net) / total_gross * 100).round(1) : 0
      avg_cvr = total_clicks > 0 ? (total_installs.to_f / total_clicks * 100).round(2) : 0

      {
        gross_spend: total_gross,
        net_spend: total_net,
        revenue: total_revenue,
        impressions: total_impressions,
        clicks: total_clicks,
        installs: total_installs,
        avg_margin: avg_margin,
        avg_cvr: avg_cvr
      }
    end

    def default_totals
      {
        gross_spend: 0,
        net_spend: 0,
        revenue: 0,
        impressions: 0,
        clicks: 0,
        installs: 0,
        avg_margin: 0,
        avg_cvr: 0
      }
    end
  end
end
