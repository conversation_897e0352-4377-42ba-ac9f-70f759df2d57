module ContextBuilder
  module Client
    class BudgetsBuilder
      include BuilderUtilities

      def initialize(client, start_date: 1.year.ago.beginning_of_year)
        @start_date = start_date.to_date
        @client = client
      end

      def build
        budgets = fetch_recent_budgets
        return nil if budgets.empty?

        build_budget_summary(budgets)
      end

      private

      attr_reader :client

      def fetch_recent_budgets
        client.client_budgets
              .includes(sub_client_budgets: :campaigns)
              .where("start_date >= ?", @start_date)
              .order(:start_date)
      end

      def build_budget_summary(budgets)
        budget_sections = budgets.map do |budget|
          budget_section = build_main_budget_row(budget)

          if budget.sub_client_budgets.any?
            sub_budget_rows = budget.sub_client_budgets.map do |sub_budget|
              build_sub_budget_row(sub_budget)
            end
            budget_section + "\n" + sub_budget_rows.join("\n")
          else
            budget_section
          end
        end

        <<~SECTION
          ## Client Budgets

          | Amount | Start Date | End Date | Sub-Budget | Campaigns |
          |--------|------------|----------|------------|-----------|
          #{budget_sections.join("\n")}
        SECTION
      end

      def build_main_budget_row(budget)
        "| #{format_currency(budget.amount.to_i)} | #{budget.start_date} | #{budget.end_date} | - | - |"
      end

      def build_sub_budget_row(sub_budget)
        campaign_info = if sub_budget.campaigns.any?
          campaign_ids = sub_budget.campaigns.map do |campaign|
            campaign.legacy_id
          end.join(", ")
          "Campaign ids: #{campaign_ids}"
        else
          "No campaigns"
        end
        "| Sub budget | - | - | #{sub_budget.name} (Planned amount #{format_currency(sub_budget.amount.to_i)}) | #{campaign_info} |"
      end
    end
  end
end
