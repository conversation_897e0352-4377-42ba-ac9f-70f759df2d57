module ContextBuilder
  class ClientBuilder
    include BuilderUtilities

    def initialize(client, context_settings = {})
      @client = client
      @include_activities = context_settings.fetch(:include_activities, false)
      @start_date = context_settings.fetch(:context_start_date, 1.months.ago.beginning_of_month).to_date
      @end_date = context_settings.fetch(:context_end_date, Date.current.end_of_month).to_date
    end

    def build
      return "Client not found" unless @client

      sections = [
        Client::BasicInfoBuilder.new(@client).build,
        Client::DetailsBuilder.new(@client).build,
        Client::BudgetsBuilder.new(@client, start_date: @start_date).build,
        MobileAppsBuilder.new(@client.mobile_apps.active_within(@start_date, @end_date)).build,
        CampaignPerformanceBuilder.new(@client, start_date: @start_date, end_date: @end_date).build
      ]

      if @include_activities
        sections << SlackActivityBuilder.new(@client.slack_messages.recent_within(@start_date, @end_date)).build
        sections << GongActivityBuilder.new(@client.gong_calls.recent_within(@start_date, @end_date)).build
        sections << FirefliesActivityBuilder.new(@client.fireflies_meetings.recent_within(@start_date, @end_date)).build
      end

      sections.compact.join("\n\n")
    end

    private

    attr_reader :client, :context_settings
  end
end
