module ContextBuilder
  class PartnerBuilder
    include BuilderUtilities

    def initialize(partner, context_settings = {})
      @partner = partner
      @include_activities = context_settings.fetch(:include_activities, false)
      @start_date = context_settings.fetch(:context_start_date, 1.months.ago.beginning_of_month).to_date
      @end_date = context_settings.fetch(:context_end_date, Date.current.end_of_month).to_date
    end

    def build
      return "Partner not found" unless @partner

      sections = [
        Partner::BasicInfoBuilder.new(@partner).build,
        Partner::DetailsBuilder.new(@partner).build
      ]

      if @include_activities
        sections << SlackActivityBuilder.new(@partner.slack_messages.recent_within(@start_date, @end_date)).build
        sections << GongActivityBuilder.new(@partner.gong_calls.recent_within(@start_date, @end_date)).build
        sections << FirefliesActivityBuilder.new(@partner.fireflies_meetings.recent_within(@start_date, @end_date)).build
      end

      sections.compact.join("\n\n")
    end

    private

    attr_reader :partner, :context_settings
  end
end
