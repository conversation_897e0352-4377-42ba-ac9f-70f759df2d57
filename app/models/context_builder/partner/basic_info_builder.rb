module ContextBuilder
  module Partner
    class BasicInfoBuilder
      include BuilderUtilities

      def initialize(partner)
        @partner = partner
      end

      def build
        <<~MARKDOWN
          # #{partner.name}

          | Field | Value |
          |-------|-------|
          | ID | #{partner.legacy_id || 'N/A'} |
          | Website | #{partner.website || 'N/A'} |
          | Headline | #{partner.headline || 'N/A'} |
          | Main Category | #{partner.main_category&.name || 'N/A'} |
          | Description | #{partner.description} |
        MARKDOWN
      end

      private

      attr_reader :partner
    end
  end
end
