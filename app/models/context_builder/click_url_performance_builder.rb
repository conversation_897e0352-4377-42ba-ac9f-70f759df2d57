module ContextBuilder
  class ClickUrlPerformanceBuilder
    include BuilderUtilities

    def initialize(click_url, start_date: 1.year.ago.beginning_of_year, end_date:  Date.current)
      @click_url = click_url
      @start_date = start_date.to_date
      @end_date = end_date.to_date
    end

    def build
      campaign_data = fetch_campaign_data

      return nil if campaign_data.empty?

      # Check if total gross spend meets minimum threshold
      totals = calculate_totals(campaign_data)
      return nil if totals[:gross_spend] < 5

      sections = []
      sections << build_overall_summary(campaign_data)
      sections << build_monthly_breakdown(campaign_data)
      sections << build_weekly_breakdown(campaign_data)
      sections << build_daily_breakdown(campaign_data)

      sections.join("\n")
    end

    private

    attr_reader :click_url

    def fetch_campaign_data
      query_params = {
        date_lteq: @end_date.to_s,
        date_gteq: @start_date.to_s,
        legacy_click_url_id_in: [ click_url.legacy_id ],
        sort_by: "date_asc"
      }

      CampaignSpendsQuery.new(query_params).results
        .includes(:campaign, :partner, :click_url)
    end

    def build_overall_summary(campaign_data)
      return "" if campaign_data.empty?

      totals = calculate_totals(campaign_data)
      first_spend = campaign_data.first&.spend_date
      last_spend = campaign_data.last&.spend_date
      days_active = campaign_data.map(&:spend_date).uniq.count

      # Extract campaign, vendor, and mobile app info through click_url associations
      campaign = click_url.campaign
      vendor = click_url.partner
      client = click_url.client
      mobile_app = campaign&.mobile_app

      campaign_name = campaign&.display_name || "N/A"
      vendor_name = vendor&.name || "N/A"
      client_name = client&.name || "N/A"
      mobile_app_name = mobile_app&.name || "N/A"
      platform = mobile_app&.platform || "N/A"
      country_code = campaign&.country_code || "N/A"
      legacy_id = click_url.legacy_id || "N/A"

      <<~SECTION
        ## Overall Performance Summary for Click URL ID: #{legacy_id}, Campaign ID: #{campaign&.legacy_id}

        **Analysis Period:** #{@start_date.strftime('%b %d, %Y')} - #{@end_date.strftime('%b %d, %Y')}

        | Metric | Value |
        |--------|-------|
        | Client Name | #{client_name} |
        | Campaign Name | #{campaign_name} |
        | Vendor Name | #{vendor_name} |
        | Mobile App | #{mobile_app_name} |
        | Platform | #{platform} |
        | Country Code | #{country_code} |
        | Total Gross Spend | $#{format_currency(totals[:gross_spend])} |
        | Total Net Spend | $#{format_currency(totals[:net_spend])} |
        | Total Revenue | $#{format_currency(totals[:revenue])} |
        | Average Margin | #{totals[:avg_margin]}% |
        | Total Impressions | #{totals[:impressions]} |
        | Total Clicks | #{totals[:clicks]} |
        | Total Installs | #{totals[:installs]} |
        | Average CVR | #{totals[:avg_cvr]}% |
        | First Spend Date | #{first_spend&.strftime('%b %d, %Y') || 'N/A'} |
        | Last Spend Date | #{last_spend&.strftime('%b %d, %Y') || 'N/A'} |
        | Days Active | #{days_active} |
        | Avg Daily Spend | $#{format_currency(totals[:gross_spend] / [ days_active, 1 ].max)} |
      SECTION
    end

    def build_monthly_breakdown(campaign_data)
      monthly_data = group_by_period(campaign_data, :month)
      return "" if monthly_data.empty?

      monthly_rows = monthly_data.map do |period, data|
        totals = calculate_totals(data)
        days_in_month = data.map(&:spend_date).uniq.count

        "| #{period.strftime('%B %Y')} | $#{format_currency(totals[:gross_spend])} | $#{format_currency(totals[:revenue])} | #{totals[:avg_margin]}% | #{totals[:impressions]} | #{totals[:clicks]} | #{totals[:installs]} | #{totals[:avg_cvr]}% | #{days_in_month} |"
      end

      <<~SECTION
        ## Monthly Breakdown

        | Month | Gross Spend | Revenue | Margin | Impressions | Clicks | Installs | CVR | Active Days |
        |-------|-------------|---------|--------|-------------|--------|----------|-----|-------------|
        #{monthly_rows.join("\n")}
      SECTION
    end

    def build_weekly_breakdown(campaign_data)
      # Only show last 12 weeks to keep it manageable
      recent_data = campaign_data.select { |d| d.spend_date >= (@end_date - 12.weeks).to_date }
      weekly_data = group_by_period(recent_data, :week)
      return "" if weekly_data.empty?

      weekly_rows = weekly_data.map do |period, data|
        totals = calculate_totals(data)
        week_start = period.beginning_of_week.strftime("%b %d")
        week_end = period.end_of_week.strftime("%b %d")
        days_active = data.map(&:spend_date).uniq.count

        "| #{week_start} - #{week_end} | $#{format_currency(totals[:gross_spend])} | $#{format_currency(totals[:revenue])} | #{totals[:avg_margin]}% | #{totals[:impressions]} | #{totals[:clicks]} | #{totals[:installs]} | #{totals[:avg_cvr]}% | #{days_active} |"
      end

      <<~SECTION
        ## Weekly Breakdown (Last 12 Weeks)

        | Week | Gross Spend | Revenue | Margin | Impressions | Clicks | Installs | CVR | Active Days |
        |------|-------------|---------|--------|-------------|--------|----------|-----|-------------|
        #{weekly_rows.join("\n")}
      SECTION
    end

    def build_daily_breakdown(campaign_data)
      # Only show last 35 days to keep it manageable
      recent_data = campaign_data.select { |d| d.spend_date >= (@end_date - 35.days).to_date }
      daily_data = group_by_period(recent_data, :day)
      return "" if daily_data.empty?

      daily_rows = daily_data.map do |date, data|
        totals = calculate_totals(data)
        day_label = date.strftime("%a, %b %d")

        "| #{day_label} | $#{format_currency(totals[:gross_spend])} | $#{format_currency(totals[:revenue])} | #{totals[:avg_margin]}% | #{totals[:impressions]} | #{totals[:clicks]} | #{totals[:installs]} | #{totals[:avg_cvr]}% |"
      end

      <<~SECTION
        ## Daily Breakdown (Last 35 Days)

        | Date | Gross Spend | Revenue | Margin | Impressions | Clicks | Installs | CVR |
        |------|-------------|---------|--------|-------------|--------|----------|-----|
        #{daily_rows.join("\n")}
      SECTION
    end

    def group_by_period(data, period_type)
      grouped = {}

      data.each do |spend|
        key = case period_type
        when :month
                spend.spend_date.beginning_of_month
        when :week
                spend.spend_date.beginning_of_week
        when :day
                spend.spend_date
        end

        grouped[key] ||= []
        grouped[key] << spend
      end

      grouped.sort.to_h
    end

    def calculate_totals(data)
      return default_totals if data.empty?

      total_gross = data.sum { |d| d.gross_spend || 0 }
      total_net = data.sum { |d| d.adjusted_net_spend || 0 }
      total_revenue = data.sum { |d| d.revenue || 0 }
      total_impressions = data.sum { |d| d.impression_count || 0 }
      total_clicks = data.sum { |d| d.click_count || 0 }
      total_installs = data.sum { |d| d.install_count || 0 }

      avg_margin = total_gross > 0 ? ((total_gross - total_net) / total_gross * 100).round(1) : 0
      avg_cvr = total_clicks > 0 ? (total_installs.to_f / total_clicks * 100).round(2) : 0

      {
        gross_spend: total_gross,
        net_spend: total_net,
        revenue: total_revenue,
        impressions: total_impressions,
        clicks: total_clicks,
        installs: total_installs,
        avg_margin: avg_margin,
        avg_cvr: avg_cvr
      }
    end

    def default_totals
      {
        gross_spend: 0,
        net_spend: 0,
        revenue: 0,
        impressions: 0,
        clicks: 0,
        installs: 0,
        avg_margin: 0,
        avg_cvr: 0
      }
    end
  end
end
