class ClickUrl < ApplicationRecord
  belongs_to :campaign
  belongs_to :track_party
  belongs_to :partner

  delegate :client, to: :campaign
  delegate :mobile_app, to: :campaign

  has_many :event_aggregates, dependent: :destroy
  has_many :partner_spends, dependent: :destroy
  has_many :campaign_spends, dependent: :destroy
  has_many :client_pricings, dependent: :destroy
  has_many :partner_pricings, dependent: :destroy

  enum :status, archived: "archived", deleted: "deleted", normal: "normal", active: "active", paused: "paused"

  scope :active, -> { where(active: true) }
  scope :active_within, ->(start_date, end_date) {
    DailyCampaignSpend.where(
      "spend_date >= ? AND spend_date <= ?", start_date, end_date
    ).distinct.pluck(:legacy_click_url_id).then do |click_url_ids|
      where(legacy_id: click_url_ids)
    end
  }
  scope :visible, -> { where.not(status: %i[archived deleted]) }
end
