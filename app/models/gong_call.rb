class GongCall < ApplicationRecord
  belongs_to :client, optional: true
  belongs_to :partner, optional: true
  has_many :gong_transcripts, dependent: :destroy

  validates :gong_id, presence: true, uniqueness: true

  scope :recent, -> { where("started_at >= ?", 1.month.ago) }
  scope :recent_within, ->(start_date, end_date) {
    where("started_at >= ? AND started_at <= ?", start_date, end_date)
  }

  def display_name
    "Gong Call ##{id} - #{started_at.strftime('%Y-%m-%d %H:%M:%S')}"
  end

  def duration_in_seconds
    duration || 0
  end

  def duration_in_minutes
    duration ? duration / 60 : 0
  end

  def has_transcript?
    gong_transcripts.exists?
  end

  def has_client?
    client.present?
  end

  def has_partner?
    partner.present?
  end
end
