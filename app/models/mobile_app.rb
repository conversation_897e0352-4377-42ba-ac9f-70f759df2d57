class MobileApp < ApplicationRecord
  audited

  belongs_to :client
  belongs_to :main_category, class_name: "MobileAppCategory", optional: true

  has_many :mobile_app_categorizations, dependent: :destroy
  has_many :mobile_app_categories, through: :mobile_app_categorizations
  has_many :mobile_app_metrics, dependent: :destroy

  has_one_attached :logo

  has_associated_audits

  scope :active, -> { where(active: true) }
  scope :active_within, ->(start_date, end_date) {
    Campaign.active_within(start_date, end_date).pluck(:mobile_app_id).then do |app_ids|
      where(id: app_ids)
    end
  }
end
