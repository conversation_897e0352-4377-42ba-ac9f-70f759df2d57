class SlackMessage < ApplicationRecord
  belongs_to :user
  belongs_to :client, optional: true
  belongs_to :partner, optional: true

  validates :channel_id, presence: true
  validates :message_ts, presence: true
  validates :message_text, presence: true

  scope :with_client, -> { where.not(client_id: nil) }
  scope :with_partner, -> { where.not(partner_id: nil) }
  scope :recent, -> { where("message_time >= ?", 1.month.ago) }
  scope :recent_within, ->(start_date, end_date) {
    where("message_time >= ? AND message_time <= ?", start_date, end_date)
  }

  before_create :set_permalink

  private

  def set_permalink
    self.permalink = SlackClient.get_permalink(channel_id, message_ts)
  end
end
