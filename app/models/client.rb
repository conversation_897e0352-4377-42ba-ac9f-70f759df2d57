class Client < ApplicationRecord
  audited
  include PgSearch::Model

  # Direct associations (alphabetical)
  has_many :api_playgrounds, dependent: :nullify
  has_many :attribution_windows, dependent: :destroy
  has_many :campaign_monthly_spend_summaries, foreign_key: "legacy_client_id", primary_key: "legacy_id", dependent: :destroy
  has_many :campaign_spend_summaries, foreign_key: "legacy_client_id", primary_key: "legacy_id", dependent: :destroy
  has_many :campaign_spends, dependent: :destroy
  has_many :campaigns, dependent: :destroy
  has_many :client_budgets, dependent: :destroy
  has_many :client_creatives, dependent: :destroy
  has_many :client_direct_partners, dependent: :destroy
  has_many :client_documents, dependent: :destroy
  has_many :client_events, dependent: :destroy
  has_many :client_fraud_platforms, dependent: :destroy
  has_many :client_interests, dependent: :destroy
  has_many :client_reports, dependent: :destroy
  has_many :client_social_media_links, dependent: :destroy
  has_many :client_track_parties, dependent: :destroy
  has_many :conversation_contexts, as: :contextual, dependent: :destroy
  has_many :conversion_funnels, dependent: :destroy
  has_many :demographic_segments, dependent: :destroy
  has_many :event_mappings, dependent: :destroy
  has_many :gong_calls, dependent: :nullify
  has_many :mobile_apps, dependent: :destroy
  has_many :monthly_campaign_spend_adjustments, dependent: :destroy
  has_many :partner_integrations, dependent: :destroy
  has_many :slack_messages, dependent: :nullify
  has_many :target_kpis, dependent: :destroy
  has_many :track_party_integrations, dependent: :destroy
  has_many :fireflies_meetings, dependent: :nullify

  # Through associations (alphabetical)
  has_many :click_urls, through: :campaigns
  has_many :conversations, through: :conversation_contexts
  has_many :direct_partners, through: :client_direct_partners, source: :partner
  has_many :fraud_platforms, through: :client_fraud_platforms
  has_many :track_parties, through: :client_track_parties

  # Has one associations (alphabetical)
  has_one :client_deal_score, dependent: :destroy
  has_one :client_detail, dependent: :destroy

  has_one_attached :logo

  enum :preferred_logo_background, [ :logo_bg_light, :logo_bg_dark ]

  pg_search_scope :search_by_name, against: :name, using: { tsearch: { prefix: true, any_word: true } }

  has_associated_audits

  accepts_nested_attributes_for :client_detail, allow_destroy: true
  accepts_nested_attributes_for :client_budgets, allow_destroy: true
  accepts_nested_attributes_for :client_events, allow_destroy: true
  accepts_nested_attributes_for :client_creatives, allow_destroy: true
  accepts_nested_attributes_for :client_interests, allow_destroy: true
  accepts_nested_attributes_for :client_deal_score, allow_destroy: true, reject_if: :all_blank
  accepts_nested_attributes_for :client_direct_partners, allow_destroy: true
  accepts_nested_attributes_for :client_social_media_links, allow_destroy: true
  accepts_nested_attributes_for :client_social_media_links, allow_destroy: true
  accepts_nested_attributes_for :client_track_parties, allow_destroy: true
  accepts_nested_attributes_for :client_documents, allow_destroy: true
  accepts_nested_attributes_for :attribution_windows, allow_destroy: true
  accepts_nested_attributes_for :client_fraud_platforms, allow_destroy: true
  accepts_nested_attributes_for :client_reports, allow_destroy: true

  scope :active, -> { where(active: true, is_test: false) }

  # Budget usage calculation methods
  def budget_usage_summary(months_back: 6)
    end_date = Date.current.end_of_month
    start_date = end_date - months_back.months + 1.day

    # Get budgets within the date range
    budgets = client_budgets.where(
      "(start_date <= ? AND end_date >= ?) OR (start_date >= ? AND start_date <= ?)",
      end_date, start_date, start_date, end_date
    )

    # Get monthly spend data for this client
    monthly_spends = MonthlyCampaignSpend.where(
      legacy_client_id: legacy_id,
      month_date: start_date..end_date
    )

    # Calculate total budget amount
    total_budget = budgets.sum(:amount)

    # Calculate total spent amount (using adjusted_net_spend)
    total_spent = monthly_spends.sum(:adjusted_net_spend) || 0

    # Calculate usage percentage
    usage_percentage = total_budget > 0 ? (total_spent / total_budget * 100).round(2) : 0

    {
      total_budget: total_budget,
      total_spent: total_spent,
      remaining_budget: total_budget - total_spent,
      usage_percentage: usage_percentage,
      period_start: start_date,
      period_end: end_date,
      budgets_count: budgets.count,
      monthly_breakdown: monthly_breakdown(monthly_spends)
    }
  end

  def budget_trend_data(months_back: 6)
    end_date = Date.current.end_of_month
    start_date = end_date - months_back.months + 1.day

    monthly_spends = MonthlyCampaignSpend.where(
      legacy_client_id: legacy_id,
      month_date: start_date..end_date
    ).group(:month_date)
     .sum(:adjusted_net_spend)

    # Fill in missing months with 0
    (start_date.to_date..end_date.to_date).map(&:beginning_of_month).uniq.map do |month|
      [ month.strftime("%Y-%m"), monthly_spends[month] || 0 ]
    end.to_h
  end

  def current_month_daily_spends
    current_month_start = Date.current.beginning_of_month
    current_month_end = Date.current.end_of_month

    # Get daily spend data for current month
    daily_spends = campaign_spend_summaries.where(
      spend_date: current_month_start..Date.current
    ).group(:spend_date)
     .sum(:adjusted_net_spend)

    # Fill in missing days with 0
    (current_month_start..Date.current).map do |date|
      [ date.strftime("%Y-%m-%d"), daily_spends[date] || 0 ]
    end.to_h
  end

  def predict_month_end_spend
    daily_spends = current_month_daily_spends
    return 0 if daily_spends.empty?

    current_month_total = daily_spends.values.sum
    days_passed = Date.current.day
    days_in_month = Date.current.end_of_month.day

    # Simple linear prediction based on average daily spend
    if days_passed > 0
      average_daily_spend = current_month_total / days_passed
      predicted_total = average_daily_spend * days_in_month
      {
        current_total: current_month_total,
        predicted_total: predicted_total.round(2),
        average_daily: average_daily_spend.round(2),
        days_remaining: days_in_month - days_passed
      }
    else
      {
        current_total: 0,
        predicted_total: 0,
        average_daily: 0,
        days_remaining: days_in_month
      }
    end
  end

  private

  def monthly_breakdown(monthly_spends)
    monthly_spends.group(:month_date).sum(:adjusted_net_spend).map do |month, amount|
      {
        month: month.strftime("%Y-%m"),
        amount: amount,
        formatted_month: month.strftime("%b %Y")
      }
    end
  end
end
