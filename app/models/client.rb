class Client < ApplicationRecord
  audited
  include PgSearch::Model

  # Direct associations (alphabetical)
  has_many :api_playgrounds, dependent: :nullify
  has_many :attribution_windows, dependent: :destroy
  has_many :campaign_monthly_spend_summaries, foreign_key: "legacy_client_id", primary_key: "legacy_id", dependent: :destroy
  has_many :campaign_spend_summaries, foreign_key: "legacy_client_id", primary_key: "legacy_id", dependent: :destroy
  has_many :campaign_spends, dependent: :destroy
  has_many :campaigns, dependent: :destroy
  has_many :client_budgets, dependent: :destroy
  has_many :client_creatives, dependent: :destroy
  has_many :client_direct_partners, dependent: :destroy
  has_many :client_documents, dependent: :destroy
  has_many :client_events, dependent: :destroy
  has_many :client_fraud_platforms, dependent: :destroy
  has_many :client_interests, dependent: :destroy
  has_many :client_reports, dependent: :destroy
  has_many :client_social_media_links, dependent: :destroy
  has_many :client_track_parties, dependent: :destroy
  has_many :conversation_contexts, as: :contextual, dependent: :destroy
  has_many :conversion_funnels, dependent: :destroy
  has_many :demographic_segments, dependent: :destroy
  has_many :event_mappings, dependent: :destroy
  has_many :gong_calls, dependent: :nullify
  has_many :mobile_apps, dependent: :destroy
  has_many :monthly_campaign_spend_adjustments, dependent: :destroy
  has_many :partner_integrations, dependent: :destroy
  has_many :slack_messages, dependent: :nullify
  has_many :target_kpis, dependent: :destroy
  has_many :track_party_integrations, dependent: :destroy
  has_many :fireflies_meetings, dependent: :nullify

  # Through associations (alphabetical)
  has_many :click_urls, through: :campaigns
  has_many :conversations, through: :conversation_contexts
  has_many :direct_partners, through: :client_direct_partners, source: :partner
  has_many :fraud_platforms, through: :client_fraud_platforms
  has_many :track_parties, through: :client_track_parties

  # Has one associations (alphabetical)
  has_one :client_deal_score, dependent: :destroy
  has_one :client_detail, dependent: :destroy

  has_one_attached :logo

  enum :preferred_logo_background, [ :logo_bg_light, :logo_bg_dark ]

  pg_search_scope :search_by_name, against: :name, using: { tsearch: { prefix: true, any_word: true } }

  has_associated_audits

  accepts_nested_attributes_for :client_detail, allow_destroy: true
  accepts_nested_attributes_for :client_budgets, allow_destroy: true
  accepts_nested_attributes_for :client_events, allow_destroy: true
  accepts_nested_attributes_for :client_creatives, allow_destroy: true
  accepts_nested_attributes_for :client_interests, allow_destroy: true
  accepts_nested_attributes_for :client_deal_score, allow_destroy: true, reject_if: :all_blank
  accepts_nested_attributes_for :client_direct_partners, allow_destroy: true
  accepts_nested_attributes_for :client_social_media_links, allow_destroy: true
  accepts_nested_attributes_for :client_social_media_links, allow_destroy: true
  accepts_nested_attributes_for :client_track_parties, allow_destroy: true
  accepts_nested_attributes_for :client_documents, allow_destroy: true
  accepts_nested_attributes_for :attribution_windows, allow_destroy: true
  accepts_nested_attributes_for :client_fraud_platforms, allow_destroy: true
  accepts_nested_attributes_for :client_reports, allow_destroy: true

  scope :active, -> { where(active: true, is_test: false) }

  # Budget usage calculation methods
  def budget_usage_summary(months_back: 6, start_date: nil, end_date: nil)
    if start_date && end_date
      start_date = start_date.to_date
      end_date = end_date.to_date
    else
      # Default to data up to yesterday, starting from months_back months ago
      end_date = Date.current - 1.day # Yesterday
      start_date = (Date.current - months_back.months).beginning_of_month # First day of months_back months ago
    end

    # Get budgets within the date range
    budgets = client_budgets.where(
      "(start_date <= ? AND end_date >= ?) OR (start_date >= ? AND start_date <= ?)",
      end_date, start_date, start_date, end_date
    )

    # Get monthly spend data for this client
    monthly_spends = CampaignMonthlySpendSummary.where(
      legacy_client_id: legacy_id,
      month_date: start_date..end_date
    )

    # Debug information
    Rails.logger.debug "=== DEBUG budget_usage_summary ==="
    Rails.logger.debug "Client legacy_id: #{legacy_id}"
    Rails.logger.debug "Date range: #{start_date} to #{end_date}"
    Rails.logger.debug "Found #{monthly_spends.count} monthly spend records"
    Rails.logger.debug "Monthly spends: #{monthly_spends.pluck(:month_date, :adjusted_net_spend)}"

    # Calculate total budget amount
    total_budget = budgets.sum(:amount)

    # Calculate total spent amount (using adjusted_net_spend)
    total_spent = monthly_spends.sum(:adjusted_net_spend) || 0

    Rails.logger.debug "Total budget: #{total_budget}"
    Rails.logger.debug "Total spent: #{total_spent}"

    # Calculate usage percentage
    usage_percentage = total_budget > 0 ? (total_spent / total_budget * 100).round(2) : 0

    {
      total_budget: total_budget,
      total_spent: total_spent,
      remaining_budget: total_budget - total_spent,
      usage_percentage: usage_percentage,
      period_start: start_date,
      period_end: end_date,
      budgets_count: budgets.count,
      monthly_breakdown: monthly_breakdown(monthly_spends)
    }
  end



  def monthly_budget_spend_breakdown(months_back: 6, start_date: nil, end_date: nil)
    if start_date && end_date
      start_date = start_date.to_date
      end_date = end_date.to_date
    else
      # Default to data up to yesterday, starting from months_back months ago
      end_date = Date.current - 1.day # Yesterday
      start_date = (Date.current - months_back.months).beginning_of_month # First day of months_back months ago
    end

    # Get all months in the range
    months_range = (start_date.to_date..end_date.to_date).map(&:beginning_of_month).uniq

    # Get monthly spend data
    monthly_spends = CampaignMonthlySpendSummary.where(
      legacy_client_id: legacy_id,
      month_date: start_date..end_date
    )

    # Get budgets within the date range
    budgets = client_budgets.where(
      "(start_date <= ? AND end_date >= ?) OR (start_date >= ? AND start_date <= ?)",
      end_date, start_date, start_date, end_date
    )



    # Build monthly breakdown
    result = months_range.map do |month|
      month_key = month.strftime("%Y-%m")
      month_label = month.strftime("%b %Y")

      # Get spend data for this month
      month_spends = monthly_spends.where(month_date: month)
      total_spend = month_spends.sum(:adjusted_net_spend) || 0

      # Get partner breakdown for this month
      partner_breakdown = get_monthly_partner_breakdown(month_spends)

      # Calculate budget for this specific month
      monthly_budget = calculate_monthly_budget_for_month(month, budgets)

      {
        month: month_key,
        month_label: month_label,
        budget: monthly_budget.round(2),
        total_spend: total_spend.round(2),
        partner_breakdown: partner_breakdown
      }
    end

    result
  end

  def monthly_budget_chart_data(months_back: 6, start_date: nil, end_date: nil)
    monthly_breakdown = monthly_budget_spend_breakdown(months_back: months_back, start_date: start_date, end_date: end_date)

    # Prepare chart data with two bars per month
    chart_data = []

    # Partner spend data (stacked bars) - first bar for each month
    all_partners = monthly_breakdown.flat_map { |month| month[:partner_breakdown] }
                                   .map { |p| p[:partner_name] }
                                   .uniq

    partner_colors = [ "#3B82F6", "#10B981", "#F59E0B", "#EF4444", "#8B5CF6", "#F97316", "#06B6D4", "#84CC16" ]

    all_partners.each_with_index do |partner_name, index|
      partner_data = monthly_breakdown.map do |month|
        partner_spend = month[:partner_breakdown].find { |p| p[:partner_name] == partner_name }
        [ month[:month_label], partner_spend ? partner_spend[:spend_amount] : 0 ]
      end
      chart_data << {
        name: partner_name,
        data: partner_data,
        color: partner_colors[index % partner_colors.length],
        stack: "partner_breakdown"
      }
    end

    # Unused budget (remaining budget after spend) - part of first bar
    unused_budget_data = monthly_breakdown.map do |month|
      unused = [ month[:budget] - month[:total_spend], 0 ].max
      [ month[:month_label], unused ]
    end
    chart_data << {
      name: "Unused Budget",
      data: unused_budget_data,
      color: "#A7F3D0",  # Light green - 表示剩余/可用
      stack: "partner_breakdown"
    }

    # Total Spent - bottom part of second bar
    total_spent_data = monthly_breakdown.map do |month|
      [ month[:month_label], month[:total_spend] ]
    end
    chart_data << {
      name: "Total Spent",
      data: total_spent_data,
      color: "#FCA5A5",  # Light red - 表示已花费
      stack: "partner_breakdown"
    }

    # Remaining Budget - top part of second bar (budget - spent)
    monthly_budget_data = monthly_breakdown.map do |month|
      [ month[:month_label], month[:budget] ]
    end
    chart_data << {
      name: "Total Budget",
      data: monthly_budget_data,
      color: "#DBEAFE",  # Light blue - 表示剩余预算
      stack: "partner_breakdown"
    }

    # Add prediction data for current month if available
    prediction_data = predict_month_end_spend
    current_month_label = Date.current.strftime("%b %Y")

    # Check if current month is in the chart data
    if monthly_breakdown.any? { |month| month[:month_label] == current_month_label } &&
       prediction_data[:predicted_total].present? && prediction_data[:predicted_total] > 0

      # Add predicted spend data series
      predicted_spend_data = monthly_breakdown.map do |month|
        if month[:month_label] == current_month_label
          [ month[:month_label], prediction_data[:predicted_total] ]
        else
          [ month[:month_label], 0 ]
        end
      end

      chart_data << {
         name: "Predicted Spend (#{prediction_data[:confidence]} Confidence)",
         data: predicted_spend_data,
         color: "#FDE047",  # Yellow - 表示预测数据
         stack: "partner_breakdown"
       }
    end

    # Return chart data
    {
      data: chart_data,
      budget_labels: monthly_breakdown.map { |month| month[:budget].round(0) },
      prediction_data: prediction_data
    }
  end



  def predict_month_end_spend
    # Get current month data
    current_month_start = Date.current.beginning_of_month
    current_month_spend = CampaignMonthlySpendSummary.where(
      legacy_client_id: legacy_id,
      month_date: current_month_start
    ).sum(:adjusted_net_spend)

    days_passed = Date.current.day
    days_in_month = Date.current.end_of_month.day

    # 前五天不进行预测
    if days_passed <= 5
      return {
        current_total: current_month_spend,
        predicted_total: nil,
        average_daily: nil,
        days_remaining: days_in_month - days_passed,
        prediction_method: "No prediction (Early month)",
        confidence: "N/A",
        recent_trend: []
      }
    end

    # 五天后，只基于当月数据进行线性预测
    if current_month_spend > 0 && days_passed > 5
      daily_rate = current_month_spend / days_passed
      predicted_total = (daily_rate * days_in_month).round(2)

      # 根据已过天数确定置信度
      confidence = if days_passed >= 20
        "High"
      elsif days_passed >= 10
        "Medium"
      else
        "Low"
      end

      {
        current_total: current_month_spend,
        predicted_total: predicted_total,
        average_daily: daily_rate.round(2),
        days_remaining: days_in_month - days_passed,
        prediction_method: "Current Month Linear",
        confidence: confidence,
        recent_trend: []
      }
    else
      # 如果当月没有花费数据
      {
        current_total: current_month_spend,
        predicted_total: 0,
        average_daily: 0,
        days_remaining: days_in_month - days_passed,
        prediction_method: "No spend data",
        confidence: "Low",
        recent_trend: []
      }
    end
  end



  private

  def calculate_monthly_budget_for_month(month, budgets)
    month_start = month.beginning_of_month
    month_end = month.end_of_month

    total_budget_for_month = 0

    budgets.each do |budget|
      # Find overlap between budget period and this month
      overlap_start = [ budget.start_date, month_start ].max
      overlap_end = [ budget.end_date, month_end ].min

      if overlap_start <= overlap_end
        # Calculate how much of this budget applies to this month
        budget_total_days = (budget.end_date - budget.start_date + 1).to_i
        overlap_days = (overlap_end - overlap_start + 1).to_i

        monthly_allocation = (budget.amount * overlap_days / budget_total_days)
        total_budget_for_month += monthly_allocation
      end
    end

    total_budget_for_month
  end



  def get_monthly_partner_breakdown(month_spends)
    return [] if month_spends.empty?

    # Group by partner and calculate totals
    partner_totals = month_spends.group(:legacy_partner_id)
                                .sum(:adjusted_net_spend)

    total_spend = partner_totals.values.sum
    return [] if total_spend == 0

    # Build breakdown with partner details
    partner_totals.map do |partner_id, spend_amount|
      partner = Partner.find_by(legacy_id: partner_id)
      next unless partner

      {
        partner_id: partner_id,
        partner_name: partner.name,
        spend_amount: spend_amount.round(2),
        percentage: ((spend_amount / total_spend) * 100).round(2)
      }
    end.compact.sort_by { |item| -item[:spend_amount] }
  end







  private

  def monthly_breakdown(monthly_spends)
    # Get same month from previous years (up to 3 years)
    historical_months = []

    (1..3).each do |years_back|
      target_date = Date.current - years_back.years
      month_start = target_date.beginning_of_month

      # Get monthly spend for this historical month
      historical_spend = CampaignMonthlySpendSummary.where(
        legacy_client_id: legacy_id,
        month_date: month_start
      ).sum(:adjusted_net_spend)

      if historical_spend > 0
        historical_months << {
          year: target_date.year,
          month: target_date.month,
          total_spend: historical_spend
        }
      end
    end

    if historical_months.any?
      # Calculate average historical spend for this month
      avg_historical = historical_months.sum { |m| m[:total_spend] } / historical_months.length

      # Adjust for current month progress
      days_passed = Date.current.day
      days_in_month = Date.current.end_of_month.day
      progress_ratio = days_passed.to_f / days_in_month

      # Predict based on historical average, adjusted for current progress
      current_month_total = current_month_daily_spends.values.sum
      if progress_ratio > 0.1 # Only adjust if we have meaningful current data
        # Blend historical average with current pace
        current_pace_monthly = current_month_total / progress_ratio
        predicted = (avg_historical * 0.6 + current_pace_monthly * 0.4).round(2)
      else
        predicted = avg_historical.round(2)
      end

      {
        available: true,
        predicted: predicted,
        historical_average: avg_historical.round(2),
        years_of_data: historical_months.length,
        historical_months: historical_months
      }
    else
      { available: false }
    end
  end

  def monthly_breakdown(monthly_spends)
    monthly_spends.group(:month_date).sum(:adjusted_net_spend).map do |month, amount|
      {
        month: month.strftime("%Y-%m"),
        amount: amount,
        formatted_month: month.strftime("%b %Y")
      }
    end
  end
end
