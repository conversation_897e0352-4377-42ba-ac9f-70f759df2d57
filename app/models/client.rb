class Client < ApplicationRecord
  audited
  include PgSearch::Model

  # Direct associations (alphabetical)
  has_many :api_playgrounds, dependent: :nullify
  has_many :attribution_windows, dependent: :destroy
  has_many :campaign_monthly_spend_summaries, foreign_key: "legacy_client_id", primary_key: "legacy_id", dependent: :destroy
  has_many :campaign_spend_summaries, foreign_key: "legacy_client_id", primary_key: "legacy_id", dependent: :destroy
  has_many :campaign_spends, dependent: :destroy
  has_many :campaigns, dependent: :destroy
  has_many :client_budgets, dependent: :destroy
  has_many :client_creatives, dependent: :destroy
  has_many :client_direct_partners, dependent: :destroy
  has_many :client_documents, dependent: :destroy
  has_many :client_events, dependent: :destroy
  has_many :client_fraud_platforms, dependent: :destroy
  has_many :client_interests, dependent: :destroy
  has_many :client_reports, dependent: :destroy
  has_many :client_social_media_links, dependent: :destroy
  has_many :client_track_parties, dependent: :destroy
  has_many :conversation_contexts, as: :contextual, dependent: :destroy
  has_many :conversion_funnels, dependent: :destroy
  has_many :demographic_segments, dependent: :destroy
  has_many :event_mappings, dependent: :destroy
  has_many :gong_calls, dependent: :nullify
  has_many :mobile_apps, dependent: :destroy
  has_many :monthly_campaign_spend_adjustments, dependent: :destroy
  has_many :partner_integrations, dependent: :destroy
  has_many :slack_messages, dependent: :nullify
  has_many :target_kpis, dependent: :destroy
  has_many :track_party_integrations, dependent: :destroy
  has_many :fireflies_meetings, dependent: :nullify

  # Through associations (alphabetical)
  has_many :click_urls, through: :campaigns
  has_many :conversations, through: :conversation_contexts
  has_many :direct_partners, through: :client_direct_partners, source: :partner
  has_many :fraud_platforms, through: :client_fraud_platforms
  has_many :track_parties, through: :client_track_parties

  # Has one associations (alphabetical)
  has_one :client_deal_score, dependent: :destroy
  has_one :client_detail, dependent: :destroy

  has_one_attached :logo

  enum :preferred_logo_background, [ :logo_bg_light, :logo_bg_dark ]

  pg_search_scope :search_by_name, against: :name, using: { tsearch: { prefix: true, any_word: true } }

  has_associated_audits

  accepts_nested_attributes_for :client_detail, allow_destroy: true
  accepts_nested_attributes_for :client_budgets, allow_destroy: true
  accepts_nested_attributes_for :client_events, allow_destroy: true
  accepts_nested_attributes_for :client_creatives, allow_destroy: true
  accepts_nested_attributes_for :client_interests, allow_destroy: true
  accepts_nested_attributes_for :client_deal_score, allow_destroy: true, reject_if: :all_blank
  accepts_nested_attributes_for :client_direct_partners, allow_destroy: true
  accepts_nested_attributes_for :client_social_media_links, allow_destroy: true
  accepts_nested_attributes_for :client_social_media_links, allow_destroy: true
  accepts_nested_attributes_for :client_track_parties, allow_destroy: true
  accepts_nested_attributes_for :client_documents, allow_destroy: true
  accepts_nested_attributes_for :attribution_windows, allow_destroy: true
  accepts_nested_attributes_for :client_fraud_platforms, allow_destroy: true
  accepts_nested_attributes_for :client_reports, allow_destroy: true

  scope :active, -> { where(active: true, is_test: false) }
end
