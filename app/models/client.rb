class Client < ApplicationRecord
  audited
  include PgSearch::Model

  # Direct associations (alphabetical)
  has_many :api_playgrounds, dependent: :nullify
  has_many :attribution_windows, dependent: :destroy
  has_many :campaign_monthly_spend_summaries, foreign_key: "legacy_client_id", primary_key: "legacy_id", dependent: :destroy
  has_many :campaign_spend_summaries, foreign_key: "legacy_client_id", primary_key: "legacy_id", dependent: :destroy
  has_many :campaign_spends, dependent: :destroy
  has_many :campaigns, dependent: :destroy
  has_many :client_budgets, dependent: :destroy
  has_many :client_creatives, dependent: :destroy
  has_many :client_direct_partners, dependent: :destroy
  has_many :client_documents, dependent: :destroy
  has_many :client_events, dependent: :destroy
  has_many :client_fraud_platforms, dependent: :destroy
  has_many :client_interests, dependent: :destroy
  has_many :client_reports, dependent: :destroy
  has_many :client_social_media_links, dependent: :destroy
  has_many :client_track_parties, dependent: :destroy
  has_many :conversation_contexts, as: :contextual, dependent: :destroy
  has_many :conversion_funnels, dependent: :destroy
  has_many :demographic_segments, dependent: :destroy
  has_many :event_mappings, dependent: :destroy
  has_many :gong_calls, dependent: :nullify
  has_many :mobile_apps, dependent: :destroy
  has_many :monthly_campaign_spend_adjustments, dependent: :destroy
  has_many :partner_integrations, dependent: :destroy
  has_many :slack_messages, dependent: :nullify
  has_many :target_kpis, dependent: :destroy
  has_many :track_party_integrations, dependent: :destroy
  has_many :fireflies_meetings, dependent: :nullify

  # Through associations (alphabetical)
  has_many :click_urls, through: :campaigns
  has_many :conversations, through: :conversation_contexts
  has_many :direct_partners, through: :client_direct_partners, source: :partner
  has_many :fraud_platforms, through: :client_fraud_platforms
  has_many :track_parties, through: :client_track_parties

  # Has one associations (alphabetical)
  has_one :client_deal_score, dependent: :destroy
  has_one :client_detail, dependent: :destroy

  has_one_attached :logo

  enum :preferred_logo_background, [ :logo_bg_light, :logo_bg_dark ]

  pg_search_scope :search_by_name, against: :name, using: { tsearch: { prefix: true, any_word: true } }

  has_associated_audits

  accepts_nested_attributes_for :client_detail, allow_destroy: true
  accepts_nested_attributes_for :client_budgets, allow_destroy: true
  accepts_nested_attributes_for :client_events, allow_destroy: true
  accepts_nested_attributes_for :client_creatives, allow_destroy: true
  accepts_nested_attributes_for :client_interests, allow_destroy: true
  accepts_nested_attributes_for :client_deal_score, allow_destroy: true, reject_if: :all_blank
  accepts_nested_attributes_for :client_direct_partners, allow_destroy: true
  accepts_nested_attributes_for :client_social_media_links, allow_destroy: true
  accepts_nested_attributes_for :client_social_media_links, allow_destroy: true
  accepts_nested_attributes_for :client_track_parties, allow_destroy: true
  accepts_nested_attributes_for :client_documents, allow_destroy: true
  accepts_nested_attributes_for :attribution_windows, allow_destroy: true
  accepts_nested_attributes_for :client_fraud_platforms, allow_destroy: true
  accepts_nested_attributes_for :client_reports, allow_destroy: true

  scope :active, -> { where(active: true, is_test: false) }

  # Budget usage calculation methods
  def budget_usage_summary(months_back: 6, start_date: nil, end_date: nil)
    if start_date && end_date
      start_date = start_date.to_date
      end_date = end_date.to_date
    else
      # Default to last 6 complete months (not including current month)
      current_month_start = Date.current.beginning_of_month
      end_date = (current_month_start - 1.day).end_of_month # Last day of previous month
      start_date = end_date.beginning_of_month - (months_back - 1).months
    end

    # Get budgets within the date range
    budgets = client_budgets.where(
      "(start_date <= ? AND end_date >= ?) OR (start_date >= ? AND start_date <= ?)",
      end_date, start_date, start_date, end_date
    )

    # Get monthly spend data for this client
    monthly_spends = CampaignMonthlySpendSummary.where(
      legacy_client_id: legacy_id,
      month_date: start_date..end_date
    )

    # Debug information
    Rails.logger.debug "=== DEBUG budget_usage_summary ==="
    Rails.logger.debug "Client legacy_id: #{legacy_id}"
    Rails.logger.debug "Date range: #{start_date} to #{end_date}"
    Rails.logger.debug "Found #{monthly_spends.count} monthly spend records"
    Rails.logger.debug "Monthly spends: #{monthly_spends.pluck(:month_date, :adjusted_net_spend)}"

    # Calculate total budget amount
    total_budget = budgets.sum(:amount)

    # Calculate total spent amount (using adjusted_net_spend)
    total_spent = monthly_spends.sum(:adjusted_net_spend) || 0

    Rails.logger.debug "Total budget: #{total_budget}"
    Rails.logger.debug "Total spent: #{total_spent}"

    # Calculate usage percentage
    usage_percentage = total_budget > 0 ? (total_spent / total_budget * 100).round(2) : 0

    {
      total_budget: total_budget,
      total_spent: total_spent,
      remaining_budget: total_budget - total_spent,
      usage_percentage: usage_percentage,
      period_start: start_date,
      period_end: end_date,
      budgets_count: budgets.count,
      monthly_breakdown: monthly_breakdown(monthly_spends)
    }
  end

  def budget_trend_data(months_back: 6, start_date: nil, end_date: nil)
    if start_date && end_date
      start_date = start_date.to_date
      end_date = end_date.to_date
    else
      # Default to last 6 complete months (not including current month)
      current_month_start = Date.current.beginning_of_month
      end_date = (current_month_start - 1.day).end_of_month # Last day of previous month
      start_date = end_date.beginning_of_month - (months_back - 1).months
    end

    monthly_spends = CampaignMonthlySpendSummary.where(
      legacy_client_id: legacy_id,
      month_date: start_date..end_date
    ).group(:month_date)
     .sum(:adjusted_net_spend)

    # Fill in missing months with 0
    (start_date.to_date..end_date.to_date).map(&:beginning_of_month).uniq.map do |month|
      [ month.strftime("%Y-%m"), monthly_spends[month] || 0 ]
    end.to_h
  end

  def current_month_daily_spends
    current_month_start = Date.current.beginning_of_month

    # Get daily spend data for current month from monthly campaign spends
    # Convert monthly data to estimated daily data for visualization
    current_month_spend = CampaignMonthlySpendSummary.where(
      legacy_client_id: legacy_id,
      month_date: current_month_start
    ).sum(:adjusted_net_spend)

    # If we have monthly data, distribute it across days
    if current_month_spend > 0
      days_in_month = Date.current.end_of_month.day
      daily_average = current_month_spend / days_in_month

      # Create daily distribution (simplified - could be enhanced with actual daily patterns)
      (current_month_start..Date.current).map do |date|
        [ date.strftime("%Y-%m-%d"), daily_average ]
      end.to_h
    else
      # Fallback to actual daily data if available
      daily_spends = campaign_spend_summaries.where(
        spend_date: current_month_start..Date.current
      ).group(:spend_date)
       .sum(:adjusted_net_spend)

      # Fill in missing days with 0
      (current_month_start..Date.current).map do |date|
        [ date.strftime("%Y-%m-%d"), daily_spends[date] || 0 ]
      end.to_h
    end
  end

  def recent_months_daily_pattern(months_back: 3)
    end_date = Date.current.end_of_month
    start_date = end_date - months_back.months + 1.day

    # Get monthly spend data for recent months
    monthly_spends = CampaignMonthlySpendSummary.where(
      legacy_client_id: legacy_id,
      month_date: start_date..end_date
    )

    return {} if monthly_spends.empty?

    # Calculate daily patterns from monthly data
    daily_pattern = {}
    monthly_spends.each do |spend|
      month_start = spend.month_date.beginning_of_month
      month_end = spend.month_date.end_of_month
      days_in_month = month_end.day
      daily_avg = spend.adjusted_net_spend / days_in_month

      (month_start..month_end).each do |date|
        daily_pattern[date.strftime("%Y-%m-%d")] = daily_avg
      end
    end

    daily_pattern
  end

  def predict_month_end_spend
    # Get current month data
    current_month_start = Date.current.beginning_of_month
    current_month_spend = CampaignMonthlySpendSummary.where(
      legacy_client_id: legacy_id,
      month_date: current_month_start
    ).sum(:adjusted_net_spend)

    days_passed = Date.current.day
    days_in_month = Date.current.end_of_month.day

    # Get recent months data for trend analysis
    recent_months_data = get_recent_months_trend(months_back: 3)

    # Calculate different prediction methods
    predictions = calculate_multiple_predictions(current_month_spend, days_passed, days_in_month, recent_months_data)

    # Choose best prediction method
    best_prediction = select_best_prediction(predictions, recent_months_data)

    {
      current_total: current_month_spend,
      predicted_total: best_prediction[:predicted_total],
      average_daily: best_prediction[:average_daily],
      days_remaining: days_in_month - days_passed,
      prediction_method: best_prediction[:method],
      confidence: best_prediction[:confidence],
      recent_trend: recent_months_data
    }
  end

  def partner_spend_breakdown(months_back: 6, start_date: nil, end_date: nil)
    if start_date && end_date
      start_date = start_date.to_date
      end_date = end_date.to_date
    else
      # Default to last 6 complete months (not including current month)
      current_month_start = Date.current.beginning_of_month
      end_date = (current_month_start - 1.day).end_of_month # Last day of previous month
      start_date = end_date.beginning_of_month - (months_back - 1).months
    end

    # Get monthly spend data for this client
    monthly_spends = CampaignMonthlySpendSummary.where(
      legacy_client_id: legacy_id,
      month_date: start_date..end_date
    )

    return [] if monthly_spends.empty?

    # Group by partner and calculate totals
    partner_totals = monthly_spends.group(:legacy_partner_id)
                                  .sum(:adjusted_net_spend)

    total_spend = partner_totals.values.sum
    return [] if total_spend == 0

    # Build breakdown with partner details
    partner_totals.map do |partner_id, spend_amount|
      partner = Partner.find_by(legacy_id: partner_id)
      next unless partner

      percentage = (spend_amount / total_spend * 100).round(2)

      # Get campaigns count for this partner
      campaigns_count = monthly_spends.where(legacy_partner_id: partner_id)
                                     .distinct
                                     .count(:legacy_campaign_id)

      {
        partner_id: partner_id,
        partner_name: partner.name,
        spend_amount: spend_amount,
        percentage: percentage,
        campaigns_count: campaigns_count
      }
    end.compact.sort_by { |item| -item[:spend_amount] }
  end

  private

  def get_recent_months_trend(months_back: 3)
    # Get the last complete months (not current month)
    current_month_start = Date.current.beginning_of_month
    end_date = current_month_start - 1.day # Last day of previous month
    start_date = end_date.beginning_of_month - (months_back - 1).months

    # Debug information
    Rails.logger.debug "=== DEBUG get_recent_months_trend ==="
    Rails.logger.debug "Client legacy_id: #{legacy_id}"
    Rails.logger.debug "Date range: #{start_date} to #{end_date}"

    # Get monthly spend data grouped by month
    monthly_spends = CampaignMonthlySpendSummary.where(
      legacy_client_id: legacy_id,
      month_date: start_date..end_date
    ).group(:month_date)
     .sum(:adjusted_net_spend)
     .sort

    Rails.logger.debug "Found #{monthly_spends.count} months with data"
    Rails.logger.debug "Monthly totals: #{monthly_spends}"

    # Convert to the expected format
    results = monthly_spends.map do |month_date, total_amount|
      {
        month: month_date.strftime("%Y-%m"),
        amount: total_amount || 0,
        formatted_month: month_date.strftime("%b %Y")
      }
    end

    Rails.logger.debug "Final results: #{results}"

    # Ensure we only return the last N months
    results.last(months_back)
  end

  def calculate_multiple_predictions(current_spend, days_passed, days_in_month, recent_data)
    predictions = {}

    # Method 1: Linear progression from current month
    if current_spend > 0 && days_passed > 0
      daily_rate = current_spend / days_passed
      predictions[:linear] = {
        predicted_total: (daily_rate * days_in_month).round(2),
        average_daily: daily_rate.round(2),
        method: "Linear (Current Month)",
        confidence: days_passed >= 7 ? "Medium" : "Low"
      }
    end

    # Method 2: Recent months average (limit to 3 months)
    if recent_data.any?
      limited_recent = recent_data.last(3)
      avg_recent = limited_recent.sum { |d| d[:amount] } / limited_recent.length
      predictions[:recent_average] = {
        predicted_total: avg_recent.round(2),
        average_daily: (avg_recent / days_in_month).round(2),
        method: "Recent #{limited_recent.length} Months Average",
        confidence: limited_recent.length >= 2 ? "High" : "Medium"
      }
    end

    # Method 3: Trend-based prediction
    if recent_data.length >= 2
      trend_prediction = calculate_trend_prediction(recent_data, days_in_month)
      predictions[:trend] = trend_prediction
    end

    predictions
  end

  def calculate_trend_prediction(recent_data, days_in_month)
    # Limit to last 3 months for trend analysis
    limited_data = recent_data.last(3)
    amounts = limited_data.map { |d| d[:amount] }

    # Simple linear trend calculation
    n = amounts.length
    return default_prediction if n < 2 # Need at least 2 points for trend

    x_values = (1..n).to_a
    y_values = amounts

    # Calculate slope (trend)
    x_mean = x_values.sum.to_f / n
    y_mean = y_values.sum.to_f / n

    numerator = x_values.zip(y_values).sum { |x, y| (x - x_mean) * (y - y_mean) }
    denominator = x_values.sum { |x| (x - x_mean) ** 2 }

    if denominator != 0
      slope = numerator / denominator
      intercept = y_mean - slope * x_mean

      # Predict next month (n+1)
      predicted = slope * (n + 1) + intercept
      predicted = [ predicted, 0 ].max # Don't predict negative values

      {
        predicted_total: predicted.round(2),
        average_daily: (predicted / days_in_month).round(2),
        method: "Trend Analysis (#{n} months)",
        confidence: n >= 3 ? "High" : "Medium",
        trend_direction: slope > 0 ? "Increasing" : (slope < 0 ? "Decreasing" : "Stable")
      }
    else
      # Fallback to average if trend calculation fails
      avg = y_values.sum / n
      {
        predicted_total: avg.round(2),
        average_daily: (avg / days_in_month).round(2),
        method: "Average (Trend calc failed)",
        confidence: "Low"
      }
    end
  end

  def select_best_prediction(predictions, recent_data)
    return default_prediction if predictions.empty?

    # Priority order: trend > recent_average > linear
    if predictions[:trend] && predictions[:trend][:confidence] != "Low"
      predictions[:trend]
    elsif predictions[:recent_average]
      predictions[:recent_average]
    elsif predictions[:linear]
      predictions[:linear]
    else
      default_prediction
    end
  end

  def default_prediction
    {
      predicted_total: 0,
      average_daily: 0,
      method: "No sufficient data",
      confidence: "None"
    }
  end

  def monthly_breakdown(monthly_spends)
    # Get same month from previous years (up to 3 years)
    historical_months = []

    (1..3).each do |years_back|
      target_date = Date.current - years_back.years
      month_start = target_date.beginning_of_month

      # Get monthly spend for this historical month
      historical_spend = CampaignMonthlySpendSummary.where(
        legacy_client_id: legacy_id,
        month_date: month_start
      ).sum(:adjusted_net_spend)

      if historical_spend > 0
        historical_months << {
          year: target_date.year,
          month: target_date.month,
          total_spend: historical_spend
        }
      end
    end

    if historical_months.any?
      # Calculate average historical spend for this month
      avg_historical = historical_months.sum { |m| m[:total_spend] } / historical_months.length

      # Adjust for current month progress
      days_passed = Date.current.day
      days_in_month = Date.current.end_of_month.day
      progress_ratio = days_passed.to_f / days_in_month

      # Predict based on historical average, adjusted for current progress
      current_month_total = current_month_daily_spends.values.sum
      if progress_ratio > 0.1 # Only adjust if we have meaningful current data
        # Blend historical average with current pace
        current_pace_monthly = current_month_total / progress_ratio
        predicted = (avg_historical * 0.6 + current_pace_monthly * 0.4).round(2)
      else
        predicted = avg_historical.round(2)
      end

      {
        available: true,
        predicted: predicted,
        historical_average: avg_historical.round(2),
        years_of_data: historical_months.length,
        historical_months: historical_months
      }
    else
      { available: false }
    end
  end

  def monthly_breakdown(monthly_spends)
    monthly_spends.group(:month_date).sum(:adjusted_net_spend).map do |month, amount|
      {
        month: month.strftime("%Y-%m"),
        amount: amount,
        formatted_month: month.strftime("%b %Y")
      }
    end
  end
end
