class CampaignSpendFile < ApplicationRecord
  has_one_attached :file

  STATUSES = %w[pending ready].freeze
  enum :status, STATUSES.map { |status| [ status, status ] }.to_h

  scope :ready, -> { where(status: :ready) }

  def self.generate
    version = "#{Date.today.strftime('%Y%m%d')}_#{SecureRandom.hex(3)}"

    # Create the record but don't save until we have the file attached
    campaign_spend_file = CampaignSpendFile.new(version: version)

    # Use string IO instead of tempfile for better memory efficiency
    csv_data = generate_csv_data

    # Attach the file directly from the string
    campaign_spend_file.file.attach(
      io: StringIO.new(csv_data),
      filename: "campaign_spends_#{version}.csv",
      content_type: "text/csv"
    )

    # Save once with all attributes
    campaign_spend_file.status = :ready
    campaign_spend_file.save!

    campaign_spend_file
  end

  private

  def self.generate_csv_data
    # Original headers
    headers = [
      "Date", "Month", "Week Number", "Country Code", "Client ID",
      "Client Name", "Click Url ID", "Campaign ID", "Campaign Name", "OS",
      "Vendor ID", "Vendor Name", "Track Party", "Gross Spend", "Net Spend",
      "Revenue", "Gross Spend Source", "Net Spend Source",
      "Client Pricing Model", "Client Pricing Rate", "Client Paid Events",
      "Partner Pricing Model", "Partner Pricing Rate", "Partner Paid Events"
    ]

    # Add all paid events to headers with their sources
    PaidEvent.all.each do |event|
      headers << event.titleize
      headers << "#{event.titleize} Source"
    end

    # Use includes to avoid N+1 queries
    campaign_spends = DailyCampaignSpend.includes(
      :campaign,
      :client,
      :partner,
      :click_url,
      :gross_campaign_spend,
      :net_campaign_spend,
      click_url: :track_party,
    ).where(is_test: false)

    # Collect all dates and click_url_ids from campaign spends
    spend_dates = campaign_spends.map(&:spend_date).uniq
    click_url_ids = campaign_spends.map { |cs| cs.click_url&.id }.compact.uniq

    # Preload all relevant pricing configurations in two bulk queries
    client_pricing_map = ClientPricing.bulk_load_configurations(click_url_ids, spend_dates)
    partner_pricing_map = PartnerPricing.bulk_load_configurations(click_url_ids, spend_dates)

    # Preload all event aggregates for the click_urls and dates in a single query
    # with proper data origin prioritization
    event_aggregates_map = preload_event_aggregates(click_url_ids, spend_dates)

    CSV.generate do |csv|
      csv << headers

      campaign_spends.find_each do |campaign_spend|
        date = campaign_spend.spend_date
        click_url_id = campaign_spend.click_url&.id

        # Skip if no click_url is associated
        next unless click_url_id

        # Get cached pricing configurations for this click_url and date
        client_pricing_configs = client_pricing_map[[ click_url_id, date ]] || []
        partner_pricing_configs = partner_pricing_map[[ click_url_id, date ]] || []

        # Format client pricing information
        client_pricing_models = client_pricing_configs.map(&:pricing_model).uniq.join(", ")
        client_pricing_rates = client_pricing_configs.map(&:rate).join(", ")
        client_paid_events = client_pricing_configs.map(&:paid_event).uniq.join(", ")

        # Format partner pricing information
        partner_pricing_models = partner_pricing_configs.map(&:pricing_model).uniq.join(", ")
        partner_pricing_rates = partner_pricing_configs.map(&:rate).join(", ")
        partner_paid_events = partner_pricing_configs.map(&:paid_event).uniq.join(", ")

        # Build the base row with original data
        row = [
          date,
          date.strftime("%Y-%m"),
          date.strftime("%Y-W%U"),
          campaign_spend.campaign.country_code,
          campaign_spend.legacy_client_id,
          campaign_spend.client.name,
          campaign_spend.legacy_click_url_id,
          campaign_spend.legacy_campaign_id,
          campaign_spend.campaign.name,
          campaign_spend.campaign.os,
          campaign_spend.legacy_partner_id,
          campaign_spend.partner.name,
          campaign_spend.track_party.name,
          campaign_spend.gross_spend,
          campaign_spend.adjusted_net_spend,
          campaign_spend.revenue,
          [ campaign_spend.gross_spend_source, campaign_spend.gross_campaign_spend.calculation_metadata["spend_source"] ].reject(&:nil?).join("/"),
          [ campaign_spend.net_spend_source, campaign_spend.net_campaign_spend.calculation_metadata["spend_source"] ].reject(&:nil?).join("/"),
          client_pricing_models,
          client_pricing_rates,
          client_paid_events,
          partner_pricing_models,
          partner_pricing_rates,
          partner_paid_events
        ]

        # Add event counts and their sources for each paid event
        PaidEvent.all.each do |event|
          # Get the event count and source from our preloaded map
          event_data = event_aggregates_map[[ click_url_id, date, event ]] || { count: nil, origin: nil }
          row << event_data[:count]
          row << event_data[:origin]
        end

        csv << row
      end
    end
  end

  def self.preload_event_aggregates(click_url_ids, dates)
    priority_origins = [
      EventDataOrigin::FEEDMOB,
      EventDataOrigin::IMPORTED_ADJUST,
      EventDataOrigin::IMPORTED_APPSFLYER,
      EventDataOrigin::IMPORTED_SINGULAR,
      EventDataOrigin::FEEDMOB_AGENCY,
      EventDataOrigin::FEEDMOB_REDSHIFT,
      EventDataOrigin::FEEDMOB_NET_SPEND,
      EventDataOrigin::MMP_AGGREGATE
    ]

    result_map = {}

    # Use raw SQL with a window function to select the highest priority aggregate per group
    # This reduces memory usage by having the database do the prioritization
    aggregates = EventAggregate
      .select("click_url_id, date, event, count, data_origin, ROW_NUMBER() OVER (PARTITION BY click_url_id, date, event ORDER BY position(data_origin::text in '#{priority_origins.join(',')}')) as priority_rank")
      .where(click_url_id: click_url_ids, date: dates, data_origin: priority_origins)
      .from("(#{EventAggregate.where(click_url_id: click_url_ids, date: dates, data_origin: priority_origins).to_sql}) as event_aggregates")

    # Use find_each to batch process results, reducing memory usage
    EventAggregate.connection.execute(
      "SELECT click_url_id, date, event, count, data_origin FROM (#{aggregates.to_sql}) ranked WHERE priority_rank = 1"
    ).each do |row|
      click_url_id = row["click_url_id"]
      date = row["date"].to_date
      event = row["event"]
      count = row["count"]
      origin = row["data_origin"]

      result_map[[ click_url_id, date, event ]] = { count: count, origin: origin }
    end

    result_map
  end
end
