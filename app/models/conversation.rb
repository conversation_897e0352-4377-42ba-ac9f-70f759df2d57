class Conversation < ApplicationRecord
  include ActionView::RecordIdentifier

  STATUSES = %w[active awaiting_response closed].freeze

  validates :source, inclusion: { in: %w[web slack].freeze }

  enum :status, STATUSES.map { |status| [ status, status ] }.to_h

  belongs_to :user
  has_many :messages, dependent: :destroy
  has_many :conversation_contexts, dependent: :destroy
  has_many :clients, through: :conversation_contexts, source: :contextual, source_type: "Client"
  has_many :partners, through: :conversation_contexts, source: :contextual, source_type: "Partner"
  has_many :client_documents, through: :conversation_contexts, source: :contextual, source_type: "ClientDocument"

  after_update_commit :broadcast_updated
  before_create :set_defaults

  def model
    super || "claude_3_5_haiku"
  end

  def broadcast_updated
    if saved_change_to_attribute?(:status)
      case source
      when "web"
        broadcast_web_update
      when "slack"
        # Nothing to do here
      end
    end
  end

  def formatted_context
    return "" if conversation_contexts.empty?

    conversation_contexts.includes(:contextual).map do |context|
      case context.contextual_type
      when "Partner"
        ContextBuilder::PartnerBuilder.new(context.contextual, context_settings.symbolize_keys).build
      when "Client"
        ContextBuilder::ClientBuilder.new(context.contextual, context_settings.symbolize_keys).build
      else
        context.content || "No content available"
      end
    end.join("\n\n---\n\n")
  end

  private

  def broadcast_web_update
    broadcast_replace_to(
      "#{dom_id(self)}_messages",
      partial: "messages/form",
      locals: { conversation: self },
      target: "#{dom_id(self)}_message_form"
    )
  end

  def set_defaults
    if source == "slack"
      self.streaming = false
      self.assistant = "feedmob"
      self.model = user.default_llm_model || "claude_3_5_haiku"
      self.profile = "default"
    end

    if source == "web"
      self.assistant = "planner"
      self.context_settings = {
        include_activities: false,
        context_start_date: 1.months.ago.beginning_of_month.to_s,
        context_end_date: Date.current.to_s
      }
    end
  end
end
