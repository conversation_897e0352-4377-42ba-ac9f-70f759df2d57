module AssistantTools
  class GenerateTypstReportTool < Base
    visible_to_users(false)

    def self.tool_spec
      {
        name: "generate_typst_report",
        description: "Generates a PDF report using Typst markup language. Creates a professional document with the provided content and structure.",
        input_schema: {
          json: {
            type: "object",
            required: [ "main_title", "sub_title", "content" ],
            properties: {
              main_title: {
                type: "string",
                description: "Main title of the report, should be concise and descriptive. Better less than 3 words."
              },
              sub_title: {
                type: "string",
                description: "Sub title of the report, should provide additional context."
              },
              content: {
                type: "string",
                description: "Main report content (exclude the page set and title, etc.) in Typst markup format and follow the provided example, do not use any syntax not mentioned in the example."
              }
            }
          }
        }
      }
    end

    def call(context)
      typst_content = generate_typst_content
      pdf_path = compile_typst_to_pdf(typst_content)
      attach_files_to_message(context[:message], pdf_path, typst_content)
      "PDF report generated and attached successfully"
    end

    private

    def generate_typst_content
      <<~TYPST
        #import "@preview/cetz:0.4.0": canvas, draw, palette
        #import "@preview/cetz-plot:0.1.2": chart, plot

        #set text(font: "Lato", size: 11pt)
        #set page(margin: (x: 2cm, y: 2.5cm))
        #set par(justify: true, leading: 0.6em)
        #set heading(numbering: "1.")
        #show heading.where(level: 1): it => [
          #v(1em)
          #text(size: 16pt, weight: "bold", fill: rgb("#00B5AD"))[#it.body]
          #v(0.5em)
          #line(length: 100%, stroke: rgb("#00B5AD"))
          #v(0.5em)
        ]

        #set document(title: "#{input[:main_title]} - #{input[:sub_title]}", author: "FeedAI Planner")
        #set page(numbering: "1 / 1", number-align: center)

        #set page(
          footer: align(center)[#text(fill: rgb("#444"))[© #{Time.current.strftime("%Y")} FeedMob Inc. All rights reserved.]]
        )

        // FeedMob Logo
        #align(center)[
          #image("logo.png", width: 35%)
        ]

        #v(2em)

        #align(center)[
          #text(size: 28pt, weight: "bold", fill: rgb("#008682"))[
            #{input[:main_title]}
          ]
        ]

        #v(0.5em)

        #align(center)[
          #text(size: 16pt, style: "italic", fill: rgb("#5a5a5a"))[
            #{input[:sub_title]}
          ]
        ]

        #v(1em)

        #align(center)[
          #text(size: 12pt, fill: rgb("#1D262F"))[
            Generated on #{Time.current.strftime("%B %d, %Y")} by FeedAI Planner
          ]
        ]

        #v(1em)
        #line(length: 100%, stroke: 2pt + rgb("#1f4e79"))
        #v(2em)

        // Disclaimer
        #rect(
          width: 100%,
          height: auto,
          stroke: 1pt + rgb("#e2e8f0"),
          fill: rgb("#f7fafc"),
          radius: 5pt,
          inset: 15pt
        )[
          #text(size: 12pt, weight: "bold", fill: rgb("#1f4e79"))[AI-Generated Content Disclaimer]

          #v(0.5em)

          This document is generated by AI and may contain incorrect or inaccurate data. The content provided is for informational purposes only and should not be considered as professional advice. Please always double-check the information and verify facts independently before making any decisions based on this report.
        ]

        #let feedmob-corporate-palette = palette.new(
          colors: (
            // Primary Colors
            rgb("#00B5AD"),   // Teal (Primary)
            rgb("#008682"),   // Dark Teal (Primary Dark)
            rgb("#31404E"),   // Slate (Primary Dark)

            // Secondary Colors
            rgb("#1B9BC2"),   // Professional Blue
            rgb("#4C5864"),   // Slate Gray
            rgb("#1D262F"),   // Dark Charcoal

            // Accent Colors
            rgb("#A2A4A4"),   // Silver
            rgb("#E5E7E9")    // Off-white
          )
        )

        #let feedmob-vibrant-palette = palette.new(
          colors: (
            // Primary Colors
            rgb("#00B5AD"),   // Teal (Primary)
            rgb("#EE6969"),   // Coral (Secondary)
            rgb("#A06ACD"),   // Purple (Secondary)

            // Energy Colors
            rgb("#F7BD63"),   // Warm Yellow
            rgb("#1B9BC2"),   // Bright Blue
            rgb("#2ECC71"),   // Vibrant Green

            // Neutral Anchors
            rgb("#32404E"),   // Gray Blue
            rgb("#B2B7BD")    // Light Gray
          )
        )

        #pagebreak()

        #{input[:content]}

        #v(2em)

        #align(center)[
          #text(size: 12pt, weight: "bold", fill: rgb("#008682"))[
            Internal Use Only
          ]
        ]

        #align(center)[
          #text(size: 10pt, style: "italic", fill: rgb("#5a5a5a"))[
            This document is for internal usage only and should not be shared externally.
          ]
        ]
      TYPST
    end

    def compile_typst_to_pdf(typst_content)
      temp_dir = Dir.mktmpdir("typst_project")

      begin
        # Create files in the project directory
        temp_typst_file = File.join(temp_dir, "report.typ")
        temp_pdf_file = File.join(temp_dir, "report.pdf")

        # Copy assets to the temp directory
        copy_assets_to_temp_dir(temp_dir)

        # Write Typst content to file
        File.write(temp_typst_file, typst_content)

        # Compile Typst to PDF (no --root needed)
        command = [ "typst", "compile", temp_typst_file, temp_pdf_file ]

        # Capture both stdout and stderr
        _, stderr, status = Open3.capture3(*command)

        unless status.success? && File.exist?(temp_pdf_file)
          error_message = "Typst compilation failed"
          error_message += ": #{stderr.strip}" unless stderr.strip.empty?
          Rails.logger.error "#{error_message}. Command: #{command.join(' ')}"
          raise ToolExecError, error_message
        end

        Rails.logger.info "Successfully compiled Typst document to PDF"
        temp_pdf_file

      ensure
        # Don't clean up here - let attach_pdf_to_message handle it
      end
    end

    def copy_assets_to_temp_dir(temp_dir)
      assets_path = Rails.root.join("app", "assets", "images", "typst")
      return unless Dir.exist?(assets_path)

      Dir.glob(File.join(assets_path, "**", "*")).each do |file_path|
        next unless File.file?(file_path)

        relative_path = Pathname.new(file_path).relative_path_from(assets_path)
        dest_path = File.join(temp_dir, relative_path)

        FileUtils.mkdir_p(File.dirname(dest_path))
        FileUtils.cp(file_path, dest_path)
      end
    end

    def attach_files_to_message(message, pdf_path, typst_content)
      begin
        # Attach PDF file
        File.open(pdf_path, "rb") do |file|
          message.files.attach(
            io: file,
            filename: "report-#{Time.current.to_i}.pdf",
            content_type: "application/pdf"
          )
        end

        # Attach Typst source file
        message.files.attach(
          io: StringIO.new(typst_content),
          filename: "report-#{Time.current.to_i}.typ",
          content_type: "text/plain"
        )

        Rails.logger.info "Successfully attached PDF report and Typst source to message #{message.id}"
      ensure
        # Clean up the entire temp directory
        temp_dir = File.dirname(pdf_path)
        FileUtils.rm_rf(temp_dir) if Dir.exist?(temp_dir)
      end
    end
  end
end
