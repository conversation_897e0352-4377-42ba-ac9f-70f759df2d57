class SubClientBudget < ApplicationRecord
  belongs_to :client_budget
  has_many :sub_client_budget_campaigns, dependent: :destroy
  has_many :campaigns, through: :sub_client_budget_campaigns

  validates :name, presence: true
  validates :amount, presence: true, numericality: { greater_than_or_equal_to: 0 }
  validates :legacy_id, presence: true, uniqueness: true

  audited associated_with: :client_budget

  # Sub budget usage calculation methods
  def usage_summary(months_back: 6)
    end_date = Date.current.end_of_month
    start_date = end_date - months_back.months + 1.day

    # Get campaign IDs associated with this sub budget
    campaign_ids = campaigns.pluck(:legacy_id)
    return empty_usage_summary if campaign_ids.empty?

    # Get monthly spend data for campaigns in this sub budget
    monthly_spends = MonthlyCampaignSpend.where(
      legacy_campaign_id: campaign_ids,
      month_date: start_date..end_date
    )

    # Calculate total spent amount
    total_spent = monthly_spends.sum(:adjusted_net_spend) || 0

    # Calculate usage percentage
    usage_percentage = amount > 0 ? (total_spent / amount * 100).round(2) : 0

    {
      budget_amount: amount,
      total_spent: total_spent,
      remaining_budget: amount - total_spent,
      usage_percentage: usage_percentage,
      period_start: start_date,
      period_end: end_date,
      campaigns_count: campaigns.count,
      campaign_breakdown: campaign_spend_breakdown(monthly_spends)
    }
  end

  def campaign_spend_breakdown(monthly_spends = nil)
    end_date = Date.current.end_of_month
    start_date = end_date - 6.months + 1.day

    # Use provided monthly_spends or fetch fresh data
    if monthly_spends.nil?
      campaign_ids = campaigns.pluck(:legacy_id)
      return [] if campaign_ids.empty?

      monthly_spends = MonthlyCampaignSpend.where(
        legacy_campaign_id: campaign_ids,
        month_date: start_date..end_date
      )
    end

    # Group by campaign and calculate totals
    campaign_totals = monthly_spends.group(:legacy_campaign_id)
                                   .sum(:adjusted_net_spend)

    total_spend = campaign_totals.values.sum
    return [] if total_spend == 0

    # Build breakdown with campaign details
    campaign_totals.map do |campaign_id, spend_amount|
      campaign = campaigns.find_by(legacy_id: campaign_id)
      next unless campaign

      percentage = (spend_amount / total_spend * 100).round(2)

      {
        campaign_id: campaign_id,
        campaign_name: campaign.name,
        spend_amount: spend_amount,
        percentage: percentage,
        budget_utilization: amount > 0 ? (spend_amount / amount * 100).round(2) : 0
      }
    end.compact.sort_by { |item| -item[:spend_amount] }
  end

  def partner_spend_breakdown(monthly_spends = nil)
    end_date = Date.current.end_of_month
    start_date = end_date - 6.months + 1.day

    # Use provided monthly_spends or fetch fresh data
    if monthly_spends.nil?
      campaign_ids = campaigns.pluck(:legacy_id)
      return [] if campaign_ids.empty?

      monthly_spends = MonthlyCampaignSpend.where(
        legacy_campaign_id: campaign_ids,
        month_date: start_date..end_date
      )
    end

    # Group by partner and calculate totals
    partner_totals = monthly_spends.group(:legacy_partner_id)
                                  .sum(:adjusted_net_spend)

    total_spend = partner_totals.values.sum
    return [] if total_spend == 0

    # Build breakdown with partner details
    partner_totals.map do |partner_id, spend_amount|
      partner = Partner.find_by(legacy_id: partner_id)
      next unless partner

      percentage = (spend_amount / total_spend * 100).round(2)

      # Get campaigns for this partner within this sub budget
      partner_campaigns = monthly_spends.where(legacy_partner_id: partner_id)
                                       .group(:legacy_campaign_id)
                                       .count

      {
        partner_id: partner_id,
        partner_name: partner.name,
        spend_amount: spend_amount,
        percentage: percentage,
        budget_utilization: amount > 0 ? (spend_amount / amount * 100).round(2) : 0,
        campaigns_count: partner_campaigns.count
      }
    end.compact.sort_by { |item| -item[:spend_amount] }
  end

  def trend_data(months_back: 6)
    end_date = Date.current.end_of_month
    start_date = end_date - months_back.months + 1.day

    campaign_ids = campaigns.pluck(:legacy_id)
    return {} if campaign_ids.empty?

    monthly_spends = MonthlyCampaignSpend.where(
      legacy_campaign_id: campaign_ids,
      month_date: start_date..end_date
    ).group(:month_date)
     .sum(:adjusted_net_spend)

    # Fill in missing months with 0
    (start_date.to_date..end_date.to_date).map(&:beginning_of_month).uniq.map do |month|
      [ month.strftime("%Y-%m"), monthly_spends[month] || 0 ]
    end.to_h
  end

  private

  def empty_usage_summary
    {
      budget_amount: amount,
      total_spent: 0,
      remaining_budget: amount,
      usage_percentage: 0,
      period_start: Date.current - 6.months,
      period_end: Date.current,
      campaigns_count: 0,
      campaign_breakdown: []
    }
  end
end
