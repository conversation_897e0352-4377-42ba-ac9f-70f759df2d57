class FirefliesMeeting < ApplicationRecord
  belongs_to :client, optional: true
  belongs_to :partner, optional: true
  has_many :fireflies_utterances, dependent: :destroy

  validates :fireflies_id, presence: true, uniqueness: true
  validates :title, presence: true

  scope :recent, -> { where("meeting_date >= ?", 1.month.ago) }
  scope :recent_within, ->(start_date, end_date) {
    where("meeting_date >= ? AND meeting_date <= ?", start_date, end_date)
  }
end
