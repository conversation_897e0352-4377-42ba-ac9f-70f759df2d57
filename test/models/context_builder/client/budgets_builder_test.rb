require "test_helper"

module ContextBuilder
  module Client
    class BudgetsBuilderTest < ActiveSupport::TestCase
      setup do
        @client = clients(:one)
      end

      def test_build_returns_formatted_budget_summary_with_recent_budgets
        builder = ContextBuilder::Client::BudgetsBuilder.new(@client, start_date: "2024-01-01")

        expected_output = <<~MARKDOWN
          ## Client Budgets

          | Amount | Start Date | End Date | Sub-Budget |
          |--------|------------|----------|------------|
          | 150000 | 2024-01-01 | 2024-03-31 | - |
          | Sub budget | - | - | Android CPA Budget (Planned amount 85000) |
          | Sub budget | - | - | iOS CPA Budget (Planned amount 65000) |
          | 200000 | 2024-04-01 | 2024-06-30 | - |
          | Sub budget | - | - | Q2 Campaign Budget (Planned amount 120000) |
        MARKDOWN

        assert_equal expected_output.strip, builder.build.strip
      end
    end
  end
end
