require "test_helper"

module ContextBuilder
  class ClickUrlPerformanceBuilderTest < ActiveSupport::TestCase
    setup do
      @click_url = click_urls(:one)
    end

    def test_build_returns_formatted_output_with_performance_data
      builder = ContextBuilder::ClickUrlPerformanceBuilder.new(@click_url, start_date: "2023-01-01", end_date: "2024-12-22")

      expected_output = <<~MARKDOWN
        ## Overall Performance Summary for Click URL ID: 401, Campaign ID: 301

        **Analysis Period:** Jan 01, 2023 - Dec 22, 2024

        | Metric | Value |
        |--------|-------|
        | Client Name | One |
        | Campaign Name | ONE iOS US |
        | Vendor Name | One |
        | Mobile App | One - Super App |
        | Platform | android |
        | Country Code | N/A |
        | Total Gross Spend | $17.57 |
        | Total Net Spend | $15.88 |
        | Total Revenue | $1.69 |
        | Average Margin | 9.6% |
        | Total Impressions | 25000 |
        | Total Clicks | 10500 |
        | Total Installs | 50 |
        | Average CVR | 0.48% |
        | First Spend Date | Jan 02, 2023 |
        | Last Spend Date | Dec 21, 2024 |
        | Days Active | 4 |
        | Avg Daily Spend | $4.39 |

        ## Monthly Breakdown

        | Month | Gross Spend | Revenue | Margin | Impressions | Clicks | Installs | CVR | Active Days |
        |-------|-------------|---------|--------|-------------|--------|----------|-----|-------------|
        | January 2023 | $3.6 | $1.2 | 33.3% | 15000 | 5000 | 25 | 0.5% | 1 |
        | December 2024 | $13.97 | $0.49 | 3.5% | 10000 | 5500 | 25 | 0.45% | 3 |

        ## Weekly Breakdown (Last 12 Weeks)

        | Week | Gross Spend | Revenue | Margin | Impressions | Clicks | Installs | CVR | Active Days |
        |------|-------------|---------|--------|-------------|--------|----------|-----|-------------|
        | Dec 16 - Dec 22 | $13.97 | $0.49 | 3.5% | 10000 | 5500 | 25 | 0.45% | 3 |

        ## Daily Breakdown (Last 35 Days)

        | Date | Gross Spend | Revenue | Margin | Impressions | Clicks | Installs | CVR |
        |------|-------------|---------|--------|-------------|--------|----------|-----|
        | Thu, Dec 19 | $2.99 | $-3.0 | -100.3% | 10000 | 2500 | 0 | 0.0% |
        | Fri, Dec 20 | $3.99 | $1.49 | 37.3% | 0 | 3000 | 25 | 0.83% |
        | Sat, Dec 21 | $6.99 | $2.0 | 28.6% | 0 | 0 | 0 | 0% |
      MARKDOWN

      assert_equal expected_output.strip, builder.build.strip
    end
  end
end
