require "test_helper"

module ContextBuilder
  module Partner
    class BasicInfoBuilderTest < ActiveSupport::TestCase
      setup do
        @partner = partners(:one)
      end

      def test_build_returns_formatted_output_with_all_fields
        builder = ContextBuilder::Partner::BasicInfoBuilder.new(@partner)

        expected_output = <<~MARKDOWN
          # One

          | Field | Value |
          |-------|-------|
          | ID | 201 |
          | Website | https://www.one.io |
          | Headline | Mobile-first advertising platform |
          | Main Category | Direct |
          | Description | Mobile-first advertising platform specializing in app installs |
        MARKDOWN

        assert_equal expected_output.strip, builder.build.strip
      end
    end
  end
end
