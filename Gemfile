source "https://rubygems.org"

# Bundle edge Rails instead: gem "rails", github: "rails/rails", branch: "main"
gem "rails", github: "rails/rails", branch: "main"
# The original asset pipeline for Rails [https://github.com/rails/sprockets-rails]
gem "sprockets-rails"
# Use postgresql as the database for Active Record
gem "pg", "~> 1.5"
# Use the Puma web server [https://github.com/puma/puma]
gem "puma", ">= 5.0"
# Use JavaScript with ESM import maps [https://github.com/rails/importmap-rails]
gem "importmap-rails"
# Hotwire's SPA-like page accelerator [https://turbo.hotwired.dev]
gem "turbo-rails"
# Hotwire's modest JavaScript framework [https://stimulus.hotwired.dev]
gem "stimulus-rails"
# Use Tailwind CSS [https://github.com/rails/tailwindcss-rails]
gem "tailwindcss-rails", "~> 3.3.1"
# Build JSON APIs with ease [https://github.com/rails/jbuilder]
gem "jbuilder"
# Use Redis adapter to run Action Cable in production
# gem "redis", ">= 4.0.1"

# Use Kredis to get higher-level data types in Redis [https://github.com/rails/kredis]
# gem "kredis"

# Use Active Model has_secure_password [https://guides.rubyonrails.org/active_model_basics.html#securepassword]
gem "bcrypt", "~> 3.1.7"

# Windows does not include zoneinfo files, so bundle the tzinfo-data gem
gem "tzinfo-data", platforms: %i[ windows jruby ]

# Reduces boot times through caching; required in config/boot.rb
gem "bootsnap", require: false

# Use Active Storage variants [https://guides.rubyonrails.org/active_storage_overview.html#transforming-images]
gem "image_processing", "~> 1.14"

# Used to query the external databases
gem "sequel"

# Used to make HTTP requests
gem "httpx"

# Used to parse the CSV
gem "csv"

gem "smarter_csv"

# Add SVG icons from various libraries (like Heroicons, Lucide Icons and Feather Icons)
gem "rails_icons"

# Pagination library
gem "pagy"

# Background job processing library
gem "solid_queue"
gem "mission_control-jobs"

# For action cable
gem "solid_cable"

# Build previews for your partials, components, view helpers, Stimulus controllers and more
gem "rouge", require: false

# Deploy this application anywhere as a Docker container [https://kamal-deploy.org]
gem "kamal", require: false

# Add HTTP asset caching/compression and X-Sendfile acceleration to Puma [https://github.com/basecamp/thruster/]
gem "thruster", require: false

# To efficiently merge Tailwind CSS classes without style conflicts
gem "tailwind_merge"

# https://github.com/googleapis/google-api-ruby-client/tree/main/generated/google-apis-sheets_v4
# As google-api-client is deprecated, and will receive no more updates
gem "google-apis-sheets_v4"
gem "google-apis-gmail_v1"
gem "google-auth-library-ruby"

# Google sign in
gem "google_sign_in"

# Manipulates strings with word parsing rules of UNIX Bourne shell
gem "shellwords"

# Role based authorization
gem "pundit"

# Charting library
gem "chartkick"

# Production s3 storage
gem "aws-sdk-s3", require: false

# AWS credentials
gem "aws-sdk-core"

# AWS bedrock
gem "aws-sdk-bedrockruntime"

# Vectorize PG
gem "neighbor"

gem "audited"

gem "discard"

# Full text search
gem "pg_search"

# Track visits
gem "ahoy_matey"

# Slack integration
gem "slack-ruby-client"

# Generate chart image
gem "quickchart"

# Group by date
gem "groupdate"

# Math expression evaluator
gem "keisan"

# Github integration
gem "octokit"

# Versioned database
gem "scenic"

# Hubspot integration
gem "hubspot-api-client"

# Qdrant Vector Database
gem "qdrant-ruby"


# Feature releasing
gem "flipper"
gem "flipper-active_record"
gem "flipper-ui"

gem "faraday-retry"

# https://github.com/countries/countries
gem "countries"

# Used to download files from the internet
gem "down"

gem "ruby-vips"

gem "xgb"

group :development, :test do
  # See https://guides.rubyonrails.org/debugging_rails_applications.html#debugging-with-the-debug-gem
  gem "debug", platforms: %i[ mri windows ], require: "debug/prelude"

  # Static analysis for security vulnerabilities [https://brakemanscanner.org/]
  gem "brakeman", require: false

  # Omakase Ruby styling [https://github.com/rails/rubocop-rails-omakase/]
  gem "rubocop-rails-omakase", require: false
end

group :development do
  # Use console on exceptions pages [https://github.com/rails/web-console]
  gem "web-console"

  # Festive themed error pages
  gem "festive_errors"
end

group :test do
  # Use system testing [https://guides.rubyonrails.org/testing.html#system-testing]
  gem "capybara"
  gem "selenium-webdriver", "4.34.0"
  gem "rails-controller-testing", "~> 1.0"
end

group :production do
  gem "stackprof"
  gem "sentry-ruby"
  gem "sentry-rails"
end
