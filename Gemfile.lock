GIT
  remote: https://github.com/rails/rails.git
  revision: 6456fc58110ed6915f7e40fff3f9b8829cd67099
  branch: main
  specs:
    actioncable (8.1.0.alpha)
      actionpack (= 8.1.0.alpha)
      activesupport (= 8.1.0.alpha)
      nio4r (~> 2.0)
      websocket-driver (>= 0.6.1)
      zeitwerk (~> 2.6)
    actionmailbox (8.1.0.alpha)
      actionpack (= 8.1.0.alpha)
      activejob (= 8.1.0.alpha)
      activerecord (= 8.1.0.alpha)
      activestorage (= 8.1.0.alpha)
      activesupport (= 8.1.0.alpha)
      mail (>= 2.8.0)
    actionmailer (8.1.0.alpha)
      actionpack (= 8.1.0.alpha)
      actionview (= 8.1.0.alpha)
      activejob (= 8.1.0.alpha)
      activesupport (= 8.1.0.alpha)
      mail (>= 2.8.0)
      rails-dom-testing (~> 2.2)
    actionpack (8.1.0.alpha)
      actionview (= 8.1.0.alpha)
      activesupport (= 8.1.0.alpha)
      nokogiri (>= 1.8.5)
      rack (>= 2.2.4)
      rack-session (>= 1.0.1)
      rack-test (>= 0.6.3)
      rails-dom-testing (~> 2.2)
      rails-html-sanitizer (~> 1.6)
      useragent (~> 0.16)
    actiontext (8.1.0.alpha)
      action_text-trix (~> 2.1.15)
      actionpack (= 8.1.0.alpha)
      activerecord (= 8.1.0.alpha)
      activestorage (= 8.1.0.alpha)
      activesupport (= 8.1.0.alpha)
      globalid (>= 0.6.0)
      nokogiri (>= 1.8.5)
    actionview (8.1.0.alpha)
      activesupport (= 8.1.0.alpha)
      builder (~> 3.1)
      erubi (~> 1.11)
      rails-dom-testing (~> 2.2)
      rails-html-sanitizer (~> 1.6)
    activejob (8.1.0.alpha)
      activesupport (= 8.1.0.alpha)
      globalid (>= 0.3.6)
    activemodel (8.1.0.alpha)
      activesupport (= 8.1.0.alpha)
    activerecord (8.1.0.alpha)
      activemodel (= 8.1.0.alpha)
      activesupport (= 8.1.0.alpha)
      timeout (>= 0.4.0)
    activestorage (8.1.0.alpha)
      actionpack (= 8.1.0.alpha)
      activejob (= 8.1.0.alpha)
      activerecord (= 8.1.0.alpha)
      activesupport (= 8.1.0.alpha)
      marcel (~> 1.0)
    activesupport (8.1.0.alpha)
      base64
      benchmark (>= 0.3)
      bigdecimal
      concurrent-ruby (~> 1.0, >= 1.3.1)
      connection_pool (>= 2.2.5)
      drb
      i18n (>= 1.6, < 2)
      logger (>= 1.4.2)
      minitest (>= 5.1)
      securerandom (>= 0.3)
      tzinfo (~> 2.0, >= 2.0.5)
      uri (>= 0.13.1)
    rails (8.1.0.alpha)
      actioncable (= 8.1.0.alpha)
      actionmailbox (= 8.1.0.alpha)
      actionmailer (= 8.1.0.alpha)
      actionpack (= 8.1.0.alpha)
      actiontext (= 8.1.0.alpha)
      actionview (= 8.1.0.alpha)
      activejob (= 8.1.0.alpha)
      activemodel (= 8.1.0.alpha)
      activerecord (= 8.1.0.alpha)
      activestorage (= 8.1.0.alpha)
      activesupport (= 8.1.0.alpha)
      bundler (>= 1.15.0)
      railties (= 8.1.0.alpha)
    railties (8.1.0.alpha)
      actionpack (= 8.1.0.alpha)
      activesupport (= 8.1.0.alpha)
      irb (~> 1.13)
      rackup (>= 1.0.0)
      rake (>= 12.2)
      thor (~> 1.0, >= 1.2.2)
      zeitwerk (~> 2.6)

GEM
  remote: https://rubygems.org/
  specs:
    action_text-trix (2.1.15)
      railties
    addressable (2.8.7)
      public_suffix (>= 2.0.2, < 7.0)
    ahoy_matey (5.4.0)
      activesupport (>= 7.1)
      device_detector (>= 1)
      safely_block (>= 0.4)
    ast (2.4.2)
    audited (5.8.0)
      activerecord (>= 5.2, < 8.2)
      activesupport (>= 5.2, < 8.2)
    aws-eventstream (1.4.0)
    aws-partitions (1.1120.0)
    aws-sdk-bedrockruntime (1.49.0)
      aws-sdk-core (~> 3, >= 3.225.0)
      aws-sigv4 (~> 1.5)
    aws-sdk-core (3.226.1)
      aws-eventstream (~> 1, >= 1.3.0)
      aws-partitions (~> 1, >= 1.992.0)
      aws-sigv4 (~> 1.9)
      base64
      jmespath (~> 1, >= 1.6.1)
      logger
    aws-sdk-kms (1.105.0)
      aws-sdk-core (~> 3, >= 3.225.0)
      aws-sigv4 (~> 1.5)
    aws-sdk-s3 (1.191.0)
      aws-sdk-core (~> 3, >= 3.225.0)
      aws-sdk-kms (~> 1)
      aws-sigv4 (~> 1.5)
    aws-sigv4 (1.12.1)
      aws-eventstream (~> 1, >= 1.0.2)
    base64 (0.3.0)
    bcrypt (3.1.20)
    bcrypt_pbkdf (1.1.1)
    benchmark (0.4.1)
    bigdecimal (3.2.2)
    bindex (0.8.1)
    bootsnap (1.18.6)
      msgpack (~> 1.2)
    brakeman (7.0.2)
      racc
    builder (3.3.0)
    capybara (3.40.0)
      addressable
      matrix
      mini_mime (>= 0.1.3)
      nokogiri (~> 1.11)
      rack (>= 1.6.0)
      rack-test (>= 0.6.3)
      regexp_parser (>= 1.5, < 3.0)
      xpath (~> 3.2)
    chartkick (5.2.0)
    cmath (1.0.0)
    concurrent-ruby (1.3.5)
    connection_pool (2.5.3)
    countries (8.0.2)
      unaccent (~> 0.3)
    crass (1.0.6)
    csv (3.3.5)
    date (3.4.1)
    debug (1.11.0)
      irb (~> 1.10)
      reline (>= 0.3.8)
    declarative (0.0.20)
    device_detector (1.1.3)
    discard (1.4.0)
      activerecord (>= 4.2, < 9.0)
    dotenv (3.1.8)
    down (5.4.2)
      addressable (~> 2.8)
    drb (2.2.3)
    ed25519 (1.4.0)
    erb (5.0.1)
    erubi (1.13.1)
    et-orbi (1.2.11)
      tzinfo
    ethon (0.16.0)
      ffi (>= 1.15.0)
    faraday (2.13.1)
      faraday-net_http (>= 2.0, < 3.5)
      json
      logger
    faraday-mashify (1.0.0)
      faraday (~> 2.0)
      hashie
    faraday-multipart (1.1.0)
      multipart-post (~> 2.0)
    faraday-net_http (3.4.1)
      net-http (>= 0.5.0)
    faraday-retry (2.3.2)
      faraday (~> 2.0)
    festive_errors (0.0.2)
      rails (>= 7.0.0)
    ffi (1.17.2-aarch64-linux-gnu)
    ffi (1.17.2-arm-linux-gnu)
    ffi (1.17.2-arm64-darwin)
    ffi (1.17.2-x86-linux-gnu)
    ffi (1.17.2-x86_64-darwin)
    ffi (1.17.2-x86_64-linux-gnu)
    flipper (1.3.5)
      concurrent-ruby (< 2)
    flipper-active_record (1.3.5)
      activerecord (>= 4.2, < 9)
      flipper (~> 1.3.5)
    flipper-ui (1.3.4)
      erubi (>= 1.0.0, < 2.0.0)
      flipper (~> 1.3.4)
      rack (>= 1.4, < 4)
      rack-protection (>= 1.5.3, < 5.0.0)
      rack-session (>= 1.0.2, < 3.0.0)
      sanitize (< 8)
    fugit (1.11.1)
      et-orbi (~> 1, >= 1.2.11)
      raabro (~> 1.4)
    gli (2.22.2)
      ostruct
    globalid (1.2.1)
      activesupport (>= 6.1)
    google-apis-core (0.18.0)
      addressable (~> 2.5, >= 2.5.1)
      googleauth (~> 1.9)
      httpclient (>= 2.8.3, < 3.a)
      mini_mime (~> 1.0)
      mutex_m
      representable (~> 3.0)
      retriable (>= 2.0, < 4.a)
    google-apis-gmail_v1 (0.44.0)
      google-apis-core (>= 0.15.0, < 2.a)
    google-apis-sheets_v4 (0.45.0)
      google-apis-core (>= 0.15.0, < 2.a)
    google-auth-library-ruby (9001.0)
    google-cloud-env (2.3.1)
      base64 (~> 0.2)
      faraday (>= 1.0, < 3.a)
    google-id-token (1.4.2)
      jwt (>= 1)
    google-logging-utils (0.2.0)
    google_sign_in (1.2.1)
      google-id-token (>= 1.4.0)
      oauth2 (>= 1.4.0)
      rails (>= 5.2.0)
    googleauth (1.14.0)
      faraday (>= 1.0, < 3.a)
      google-cloud-env (~> 2.2)
      google-logging-utils (~> 0.1)
      jwt (>= 1.4, < 3.0)
      multi_json (~> 1.11)
      os (>= 0.9, < 2.0)
      signet (>= 0.16, < 2.a)
    groupdate (6.7.0)
      activesupport (>= 7.1)
    hashie (5.0.0)
    http-2 (1.1.1)
    httpclient (2.9.0)
      mutex_m
    httpx (1.5.1)
      http-2 (>= 1.0.0)
    hubspot-api-client (20.0.0)
      json (~> 2.1, >= 2.1.0)
      typhoeus (~> 1.4.0)
    i18n (1.14.7)
      concurrent-ruby (~> 1.0)
    image_processing (1.14.0)
      mini_magick (>= 4.9.5, < 6)
      ruby-vips (>= 2.0.17, < 3)
    importmap-rails (2.1.0)
      actionpack (>= 6.0.0)
      activesupport (>= 6.0.0)
      railties (>= 6.0.0)
    io-console (0.8.0)
    irb (1.15.2)
      pp (>= 0.6.0)
      rdoc (>= 4.0.0)
      reline (>= 0.4.2)
    jbuilder (2.13.0)
      actionview (>= 5.0.0)
      activesupport (>= 5.0.0)
    jmespath (1.6.2)
    json (2.12.2)
    jwt (2.10.1)
      base64
    kamal (2.7.0)
      activesupport (>= 7.0)
      base64 (~> 0.2)
      bcrypt_pbkdf (~> 1.0)
      concurrent-ruby (~> 1.2)
      dotenv (~> 3.1)
      ed25519 (~> 1.4)
      net-ssh (~> 7.3)
      sshkit (>= 1.23.0, < 2.0)
      thor (~> 1.3)
      zeitwerk (>= 2.6.18, < 3.0)
    keisan (0.9.2)
      cmath (~> 1.0)
    language_server-protocol (********)
    lint_roller (1.1.0)
    logger (1.7.0)
    loofah (2.24.1)
      crass (~> 1.0.2)
      nokogiri (>= 1.12.0)
    mail (2.8.1)
      mini_mime (>= 0.1.1)
      net-imap
      net-pop
      net-smtp
    marcel (1.0.4)
    matrix (0.4.2)
    mini_magick (5.1.2)
      benchmark
      logger
    mini_mime (1.1.5)
    mini_portile2 (2.8.9)
    minitest (5.25.5)
    mission_control-jobs (1.0.2)
      actioncable (>= 7.1)
      actionpack (>= 7.1)
      activejob (>= 7.1)
      activerecord (>= 7.1)
      importmap-rails (>= 1.2.1)
      irb (~> 1.13)
      railties (>= 7.1)
      stimulus-rails
      turbo-rails
    msgpack (1.8.0)
    multi_json (1.15.0)
    multi_xml (0.7.1)
      bigdecimal (~> 3.1)
    multipart-post (2.4.1)
    mutex_m (0.3.0)
    neighbor (0.6.0)
      activerecord (>= 7.1)
    net-http (0.6.0)
      uri
    net-imap (0.5.9)
      date
      net-protocol
    net-pop (0.1.2)
      net-protocol
    net-protocol (0.2.2)
      timeout
    net-scp (4.1.0)
      net-ssh (>= 2.6.5, < 8.0.0)
    net-sftp (4.0.0)
      net-ssh (>= 5.0.0, < 8.0.0)
    net-smtp (0.5.1)
      net-protocol
    net-ssh (7.3.0)
    nio4r (2.7.4)
    nokogiri (1.18.8)
      mini_portile2 (~> 2.8.2)
      racc (~> 1.4)
    nokogiri (1.18.8-aarch64-linux-gnu)
      racc (~> 1.4)
    nokogiri (1.18.8-arm-linux-gnu)
      racc (~> 1.4)
    nokogiri (1.18.8-arm64-darwin)
      racc (~> 1.4)
    nokogiri (1.18.8-x86_64-darwin)
      racc (~> 1.4)
    nokogiri (1.18.8-x86_64-linux-gnu)
      racc (~> 1.4)
    oauth2 (2.0.9)
      faraday (>= 0.17.3, < 3.0)
      jwt (>= 1.0, < 3.0)
      multi_xml (~> 0.5)
      rack (>= 1.2, < 4)
      snaky_hash (~> 2.0)
      version_gem (~> 1.1)
    octokit (10.0.0)
      faraday (>= 1, < 3)
      sawyer (~> 0.9)
    os (1.1.4)
    ostruct (0.6.2)
    pagy (9.3.4)
    parallel (1.26.3)
    parser (3.3.7.1)
      ast (~> 2.4.1)
      racc
    pg (1.5.9)
    pg_search (2.3.7)
      activerecord (>= 6.1)
      activesupport (>= 6.1)
    pp (0.6.2)
      prettyprint
    prettyprint (0.2.0)
    psych (5.2.6)
      date
      stringio
    public_suffix (6.0.2)
    puma (6.6.0)
      nio4r (~> 2.0)
    pundit (2.5.0)
      activesupport (>= 3.0.0)
    qdrant-ruby (0.9.9)
      faraday (>= 2.0.1, < 3)
    quickchart (1.2.1)
    raabro (1.4.0)
    racc (1.8.1)
    rack (3.1.16)
    rack-protection (4.1.1)
      base64 (>= 0.1.0)
      logger (>= 1.6.0)
      rack (>= 3.0.0, < 4)
    rack-session (2.1.1)
      base64 (>= 0.1.0)
      rack (>= 3.0.0)
    rack-test (2.2.0)
      rack (>= 1.3)
    rackup (2.2.1)
      rack (>= 3)
    rails-controller-testing (1.0.5)
      actionpack (>= 5.0.1.rc1)
      actionview (>= 5.0.1.rc1)
      activesupport (>= 5.0.1.rc1)
    rails-dom-testing (2.3.0)
      activesupport (>= 5.0.0)
      minitest
      nokogiri (>= 1.6)
    rails-html-sanitizer (1.6.2)
      loofah (~> 2.21)
      nokogiri (>= 1.15.7, != 1.16.7, != 1.16.6, != 1.16.5, != 1.16.4, != 1.16.3, != 1.16.2, != 1.16.1, != 1.16.0.rc1, != 1.16.0)
    rails_icons (1.3.0)
      nokogiri (~> 1.16, >= 1.16.4)
      rails (> 6.1)
    rainbow (3.1.1)
    rake (13.3.0)
    rdoc (6.14.1)
      erb
      psych (>= 4.0.0)
    regexp_parser (2.10.0)
    reline (0.6.1)
      io-console (~> 0.5)
    representable (3.2.0)
      declarative (< 0.1.0)
      trailblazer-option (>= 0.1.1, < 0.2.0)
      uber (< 0.2.0)
    retriable (3.1.2)
    rexml (3.4.1)
    rouge (4.5.1)
    rubocop (1.72.2)
      json (~> 2.3)
      language_server-protocol (~> 3.17.0.2)
      lint_roller (~> 1.1.0)
      parallel (~> 1.10)
      parser (>= 3.3.0.2)
      rainbow (>= 2.2.2, < 4.0)
      regexp_parser (>= 2.9.3, < 3.0)
      rubocop-ast (>= 1.38.0, < 2.0)
      ruby-progressbar (~> 1.7)
      unicode-display_width (>= 2.4.0, < 4.0)
    rubocop-ast (1.38.0)
      parser (>= *******)
    rubocop-performance (1.24.0)
      lint_roller (~> 1.1)
      rubocop (>= 1.72.1, < 2.0)
      rubocop-ast (>= 1.38.0, < 2.0)
    rubocop-rails (2.30.2)
      activesupport (>= 4.2.0)
      lint_roller (~> 1.1)
      rack (>= 1.1)
      rubocop (>= 1.72.1, < 2.0)
      rubocop-ast (>= 1.38.0, < 2.0)
    rubocop-rails-omakase (1.1.0)
      rubocop (>= 1.72)
      rubocop-performance (>= 1.24)
      rubocop-rails (>= 2.30)
    ruby-progressbar (1.13.0)
    ruby-vips (2.2.4)
      ffi (~> 1.12)
      logger
    rubyzip (2.4.1)
    safely_block (0.4.1)
    sanitize (7.0.0)
      crass (~> 1.0.2)
      nokogiri (>= 1.16.8)
    sawyer (0.9.2)
      addressable (>= 2.3.5)
      faraday (>= 0.17.3, < 3)
    scenic (1.8.0)
      activerecord (>= 4.0.0)
      railties (>= 4.0.0)
    securerandom (0.4.1)
    selenium-webdriver (4.34.0)
      base64 (~> 0.2)
      logger (~> 1.4)
      rexml (~> 3.2, >= 3.2.5)
      rubyzip (>= 1.2.2, < 3.0)
      websocket (~> 1.0)
    sentry-rails (5.26.0)
      railties (>= 5.0)
      sentry-ruby (~> 5.26.0)
    sentry-ruby (5.26.0)
      bigdecimal
      concurrent-ruby (~> 1.0, >= 1.0.2)
    sequel (5.90.0)
      bigdecimal
    shellwords (0.2.2)
    signet (0.20.0)
      addressable (~> 2.8)
      faraday (>= 0.17.5, < 3.a)
      jwt (>= 1.5, < 3.0)
      multi_json (~> 1.10)
    sin_lru_redux (2.5.2)
    slack-ruby-client (2.6.0)
      faraday (>= 2.0)
      faraday-mashify
      faraday-multipart
      gli
      hashie
      logger
    smarter_csv (1.14.4)
    snaky_hash (2.0.1)
      hashie
      version_gem (~> 1.1, >= 1.1.1)
    solid_cable (3.0.11)
      actioncable (>= 7.2)
      activejob (>= 7.2)
      activerecord (>= 7.2)
      railties (>= 7.2)
    solid_queue (1.1.0)
      activejob (>= 7.1)
      activerecord (>= 7.1)
      concurrent-ruby (>= 1.3.1)
      fugit (~> 1.11.0)
      railties (>= 7.1)
      thor (~> 1.3.1)
    sprockets (4.2.1)
      concurrent-ruby (~> 1.0)
      rack (>= 2.2.4, < 4)
    sprockets-rails (3.5.2)
      actionpack (>= 6.1)
      activesupport (>= 6.1)
      sprockets (>= 3.0.0)
    sshkit (1.24.0)
      base64
      logger
      net-scp (>= 1.1.2)
      net-sftp (>= 2.1.2)
      net-ssh (>= 2.8.0)
      ostruct
    stackprof (0.2.27)
    stimulus-rails (1.3.4)
      railties (>= 6.0.0)
    stringio (3.1.7)
    tailwind_merge (1.3.1)
      sin_lru_redux (~> 2.5)
    tailwindcss-rails (3.3.1)
      railties (>= 7.0.0)
      tailwindcss-ruby (~> 3.0)
    tailwindcss-ruby (3.4.17)
    tailwindcss-ruby (3.4.17-aarch64-linux)
    tailwindcss-ruby (3.4.17-arm-linux)
    tailwindcss-ruby (3.4.17-arm64-darwin)
    tailwindcss-ruby (3.4.17-x86_64-darwin)
    tailwindcss-ruby (3.4.17-x86_64-linux)
    thor (1.3.2)
    thruster (0.1.14)
    thruster (0.1.14-aarch64-linux)
    thruster (0.1.14-arm64-darwin)
    thruster (0.1.14-x86_64-darwin)
    thruster (0.1.14-x86_64-linux)
    timeout (0.4.3)
    trailblazer-option (0.1.2)
    turbo-rails (2.0.16)
      actionpack (>= 7.1.0)
      railties (>= 7.1.0)
    typhoeus (1.4.1)
      ethon (>= 0.9.0)
    tzinfo (2.0.6)
      concurrent-ruby (~> 1.0)
    uber (0.1.0)
    unaccent (0.4.0)
    unicode-display_width (3.1.4)
      unicode-emoji (~> 4.0, >= 4.0.4)
    unicode-emoji (4.0.4)
    uri (1.0.3)
    useragent (0.16.11)
    version_gem (1.1.4)
    web-console (4.2.1)
      actionview (>= 6.0.0)
      activemodel (>= 6.0.0)
      bindex (>= 0.4.0)
      railties (>= 6.0.0)
    websocket (1.2.11)
    websocket-driver (0.8.0)
      base64
      websocket-extensions (>= 0.1.0)
    websocket-extensions (0.1.5)
    xgb (0.10.0)
      ffi
    xpath (3.2.0)
      nokogiri (~> 1.8)
    zeitwerk (2.7.3)

PLATFORMS
  aarch64-linux
  arm-linux
  arm64-darwin
  x86-linux
  x86_64-darwin
  x86_64-linux

DEPENDENCIES
  ahoy_matey
  audited
  aws-sdk-bedrockruntime
  aws-sdk-core
  aws-sdk-s3
  bcrypt (~> 3.1.7)
  bootsnap
  brakeman
  capybara
  chartkick
  countries
  csv
  debug
  discard
  down
  faraday-retry
  festive_errors
  flipper
  flipper-active_record
  flipper-ui
  google-apis-gmail_v1
  google-apis-sheets_v4
  google-auth-library-ruby
  google_sign_in
  groupdate
  httpx
  hubspot-api-client
  image_processing (~> 1.14)
  importmap-rails
  jbuilder
  kamal
  keisan
  mission_control-jobs
  neighbor
  octokit
  pagy
  pg (~> 1.5)
  pg_search
  puma (>= 5.0)
  pundit
  qdrant-ruby
  quickchart
  rails!
  rails-controller-testing (~> 1.0)
  rails_icons
  rouge
  rubocop-rails-omakase
  ruby-vips
  scenic
  selenium-webdriver (= 4.34.0)
  sentry-rails
  sentry-ruby
  sequel
  shellwords
  slack-ruby-client
  smarter_csv
  solid_cable
  solid_queue
  sprockets-rails
  stackprof
  stimulus-rails
  tailwind_merge
  tailwindcss-rails (~> 3.3.1)
  thruster
  turbo-rails
  tzinfo-data
  web-console
  xgb

BUNDLED WITH
   2.5.17
