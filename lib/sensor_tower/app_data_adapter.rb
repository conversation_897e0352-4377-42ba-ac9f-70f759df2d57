require "ostruct"

module SensorTower
  class AppDataAdapter
    class AppNotFoundError < StandardError; end

    attr_reader :app_id, :name, :artwork, :description, :current_rating, :rating_count,
                :genre_names, :primary_genre, :url, :price, :author, :store_type, :platform

    def initialize(sensor_tower_data, bundle_id, platform)
      @platform = platform
      @store_type = platform == "ios" ? :app_store : :google_play

      # Find the specific app data from the response
      app_data = extract_app_data(sensor_tower_data, bundle_id)
      raise AppNotFoundError, "App not found for bundle_id: #{bundle_id}" unless app_data

      # Map SensorTower data to AppStoreParser-like structure
      map_app_data(app_data)
    end

    def to_hash
      {
        id: @app_id,
        name: @name,
        artwork: @artwork,
        description: @description,
        current_rating: @current_rating,
        rating_count: @rating_count,
        genre_names: @genre_names,
        primary_genre: @primary_genre,
        url: @url,
        price: @price,
        author: @author,
        store_type: @store_type
      }
    end

    def to_json(*args)
      to_hash.to_json(*args)
    end

    private

    def extract_app_data(sensor_tower_data, bundle_id)
      return nil unless sensor_tower_data.is_a?(Hash) && sensor_tower_data["apps"].is_a?(Array)

      bundle_id_str = bundle_id.to_s
      sensor_tower_data["apps"].find do |app|
        next unless app
        app["app_id"].to_s == bundle_id_str
      end
    end

    def map_app_data(app_data)
      @app_id = app_data["app_id"]
      @name = app_data["name"] || app_data["title"] || ""
      @artwork = app_data["icon_url"] || app_data["artwork_url"]
      @description = app_data["description"] || ""
      @current_rating = app_data["rating"]
      @rating_count = app_data["rating_count"]
      @url = app_data["store_url"] || build_store_url
      @price = app_data["price"] || 0
      <AUTHOR> app_data["publisher"] || app_data["developer"] || ""

      # Map categories - SensorTower might return categories differently
      @genre_names = extract_categories(app_data)
      @primary_genre = @genre_names&.first
    end

    def extract_categories(app_data)
      # SensorTower might have different category field names
      categories = app_data["categories"] ||
                  app_data["genre"] ||
                  app_data["primary_category"] ||
                  app_data["category"]

      case categories
      when Array
        categories.compact
      when String
        [ categories ]
      else
        []
      end
    end

    def build_store_url
      return nil unless @app_id

      case @platform
      when "ios"
        "https://apps.apple.com/app/id#{@app_id}"
      when "android"
        "https://play.google.com/store/apps/details?id=#{@app_id}"
      end
    end
  end
end
