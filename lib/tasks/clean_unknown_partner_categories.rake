namespace :clean_unknown_partner_categories do
  desc "Remove partner categories that don't match formatted vendor categories and add missing ones"
  task execute: :environment do
    # Define the mapping between existing categories and formatted vendor categories
    valid_category_mappings = {
      "Affiliate Network" => :affiliate,
      "Ad Network" => :ad_network,
      "Demand Side Platform (DSP)" => :dsp,
      "OEM / Preload partner" => :oem,
      "Incent" => :incent_network,
      "Native" => :native_platform,
      "O&O" => :o_o,
      "Email Marketing" => :email,
      "Influencer Marketing Platform" => :influencer,
      "CTV Partner" => :ctv,
      "Browser & Search Partner" => :browser
    }

    # Add missing categories that exist in formatted vendor categories but not in mappings
    missing_category_mappings = {
      "Direct" => :direct,
      "Network" => :network,
      "Media Buyer" => :media_buyer,
      "Pop Platform" => :pop_platform,
      "SDK Network" => :sdk_network,
      "Incent O&O" => :incent_o_o
    }

    all_valid_mappings = valid_category_mappings.merge(missing_category_mappings)
    valid_category_names = all_valid_mappings.keys

    puts "Finding partner categories to remove..."

    # Find categories that don't match any valid category
    categories_to_remove = PartnerCategory.where.not(name: valid_category_names)

    unless categories_to_remove.empty?
      puts "Categories to be removed:"
      categories_to_remove.each do |category|
        partner_count = category.partners.count
        puts "  - #{category.name} (#{partner_count} partners associated)"
      end

      ActiveRecord::Base.transaction do
        categories_to_remove.each do |category|
          puts "Removing category: #{category.name}"

          # Remove partner categorizations first
          category.partner_categorizations.destroy_all

          # Update partners that have this as main_category
          Partner.where(main_category: category).update_all(main_category_id: nil)

          # Remove the category
          category.destroy!
        end
      end
    else
      puts "No categories to remove. All existing categories are valid."
    end

    # Add missing categories
    puts "\nChecking for missing categories to add..."

    existing_category_names = PartnerCategory.pluck(:name)
    categories_to_add = valid_category_names - existing_category_names

    if categories_to_add.any?
      puts "Adding missing categories:"
      categories_to_add.each do |category_name|
        PartnerCategory.create!(name: category_name)
        puts "  + #{category_name}"
      end
      puts "Successfully added #{categories_to_add.count} categories."
    else
      puts "No missing categories to add."
    end
  end
end
