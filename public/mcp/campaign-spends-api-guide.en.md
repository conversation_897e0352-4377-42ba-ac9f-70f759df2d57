# CampaignSpendsApiQuery User Manual

## Overview
CampaignSpendsApiQuery is a Ruby class for querying advertising campaign data, providing flexible data aggregation and filtering capabilities.

## Main Parameters

### Date Parameters
- **date_gteq**: Start date (string format: "YYYY-MM-DD")
  - Default: First day of previous month
  - Example: "2024-01-01"

- **date_lteq**: End date (string format: "YYYY-MM-DD")
  - Default: Yesterday
  - Example: "2024-01-31"

### Grouping Parameters
- **groups**: Data grouping methods (string array)
  - Available options:
    - "day": Group by day
    - "week": Group by week
    - "month": Group by month
    - "client": Group by client
    - "partner": Group by partner
    - "campaign": Group by campaign
    - "click_url": Group by click URL
    - "country": Group by country
  - Default: ["campaign", "partner"]

### Metric Parameters
- **metrics**: Metrics to return (string array)
  - Available options:
    - "gross": Total spend
    - "net": Net spend
    - "revenue": Revenue
    - "impressions": Impression count
    - "clicks": Click count
    - "installs": Install count
    - "cvr": Conversion rate (%)
    - "margin": Profit margin (%)
  - Default: ["gross", "net"]

### Filter Parameters
- **legacy_client_id_in**: Client ID filter (string array)
  - Example: ["123", "456"]

- **legacy_partner_id_in**: Partner ID filter (string array)
  - Example: ["789", "101"]

- **legacy_campaign_id_in**: Campaign ID filter (string array)
  - Example: ["111", "222"]

- **legacy_click_url_id_in**: Click URL ID filter (string array)
  - Example: ["333", "444"]

## Response Data Structure

Query results are returned as a hash table with grouping names as keys and metric data objects as values:

```json
[
  {
    "day": "2024-01-01",
    "client": "Client A",
    "partner": "Partner X",
    "gross": 1000.50,
    "net": 800.25,
    "revenue": 1200.00,
    "impressions": 50000,
    "clicks": 1500,
    "installs": 150,
    "cvr": 10.0,
    "margin": 20.0
  }
]
```

## Usage Examples

### 1. Basic Query - Daily grouped data for specified date range
```
Parameters:
{
  "date_gteq": "2024-01-01",
  "date_lteq": "2024-01-31",
  "groups": ["day"],
  "metrics": ["gross", "net", "clicks"]
}
```

### 2. Client Grouped Query
```
Parameters:
{
  "groups": ["client"],
  "metrics": ["gross", "net", "revenue", "margin"],
  "legacy_client_id_in": ["123", "456"]
}
```

### 3. Campaign Grouped Query with Conversion Data
```
Parameters:
{
  "groups": ["campaign"],
  "metrics": ["clicks", "installs", "cvr"],
  "legacy_campaign_id_in": ["111", "222"]
}
```

### 4. Monthly Grouped Data for Specified Date Range with Default Metrics
```
Parameters:
{
  "date_gteq": "2024-01-01",
  "date_lteq": "2024-12-31",
  "groups": ["month"],
  "metrics": ["gross", "net"]
}
```

### 5. Query by Client IDs with Default Grouping ["campaign", "partner"]
- First use Search Ids tool to find client ids by keywords
```
Parameters:
{
  "legacy_client_id_in": ["123", "456"],
  "date_gteq": "2024-01-01",
  "date_lteq": "2024-12-31",
  "groups": ["campaign", "partner"],
  "metrics": ["gross", "net"]
}

### 6. Query by Vendor/Partner IDs with Default Grouping ["campaign", "partner"]
- First use Search Ids tool to find vendor/partner ids by keywords
```
Parameters:
{
  "legacy_partner_id_in": ["123", "456"],
  "date_gteq": "2024-01-01",
  "date_lteq": "2024-12-31",
  "groups": ["campaign", "partner"],
  "metrics": ["gross", "net"]
}

### 7. Query by Campaign IDs with Specified Metrics, Grouped by ["campaign"]
- First use Search Ids tool to find campaign ids by keywords
```
Parameters:
{
  "legacy_campaign_id_in": ["123", "456"],
  "date_gteq": "2024-01-01",
  "date_lteq": "2024-12-31",
  "groups": ["campaign"],
  "metrics": ["gross", "net", "margin"]
}
```

## Notes

1. **Date Format**: Must use "YYYY-MM-DD" format
2. **Array Parameters**: Filter conditions support multiple values using array format
3. **Default Filtering**: Automatically filters out test data (is_test: false)
4. **Calculated Fields**: 
   - CVR = (installs / clicks) * 100
   - Margin = ((gross - net) / gross) * 100
5. **Data Precision**: Monetary values rounded to 2 decimal places, percentages to 2 decimal places

## API Calling Method

Make HTTP GET request:
```
GET /api/mcp/campaign_spend.json?date_gteq=2024-01-01&date_lteq=2024-01-31&groups=day&metrics[]=gross&metrics[]=net
```

Array parameters require `[]` suffix, e.g.:
- `metrics[]=gross&metrics[]=net`
- `legacy_client_id_in[]=123&legacy_client_id_in[]=456`
