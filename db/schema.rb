# This file is auto-generated from the current state of the database. Instead
# of editing this file, please use the migrations feature of Active Record to
# incrementally modify your database, and then regenerate this schema definition.
#
# This file is the source <PERSON>s uses to define your schema when running `bin/rails
# db:schema:load`. When creating a new database, `bin/rails db:schema:load` tends to
# be faster and is potentially less error prone than running all of your
# migrations from scratch. Old migrations may fail to apply correctly if those
# migrations use external dependencies or application code.
#
# It's strongly recommended that you check this file into your version control system.

ActiveRecord::Schema[8.1].define(version: 2025_06_30_013637) do
  # These are extensions that must be enabled in order to support this database
  enable_extension "fuzzystrmatch"
  enable_extension "pg_catalog.plpgsql"
  enable_extension "pg_trgm"
  enable_extension "vector"

  create_table "active_storage_attachments", force: :cascade do |t|
    t.bigint "blob_id", null: false
    t.datetime "created_at", null: false
    t.string "name", null: false
    t.bigint "record_id", null: false
    t.string "record_type", null: false
    t.index ["blob_id"], name: "index_active_storage_attachments_on_blob_id"
    t.index ["record_type", "record_id", "name", "blob_id"], name: "index_active_storage_attachments_uniqueness", unique: true
  end

  create_table "active_storage_blobs", force: :cascade do |t|
    t.bigint "byte_size", null: false
    t.string "checksum"
    t.string "content_type"
    t.datetime "created_at", null: false
    t.string "filename", null: false
    t.string "key", null: false
    t.text "metadata"
    t.string "service_name", null: false
    t.index ["key"], name: "index_active_storage_blobs_on_key", unique: true
  end

  create_table "active_storage_variant_records", force: :cascade do |t|
    t.bigint "blob_id", null: false
    t.string "variation_digest", null: false
    t.index ["blob_id", "variation_digest"], name: "index_active_storage_variant_records_uniqueness", unique: true
  end

  create_table "ahoy_events", force: :cascade do |t|
    t.string "name"
    t.jsonb "properties"
    t.datetime "time"
    t.bigint "user_id"
    t.bigint "visit_id"
    t.index ["name", "time"], name: "index_ahoy_events_on_name_and_time"
    t.index ["properties"], name: "index_ahoy_events_on_properties", opclass: :jsonb_path_ops, using: :gin
    t.index ["user_id"], name: "index_ahoy_events_on_user_id"
    t.index ["visit_id"], name: "index_ahoy_events_on_visit_id"
  end

  create_table "ahoy_visits", force: :cascade do |t|
    t.string "app_version"
    t.string "browser"
    t.string "city"
    t.string "country"
    t.string "device_type"
    t.string "ip"
    t.text "landing_page"
    t.float "latitude"
    t.float "longitude"
    t.string "os"
    t.string "os_version"
    t.string "platform"
    t.text "referrer"
    t.string "referring_domain"
    t.string "region"
    t.datetime "started_at"
    t.text "user_agent"
    t.bigint "user_id"
    t.string "utm_campaign"
    t.string "utm_content"
    t.string "utm_medium"
    t.string "utm_source"
    t.string "utm_term"
    t.string "visit_token"
    t.string "visitor_token"
    t.index ["user_id"], name: "index_ahoy_visits_on_user_id"
    t.index ["visit_token"], name: "index_ahoy_visits_on_visit_token", unique: true
    t.index ["visitor_token", "started_at"], name: "index_ahoy_visits_on_visitor_token_and_started_at"
  end

  create_table "alert_dismissals", force: :cascade do |t|
    t.bigint "alert_id", null: false
    t.datetime "created_at", null: false
    t.datetime "dismissed_at"
    t.datetime "updated_at", null: false
    t.bigint "user_id", null: false
    t.index ["alert_id"], name: "index_alert_dismissals_on_alert_id"
    t.index ["user_id"], name: "index_alert_dismissals_on_user_id"
  end

  create_table "alerts", force: :cascade do |t|
    t.boolean "active"
    t.string "alert_type"
    t.text "content"
    t.datetime "created_at", null: false
    t.datetime "end_date"
    t.datetime "start_date"
    t.string "title"
    t.datetime "updated_at", null: false
  end

  create_table "api_playgrounds", force: :cascade do |t|
    t.bigint "client_id"
    t.text "code"
    t.datetime "created_at", null: false
    t.text "description"
    t.string "name"
    t.bigint "partner_id"
    t.integer "rate_limit_count"
    t.string "rate_limit_period"
    t.string "slug"
    t.bigint "track_party_id"
    t.datetime "updated_at", null: false
    t.index ["client_id"], name: "index_api_playgrounds_on_client_id"
    t.index ["partner_id"], name: "index_api_playgrounds_on_partner_id"
    t.index ["slug"], name: "index_api_playgrounds_on_slug", unique: true
    t.index ["track_party_id"], name: "index_api_playgrounds_on_track_party_id"
  end

  create_table "attribution_windows", force: :cascade do |t|
    t.bigint "client_id", null: false
    t.datetime "created_at", null: false
    t.interval "duration"
    t.boolean "probabilistic_enabled"
    t.string "tracking_method"
    t.datetime "updated_at", null: false
    t.string "window_type"
    t.index ["client_id"], name: "index_attribution_windows_on_client_id"
  end

  create_table "audits", force: :cascade do |t|
    t.string "action"
    t.integer "associated_id"
    t.string "associated_type"
    t.integer "auditable_id"
    t.string "auditable_type"
    t.jsonb "audited_changes"
    t.string "comment"
    t.datetime "created_at"
    t.string "remote_address"
    t.string "request_uuid"
    t.integer "user_id"
    t.string "user_type"
    t.string "username"
    t.integer "version", default: 0
    t.index ["associated_type", "associated_id"], name: "associated_index"
    t.index ["auditable_type", "auditable_id", "version"], name: "auditable_index"
    t.index ["created_at"], name: "index_audits_on_created_at"
    t.index ["request_uuid"], name: "index_audits_on_request_uuid"
    t.index ["user_id", "user_type"], name: "user_index"
  end

  create_table "campaign_spend_adjustments", force: :cascade do |t|
    t.decimal "amount", precision: 10, scale: 2, null: false
    t.bigint "campaign_spend_id", null: false
    t.datetime "created_at", null: false
    t.json "metadata"
    t.string "reason", null: false
    t.datetime "updated_at", null: false
    t.index ["campaign_spend_id", "reason"], name: "idx_on_campaign_spend_id_reason_33a5d24320", unique: true
    t.index ["campaign_spend_id"], name: "index_campaign_spend_adjustments_on_campaign_spend_id"
  end

  create_table "campaign_spend_files", force: :cascade do |t|
    t.datetime "created_at", null: false
    t.string "status", default: "pending", null: false
    t.datetime "updated_at", null: false
    t.string "version", null: false
    t.index ["status"], name: "index_campaign_spend_files_on_status"
    t.index ["version"], name: "index_campaign_spend_files_on_version", unique: true
  end

  create_table "campaign_spends", force: :cascade do |t|
    t.json "calculation_metadata"
    t.string "calculation_source", null: false
    t.bigint "click_url_id", null: false
    t.bigint "client_id", null: false
    t.datetime "created_at", null: false
    t.date "date", null: false
    t.bigint "partner_id", null: false
    t.decimal "spend", precision: 10, scale: 2, null: false
    t.string "spend_type", null: false
    t.datetime "updated_at", null: false
    t.index ["click_url_id"], name: "index_campaign_spends_on_click_url_id"
    t.index ["client_id"], name: "index_campaign_spends_on_client_id"
    t.index ["date", "click_url_id", "spend_type"], name: "index_campaign_spends_on_date_and_click_url_id_and_spend_type", unique: true
    t.index ["date", "click_url_id", "spend_type"], name: "index_campaign_spends_on_date_click_url_id_spend_type", unique: true
    t.index ["partner_id"], name: "index_campaign_spends_on_partner_id"
  end

  create_table "campaigns", force: :cascade do |t|
    t.boolean "active", default: false, null: false
    t.bigint "client_id", null: false
    t.string "country_code"
    t.datetime "created_at", null: false
    t.bigint "legacy_id"
    t.bigint "mobile_app_id"
    t.string "name"
    t.string "os"
    t.string "status"
    t.datetime "updated_at", null: false
    t.index ["client_id"], name: "index_campaigns_on_client_id"
    t.index ["legacy_id"], name: "index_campaigns_on_legacy_id", unique: true
    t.index ["status"], name: "index_campaigns_on_status"
  end

  create_table "click_urls", force: :cascade do |t|
    t.boolean "active", default: false, null: false
    t.bigint "campaign_id", null: false
    t.datetime "created_at", null: false
    t.bigint "legacy_id"
    t.string "link_type"
    t.bigint "partner_id", null: false
    t.string "status", null: false
    t.bigint "track_party_id", null: false
    t.datetime "updated_at", null: false
    t.index ["campaign_id"], name: "index_click_urls_on_campaign_id"
    t.index ["legacy_id"], name: "index_click_urls_on_legacy_id", unique: true
    t.index ["partner_id"], name: "index_click_urls_on_partner_id"
    t.index ["track_party_id"], name: "index_click_urls_on_track_party_id"
  end

  create_table "client_budgets", force: :cascade do |t|
    t.decimal "amount"
    t.bigint "client_id", null: false
    t.datetime "created_at", null: false
    t.date "end_date"
    t.date "start_date"
    t.datetime "updated_at", null: false
    t.index ["client_id"], name: "index_client_budgets_on_client_id"
  end

  create_table "client_creatives", force: :cascade do |t|
    t.bigint "client_id", null: false
    t.datetime "created_at", null: false
    t.string "name"
    t.datetime "updated_at", null: false
    t.string "url"
    t.index ["client_id"], name: "index_client_creatives_on_client_id"
  end

  create_table "client_deal_scores", force: :cascade do |t|
    t.integer "attribution_simplicity_score"
    t.integer "brand_value_score"
    t.integer "budget_potential_score"
    t.bigint "client_id", null: false
    t.datetime "created_at", null: false
    t.integer "margin_potential_score"
    t.integer "mission_alignment_score"
    t.integer "partner_openness_score"
    t.integer "tracking_ease_score"
    t.integer "uniqueness_score"
    t.datetime "updated_at", null: false
    t.index ["client_id"], name: "index_client_deal_scores_on_client_id"
  end

  create_table "client_details", force: :cascade do |t|
    t.bigint "account_executive_user_id"
    t.bigint "account_manager_user_id"
    t.text "additional_notes"
    t.text "biggest_obstacles"
    t.bigint "client_id", null: false
    t.text "client_persona"
    t.string "contact_name"
    t.datetime "created_at", null: false
    t.text "noteworthy_restrictions"
    t.string "preferred_communication_channel"
    t.jsonb "supported_geos", default: []
    t.text "target_audience"
    t.datetime "updated_at", null: false
    t.text "user_behavior_notes"
    t.text "user_flow_description"
    t.index ["account_executive_user_id"], name: "index_client_details_on_account_executive_user_id"
    t.index ["account_manager_user_id"], name: "index_client_details_on_account_manager_user_id"
    t.index ["client_id"], name: "index_client_details_on_client_id"
  end

  create_table "client_direct_partners", force: :cascade do |t|
    t.bigint "client_id", null: false
    t.datetime "created_at", null: false
    t.bigint "partner_id", null: false
    t.string "relationship_type"
    t.datetime "updated_at", null: false
    t.index ["client_id"], name: "index_client_direct_partners_on_client_id"
    t.index ["partner_id"], name: "index_client_direct_partners_on_partner_id"
  end

  create_table "client_documents", force: :cascade do |t|
    t.boolean "assistant_read", default: false, null: false
    t.string "assistant_read_sheet_names"
    t.string "category"
    t.bigint "client_id", null: false
    t.datetime "created_at", null: false
    t.string "title"
    t.datetime "updated_at", null: false
    t.string "url"
    t.index ["client_id"], name: "index_client_documents_on_client_id"
  end

  create_table "client_events", force: :cascade do |t|
    t.string "app_sdk_event"
    t.bigint "client_id", null: false
    t.datetime "created_at", null: false
    t.decimal "cvr_goal_from_install"
    t.string "description"
    t.string "feedmob_event"
    t.decimal "goal_cpa"
    t.boolean "is_primary_kpi"
    t.datetime "updated_at", null: false
    t.index ["client_id"], name: "index_client_events_on_client_id"
  end

  create_table "client_fraud_platforms", force: :cascade do |t|
    t.bigint "client_id", null: false
    t.datetime "created_at", null: false
    t.bigint "fraud_platform_id", null: false
    t.datetime "updated_at", null: false
    t.index ["client_id"], name: "index_client_fraud_platforms_on_client_id"
  end

  create_table "client_interests", force: :cascade do |t|
    t.bigint "client_id", null: false
    t.datetime "created_at", null: false
    t.string "interest_category"
    t.string "interest_name"
    t.integer "relevance_score"
    t.datetime "updated_at", null: false
    t.index ["client_id"], name: "index_client_interests_on_client_id"
  end

  create_table "client_pricings", force: :cascade do |t|
    t.integer "click_url_id"
    t.integer "client_id"
    t.datetime "created_at", null: false
    t.date "effective_from"
    t.date "effective_to"
    t.string "paid_event"
    t.integer "partner_id"
    t.string "pricing_model"
    t.decimal "rate", precision: 10, scale: 2
    t.datetime "updated_at", null: false
    t.index ["click_url_id", "effective_from", "effective_to"], name: "idx_on_click_url_id_effective_from_effective_to_69addb6dbb"
    t.index ["pricing_model"], name: "index_client_pricings_on_pricing_model"
  end

  create_table "client_report_files", force: :cascade do |t|
    t.bigint "client_report_id", null: false
    t.datetime "created_at", null: false
    t.string "status", default: "pending", null: false
    t.text "sync_log"
    t.datetime "updated_at", null: false
    t.index ["client_report_id"], name: "index_client_report_files_on_client_report_id"
  end

  create_table "client_reports", force: :cascade do |t|
    t.boolean "active", default: true
    t.bigint "client_id", null: false
    t.datetime "created_at", null: false
    t.string "date_keys"
    t.string "report_name", null: false
    t.string "report_type", default: "daily"
    t.boolean "rolling_insert", default: false
    t.string "unique_keys"
    t.datetime "updated_at", null: false
    t.index ["client_id", "report_name"], name: "index_client_reports_on_client_id_and_report_name", unique: true
    t.index ["client_id"], name: "index_client_reports_on_client_id"
    t.index ["unique_keys"], name: "index_client_reports_on_unique_keys"
  end

  create_table "client_social_media_links", force: :cascade do |t|
    t.boolean "active"
    t.bigint "client_id", null: false
    t.datetime "created_at", null: false
    t.string "platform"
    t.datetime "updated_at", null: false
    t.string "url"
    t.index ["client_id"], name: "index_client_social_media_links_on_client_id"
  end

  create_table "client_track_parties", force: :cascade do |t|
    t.string "access_url"
    t.string "account_username"
    t.bigint "client_id", null: false
    t.datetime "created_at", null: false
    t.bigint "track_party_id", null: false
    t.datetime "updated_at", null: false
    t.index ["client_id"], name: "index_client_track_parties_on_client_id"
    t.index ["track_party_id"], name: "index_client_track_parties_on_track_party_id"
  end

  create_table "clients", force: :cascade do |t|
    t.boolean "active", default: false
    t.datetime "created_at", null: false
    t.string "email_suffix"
    t.string "headline"
    t.boolean "is_test", default: false
    t.bigint "legacy_id"
    t.string "name", null: false
    t.integer "preferred_logo_background", default: 0, null: false
    t.datetime "updated_at", null: false
    t.string "website"
    t.index ["legacy_id"], name: "index_clients_on_legacy_id", unique: true
  end

  create_table "conversation_contexts", force: :cascade do |t|
    t.text "content"
    t.bigint "contextual_id", null: false
    t.string "contextual_type", null: false
    t.bigint "conversation_id", null: false
    t.datetime "created_at", null: false
    t.jsonb "metadata"
    t.datetime "updated_at", null: false
    t.index ["contextual_type", "contextual_id"], name: "index_conversation_contexts_on_contextual"
    t.index ["conversation_id"], name: "index_conversation_contexts_on_conversation_id"
  end

  create_table "conversations", force: :cascade do |t|
    t.string "assistant"
    t.jsonb "context_settings", default: {}, null: false
    t.datetime "created_at", null: false
    t.jsonb "metadata", default: {}
    t.string "model"
    t.bigint "parent_conversation_id"
    t.string "profile", default: "default"
    t.string "source", default: "web", null: false
    t.jsonb "source_meta", default: {}, null: false
    t.string "status", default: "active"
    t.boolean "streaming", default: true, null: false
    t.datetime "updated_at", null: false
    t.bigint "user_id", null: false
    t.index ["parent_conversation_id"], name: "index_conversations_on_parent_conversation_id"
    t.index ["source"], name: "index_conversations_on_source"
    t.index ["source_meta"], name: "index_conversations_on_source_meta", using: :gin
    t.index ["user_id"], name: "index_conversations_on_user_id"
  end

  create_table "conversion_funnels", force: :cascade do |t|
    t.bigint "client_id", null: false
    t.decimal "conversion_rate"
    t.datetime "created_at", null: false
    t.string "from_event"
    t.text "notes"
    t.string "to_event"
    t.datetime "updated_at", null: false
    t.index ["client_id"], name: "index_conversion_funnels_on_client_id"
  end

  create_table "daily_campaign_spends", force: :cascade do |t|
    t.decimal "adjusted_margin"
    t.decimal "adjusted_net_spend"
    t.integer "click_count"
    t.datetime "created_at", null: false
    t.decimal "cvr"
    t.jsonb "event_data", default: {}
    t.integer "gross_campaign_spend_id"
    t.decimal "gross_spend"
    t.string "gross_spend_source"
    t.integer "impression_count"
    t.integer "install_count"
    t.boolean "is_test"
    t.integer "legacy_campaign_id"
    t.integer "legacy_click_url_id"
    t.integer "legacy_client_id"
    t.integer "legacy_partner_id"
    t.decimal "margin"
    t.integer "net_campaign_spend_id"
    t.decimal "net_spend"
    t.decimal "net_spend_adjustment"
    t.jsonb "net_spend_adjustments"
    t.string "net_spend_source"
    t.decimal "revenue"
    t.date "spend_date"
    t.datetime "updated_at", null: false
    t.index ["event_data"], name: "index_daily_campaign_spends_on_event_data", using: :gin
    t.index ["legacy_campaign_id"], name: "index_daily_campaign_spends_on_legacy_campaign_id"
    t.index ["legacy_click_url_id"], name: "index_daily_campaign_spends_on_legacy_click_url_id"
    t.index ["legacy_client_id"], name: "index_daily_campaign_spends_on_legacy_client_id"
    t.index ["legacy_partner_id"], name: "index_daily_campaign_spends_on_legacy_partner_id"
    t.index ["spend_date", "legacy_click_url_id"], name: "index_daily_campaign_spends_on_spend_date_and_click_url_id", unique: true
    t.index ["spend_date"], name: "index_daily_campaign_spends_on_spend_date"
  end

  create_table "demographic_segments", force: :cascade do |t|
    t.bigint "client_id", null: false
    t.datetime "created_at", null: false
    t.boolean "is_primary", default: false
    t.integer "priority_order"
    t.string "segment_type"
    t.string "segment_value"
    t.datetime "updated_at", null: false
    t.index ["client_id"], name: "index_demographic_segments_on_client_id"
  end

  create_table "documents", force: :cascade do |t|
    t.text "content"
    t.datetime "created_at", null: false
    t.date "date"
    t.string "doc_type"
    t.jsonb "metadata"
    t.string "source_url"
    t.string "status", default: "unprocessed"
    t.text "summary"
    t.string "title"
    t.datetime "updated_at", null: false
    t.index ["status"], name: "index_documents_on_status"
  end

  create_table "email_messages", force: :cascade do |t|
    t.text "body", null: false
    t.datetime "created_at", null: false
    t.bigint "email_thread_id", null: false
    t.string "from_address"
    t.json "metadata", default: {}
    t.datetime "sent_at"
    t.json "to_addresses", default: []
    t.datetime "updated_at", null: false
    t.index ["email_thread_id"], name: "index_email_messages_on_email_thread_id"
    t.index ["from_address"], name: "index_email_messages_on_from_address"
    t.index ["sent_at"], name: "index_email_messages_on_sent_at"
  end

  create_table "email_threads", force: :cascade do |t|
    t.datetime "created_at", null: false
    t.string "subject"
    t.datetime "updated_at", null: false
  end

  create_table "entries", force: :cascade do |t|
    t.text "content", null: false
    t.datetime "created_at", null: false
    t.bigint "document_id"
    t.vector "embedding", limit: 1024
    t.jsonb "metadata", default: {}
    t.string "type", null: false
    t.datetime "updated_at", null: false
    t.index ["document_id"], name: "index_entries_on_document_id"
    t.index ["type"], name: "index_entries_on_type"
  end

  create_table "event_aggregates", force: :cascade do |t|
    t.bigint "click_url_id", null: false
    t.bigint "count"
    t.datetime "created_at", null: false
    t.string "data_origin"
    t.date "date"
    t.string "event"
    t.datetime "updated_at", null: false
    t.index ["click_url_id", "date", "event", "data_origin"], name: "idx_event_aggregates_lookup", unique: true
  end

  create_table "event_mappings", force: :cascade do |t|
    t.string "client_event_name", null: false
    t.bigint "client_id", null: false
    t.datetime "created_at", null: false
    t.string "event"
    t.datetime "updated_at", null: false
    t.index ["client_id", "client_event_name"], name: "index_event_mappings_on_client_id_and_client_event_name", unique: true
    t.index ["client_id"], name: "index_event_mappings_on_client_id"
  end

  create_table "fireflies_meetings", force: :cascade do |t|
    t.datetime "association_checked_at"
    t.bigint "client_id"
    t.datetime "created_at", null: false
    t.integer "duration"
    t.string "fireflies_id", null: false
    t.datetime "meeting_date"
    t.string "meeting_url"
    t.json "participants", default: []
    t.bigint "partner_id"
    t.json "summary_keywords"
    t.text "summary_overview"
    t.text "summary_short"
    t.datetime "synced_at"
    t.string "title"
    t.datetime "updated_at", null: false
    t.index ["client_id"], name: "index_fireflies_meetings_on_client_id"
    t.index ["fireflies_id"], name: "index_fireflies_meetings_on_fireflies_id", unique: true
    t.index ["meeting_date"], name: "index_fireflies_meetings_on_meeting_date"
    t.index ["partner_id"], name: "index_fireflies_meetings_on_partner_id"
    t.index ["synced_at"], name: "index_fireflies_meetings_on_synced_at"
  end

  create_table "fireflies_utterances", force: :cascade do |t|
    t.text "content", null: false
    t.datetime "created_at", null: false
    t.datetime "end_time"
    t.bigint "fireflies_meeting_id", null: false
    t.integer "sentence_index", null: false
    t.string "speaker_id"
    t.string "speaker_name"
    t.datetime "start_time"
    t.datetime "updated_at", null: false
    t.index ["fireflies_meeting_id", "sentence_index"], name: "index_fireflies_utterances_on_meeting_and_index"
    t.index ["fireflies_meeting_id"], name: "index_fireflies_utterances_on_fireflies_meeting_id"
    t.index ["speaker_name"], name: "index_fireflies_utterances_on_speaker_name"
    t.index ["start_time"], name: "index_fireflies_utterances_on_start_time"
  end

  create_table "flipper_features", force: :cascade do |t|
    t.datetime "created_at", null: false
    t.string "key", null: false
    t.datetime "updated_at", null: false
    t.index ["key"], name: "index_flipper_features_on_key", unique: true
  end

  create_table "flipper_gates", force: :cascade do |t|
    t.datetime "created_at", null: false
    t.string "feature_key", null: false
    t.string "key", null: false
    t.datetime "updated_at", null: false
    t.text "value"
    t.index ["feature_key", "key", "value"], name: "index_flipper_gates_on_feature_key_and_key_and_value", unique: true
  end

  create_table "fraud_platforms", force: :cascade do |t|
    t.datetime "created_at", null: false
    t.text "description"
    t.string "name"
    t.datetime "updated_at", null: false
  end

  create_table "github_credentials", force: :cascade do |t|
    t.string "access_token"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "user_id", null: false
    t.index ["user_id"], name: "index_github_credentials_on_user_id"
  end

  create_table "gmail_credentials", force: :cascade do |t|
    t.text "access_token"
    t.datetime "created_at", null: false
    t.datetime "expires_at"
    t.text "refresh_token"
    t.datetime "updated_at", null: false
    t.bigint "user_id", null: false
    t.index ["user_id"], name: "index_gmail_credentials_on_user_id"
  end

  create_table "gong_calls", force: :cascade do |t|
    t.datetime "association_checked_at"
    t.bigint "client_id"
    t.datetime "created_at", null: false
    t.string "direction"
    t.integer "duration"
    t.string "gong_id", null: false
    t.string "gong_url"
    t.json "participants"
    t.bigint "partner_id"
    t.datetime "started_at"
    t.string "title"
    t.datetime "updated_at", null: false
    t.string "workspace_id"
    t.index ["client_id"], name: "index_gong_calls_on_client_id"
    t.index ["direction"], name: "index_gong_calls_on_direction"
    t.index ["gong_id"], name: "index_gong_calls_on_gong_id", unique: true
    t.index ["partner_id"], name: "index_gong_calls_on_partner_id"
    t.index ["started_at"], name: "index_gong_calls_on_started_at"
    t.index ["workspace_id"], name: "index_gong_calls_on_workspace_id"
  end

  create_table "gong_transcripts", force: :cascade do |t|
    t.datetime "created_at", null: false
    t.integer "duration_ms"
    t.datetime "end_time", null: false
    t.bigint "gong_call_id", null: false
    t.json "metadata"
    t.integer "sequence_order"
    t.string "speaker_affiliation"
    t.string "speaker_email"
    t.string "speaker_id", null: false
    t.string "speaker_name"
    t.string "speaker_title"
    t.datetime "start_time", null: false
    t.text "text", null: false
    t.string "topic"
    t.datetime "updated_at", null: false
    t.integer "word_count"
    t.index ["gong_call_id", "speaker_id"], name: "index_gong_transcripts_on_gong_call_id_and_speaker_id"
    t.index ["gong_call_id", "start_time"], name: "index_gong_transcripts_on_gong_call_id_and_start_time"
    t.index ["gong_call_id"], name: "index_gong_transcripts_on_gong_call_id"
    t.index ["sequence_order"], name: "index_gong_transcripts_on_sequence_order"
    t.index ["speaker_id"], name: "index_gong_transcripts_on_speaker_id"
    t.index ["start_time", "end_time"], name: "index_gong_transcripts_on_start_time_and_end_time"
  end

  create_table "hubspot_notes", force: :cascade do |t|
    t.boolean "archived"
    t.string "attachment_ids"
    t.text "body"
    t.datetime "created_at", null: false
    t.datetime "hs_created_at"
    t.string "hs_object_id", null: false
    t.string "hs_owner_id", null: false
    t.string "hs_ticket_id", null: false
    t.datetime "hs_updated_at"
    t.integer "owner_id"
    t.integer "ticket_id", null: false
    t.datetime "updated_at", null: false
  end

  create_table "hubspot_owners", force: :cascade do |t|
    t.datetime "created_at", null: false
    t.string "email", null: false
    t.string "first_name"
    t.string "hs_object_id", null: false
    t.string "last_name"
    t.datetime "updated_at", null: false
  end

  create_table "hubspot_tickets", force: :cascade do |t|
    t.boolean "archived"
    t.text "content", null: false
    t.datetime "created_at", null: false
    t.datetime "hs_created_at"
    t.string "hs_object_id", null: false
    t.string "hs_owner_id"
    t.datetime "hs_updated_at"
    t.integer "owner_id"
    t.string "pipeline"
    t.string "pipeline_stage"
    t.text "subject", null: false
    t.string "ticket_category"
    t.string "ticket_priority"
    t.string "ticket_priority_advanced"
    t.datetime "updated_at", null: false
  end

  create_table "logs", force: :cascade do |t|
    t.datetime "created_at", null: false
    t.json "data", default: {}
    t.string "level", default: "info", null: false
    t.bigint "loggable_id", null: false
    t.string "loggable_type", null: false
    t.string "message"
    t.datetime "updated_at", null: false
    t.index ["loggable_type", "loggable_id"], name: "index_logs_on_loggable"
  end

  create_table "machine_learning_models", force: :cascade do |t|
    t.datetime "created_at", null: false
    t.jsonb "encoding_categories", default: {}
    t.boolean "is_current", default: false
    t.json "metadata", default: {}
    t.string "model_type", null: false
    t.string "name", null: false
    t.integer "num_samples", default: 0
    t.string "target_metric", null: false
    t.datetime "updated_at", null: false
  end

  create_table "messages", force: :cascade do |t|
    t.string "assistant"
    t.text "content"
    t.bigint "conversation_id", null: false
    t.datetime "created_at", null: false
    t.string "error_message"
    t.json "metadata", default: {}
    t.text "reasoning_content"
    t.string "reasoning_signature"
    t.json "references", default: []
    t.text "role"
    t.string "status"
    t.json "tool_results", default: []
    t.json "tool_uses", default: []
    t.datetime "updated_at", null: false
    t.index ["conversation_id"], name: "index_messages_on_conversation_id"
  end

  create_table "mobile_app_categories", force: :cascade do |t|
    t.datetime "created_at", null: false
    t.string "name"
    t.datetime "updated_at", null: false
  end

  create_table "mobile_app_categorizations", force: :cascade do |t|
    t.datetime "created_at", null: false
    t.bigint "mobile_app_category_id", null: false
    t.bigint "mobile_app_id", null: false
    t.datetime "updated_at", null: false
    t.index ["mobile_app_category_id"], name: "index_mobile_app_categorizations_on_mobile_app_category_id"
    t.index ["mobile_app_id"], name: "index_mobile_app_categorizations_on_mobile_app_id"
  end

  create_table "mobile_app_metrics", force: :cascade do |t|
    t.datetime "created_at", null: false
    t.string "location"
    t.date "metric_date", null: false
    t.bigint "mobile_app_id", null: false
    t.decimal "rating", precision: 3, scale: 2
    t.bigint "rating_count"
    t.datetime "updated_at", null: false
    t.index ["mobile_app_id", "metric_date"], name: "index_mobile_app_metrics_on_mobile_app_id_and_metric_date", unique: true
    t.index ["mobile_app_id"], name: "index_mobile_app_metrics_on_mobile_app_id"
  end

  create_table "mobile_apps", force: :cascade do |t|
    t.boolean "active", default: false, null: false
    t.string "bundle_id", null: false
    t.bigint "client_id", null: false
    t.datetime "created_at", null: false
    t.text "description"
    t.bigint "legacy_id"
    t.bigint "main_category_id"
    t.string "name", null: false
    t.string "platform", null: false
    t.string "store_url"
    t.datetime "updated_at", null: false
    t.string "website"
    t.index ["bundle_id"], name: "index_mobile_apps_on_bundle_id"
    t.index ["client_id"], name: "index_mobile_apps_on_client_id"
    t.index ["legacy_id"], name: "index_mobile_apps_on_legacy_id", unique: true
    t.index ["main_category_id"], name: "index_mobile_apps_on_main_category_id"
  end

  create_table "monthly_campaign_spend_adjustments", force: :cascade do |t|
    t.bigint "campaign_id", null: false
    t.bigint "client_id", null: false
    t.datetime "created_at", null: false
    t.decimal "gross_amount", precision: 10, scale: 2, null: false
    t.integer "legacy_campaign_id"
    t.integer "legacy_client_id"
    t.integer "legacy_id"
    t.integer "legacy_partner_id"
    t.json "metadata"
    t.string "month", null: false
    t.decimal "net_amount", precision: 10, scale: 2, null: false
    t.bigint "partner_id", null: false
    t.string "reason", null: false
    t.datetime "updated_at", null: false
    t.index ["campaign_id"], name: "index_monthly_campaign_spend_adjustments_on_campaign_id"
    t.index ["client_id"], name: "index_monthly_campaign_spend_adjustments_on_client_id"
    t.index ["legacy_campaign_id"], name: "index_monthly_campaign_spend_adjustments_on_legacy_campaign_id"
    t.index ["legacy_client_id"], name: "index_monthly_campaign_spend_adjustments_on_legacy_client_id"
    t.index ["legacy_id"], name: "index_monthly_campaign_spend_adjustments_on_legacy_id", unique: true
    t.index ["legacy_partner_id"], name: "index_monthly_campaign_spend_adjustments_on_legacy_partner_id"
    t.index ["partner_id"], name: "index_monthly_campaign_spend_adjustments_on_partner_id"
  end

  create_table "monthly_campaign_spends", force: :cascade do |t|
    t.decimal "adjusted_gross_spend"
    t.decimal "adjusted_margin"
    t.decimal "adjusted_net_spend"
    t.decimal "adjusted_revenue"
    t.string "adjustment_reason"
    t.integer "click_count"
    t.datetime "created_at", null: false
    t.decimal "cvr"
    t.decimal "gross_spend"
    t.integer "impression_count"
    t.integer "install_count"
    t.boolean "is_test"
    t.integer "legacy_campaign_id"
    t.integer "legacy_client_id"
    t.integer "legacy_partner_id"
    t.date "month_date"
    t.integer "monthly_campaign_spend_adjustment_id"
    t.decimal "monthly_gross_adjustment"
    t.decimal "monthly_net_adjustment"
    t.decimal "net_spend"
    t.decimal "net_spend_adjustment"
    t.datetime "updated_at", null: false
    t.index ["legacy_campaign_id"], name: "index_monthly_campaign_spends_on_legacy_campaign_id"
    t.index ["legacy_client_id"], name: "index_monthly_campaign_spends_on_legacy_client_id"
    t.index ["legacy_partner_id"], name: "index_monthly_campaign_spends_on_legacy_partner_id"
    t.index ["month_date", "legacy_campaign_id", "legacy_client_id", "legacy_partner_id"], name: "index_monthly_campaign_spends_on_month_date_and_campaign_id", unique: true
    t.index ["month_date"], name: "index_monthly_campaign_spends_on_month_date"
  end

  create_table "partner_campaign_mappings", force: :cascade do |t|
    t.bigint "click_url_id", null: false
    t.datetime "created_at", null: false
    t.integer "legacy_id"
    t.string "legacy_table"
    t.string "mapping_campaign_id"
    t.string "mapping_campaign_name"
    t.datetime "updated_at", null: false
    t.index ["click_url_id"], name: "index_partner_campaign_mappings_on_click_url_id"
  end

  create_table "partner_categories", force: :cascade do |t|
    t.datetime "created_at", null: false
    t.string "name"
    t.datetime "updated_at", null: false
  end

  create_table "partner_categorizations", force: :cascade do |t|
    t.datetime "created_at", null: false
    t.bigint "partner_category_id", null: false
    t.bigint "partner_id", null: false
    t.datetime "updated_at", null: false
    t.index ["partner_category_id"], name: "index_partner_categorizations_on_partner_category_id"
    t.index ["partner_id"], name: "index_partner_categorizations_on_partner_id"
  end

  create_table "partner_details", force: :cascade do |t|
    t.string "add_on_service"
    t.boolean "appsflyer_agency_approval"
    t.text "benchmark"
    t.string "billing_type"
    t.string "company_size"
    t.datetime "created_at", null: false
    t.string "creative_guideline"
    t.boolean "dashboard_availability"
    t.text "incentive_program"
    t.boolean "incrementality_test_capacity"
    t.boolean "link_test_ability"
    t.string "macro_mapping"
    t.text "media_description"
    t.string "minimum_budget"
    t.boolean "multiple_reward_capacity"
    t.string "net_spend_sources"
    t.string "os_and_scale"
    t.text "other_notes"
    t.string "ownership_for_traffic"
    t.bigint "partner_id", null: false
    t.string "payment_term"
    t.string "poc_location"
    t.boolean "reporting_practice"
    t.string "reporting_timezone"
    t.string "requested_link_type"
    t.boolean "skan_compatible"
    t.text "targeting_capacity"
    t.string "top_performing_geos"
    t.text "unique_selling_point"
    t.datetime "updated_at", null: false
    t.text "user_flow_description"
    t.index ["partner_id"], name: "index_partner_details_on_partner_id"
  end

  create_table "partner_documents", force: :cascade do |t|
    t.string "category"
    t.datetime "created_at", null: false
    t.bigint "partner_id", null: false
    t.string "title"
    t.datetime "updated_at", null: false
    t.index ["partner_id"], name: "index_partner_documents_on_partner_id"
  end

  create_table "partner_integrations", force: :cascade do |t|
    t.bigint "client_id", null: false
    t.datetime "created_at", null: false
    t.text "credentials", null: false
    t.bigint "partner_id", null: false
    t.datetime "updated_at", null: false
    t.index ["client_id"], name: "index_partner_integrations_on_client_id"
    t.index ["partner_id"], name: "index_partner_integrations_on_partner_id"
  end

  create_table "partner_media", force: :cascade do |t|
    t.datetime "created_at", null: false
    t.text "description"
    t.string "media_type"
    t.string "name"
    t.bigint "partner_id", null: false
    t.datetime "updated_at", null: false
    t.string "url"
    t.index ["partner_id"], name: "index_partner_media_on_partner_id"
  end

  create_table "partner_pricings", force: :cascade do |t|
    t.integer "click_url_id"
    t.integer "client_id"
    t.datetime "created_at", null: false
    t.date "effective_from"
    t.date "effective_to"
    t.string "paid_event"
    t.bigint "partner_id", null: false
    t.string "pricing_model"
    t.decimal "rate", precision: 10, scale: 2
    t.datetime "updated_at", null: false
    t.index ["click_url_id", "effective_from", "effective_to"], name: "idx_on_click_url_id_effective_from_effective_to_970d1afaf9"
    t.index ["partner_id"], name: "index_partner_pricings_on_partner_id"
    t.index ["pricing_model"], name: "index_partner_pricings_on_pricing_model"
  end

  create_table "partner_quotes", force: :cascade do |t|
    t.string "company_name"
    t.datetime "created_at", null: false
    t.string "full_name"
    t.string "job_title"
    t.bigint "partner_id", null: false
    t.text "quote_text"
    t.datetime "updated_at", null: false
    t.index ["partner_id"], name: "index_partner_quotes_on_partner_id"
  end

  create_table "partner_resources", force: :cascade do |t|
    t.datetime "created_at", null: false
    t.string "name"
    t.bigint "partner_id", null: false
    t.datetime "updated_at", null: false
    t.string "url"
    t.index ["partner_id"], name: "index_partner_resources_on_partner_id"
  end

  create_table "partner_social_media_links", force: :cascade do |t|
    t.boolean "active"
    t.datetime "created_at", null: false
    t.bigint "partner_id", null: false
    t.string "platform"
    t.datetime "updated_at", null: false
    t.string "url"
    t.index ["partner_id"], name: "index_partner_social_media_links_on_partner_id"
  end

  create_table "partner_spends", force: :cascade do |t|
    t.bigint "click_url_id", null: false
    t.datetime "created_at", null: false
    t.date "date", null: false
    t.bigint "partner_id", null: false
    t.string "source"
    t.decimal "spend", precision: 10, scale: 2
    t.datetime "updated_at", null: false
    t.index ["click_url_id"], name: "index_partner_spends_on_click_url_id"
    t.index ["partner_id"], name: "index_partner_spends_on_partner_id"
    t.index ["source", "click_url_id", "date"], name: "index_partner_spends_on_source_and_click_url_id_and_date", unique: true
  end

  create_table "partner_use_cases", force: :cascade do |t|
    t.datetime "created_at", null: false
    t.text "description"
    t.bigint "partner_id", null: false
    t.string "title"
    t.datetime "updated_at", null: false
    t.index ["partner_id"], name: "index_partner_use_cases_on_partner_id"
  end

  create_table "partners", force: :cascade do |t|
    t.string "adjust_id"
    t.datetime "created_at", null: false
    t.text "description"
    t.string "email"
    t.string "email_suffix"
    t.text "headline"
    t.boolean "is_test", default: false
    t.bigint "legacy_id"
    t.bigint "main_category_id"
    t.string "name"
    t.integer "preferred_logo_background", default: 0
    t.string "slug"
    t.string "status"
    t.datetime "updated_at", null: false
    t.string "website"
    t.index ["adjust_id"], name: "index_partners_on_adjust_id", unique: true
    t.index ["legacy_id"], name: "index_partners_on_legacy_id", unique: true
    t.index ["main_category_id"], name: "index_partners_on_main_category_id"
    t.index ["slug"], name: "index_partners_on_slug", unique: true
  end

  create_table "proxy_request_logs", force: :cascade do |t|
    t.bigint "api_playground_id", null: false
    t.datetime "created_at", null: false
    t.string "error_message"
    t.jsonb "request_body"
    t.jsonb "request_headers"
    t.string "request_method"
    t.string "request_url"
    t.datetime "requested_at"
    t.text "response_body"
    t.jsonb "response_headers"
    t.integer "response_status"
    t.boolean "succeeded"
    t.datetime "updated_at", null: false
    t.index ["api_playground_id"], name: "index_proxy_request_logs_on_api_playground_id"
  end

  create_table "sessions", force: :cascade do |t|
    t.datetime "created_at", null: false
    t.string "ip_address"
    t.datetime "updated_at", null: false
    t.string "user_agent"
    t.bigint "user_id", null: false
    t.index ["user_id"], name: "index_sessions_on_user_id"
  end

  create_table "slack_messages", force: :cascade do |t|
    t.string "channel_id", null: false
    t.string "channel_name"
    t.integer "client_id"
    t.datetime "created_at", null: false
    t.text "message_text"
    t.datetime "message_time"
    t.string "message_ts", null: false
    t.string "message_user_id"
    t.string "message_user_name"
    t.integer "partner_id"
    t.string "permalink"
    t.datetime "updated_at", null: false
    t.bigint "user_id", null: false
    t.index ["user_id"], name: "index_slack_messages_on_user_id"
  end

  create_table "solid_cable_messages", force: :cascade do |t|
    t.binary "channel", null: false
    t.bigint "channel_hash", null: false
    t.datetime "created_at", null: false
    t.binary "payload", null: false
    t.index ["channel"], name: "index_solid_cable_messages_on_channel"
    t.index ["channel_hash"], name: "index_solid_cable_messages_on_channel_hash"
    t.index ["created_at"], name: "index_solid_cable_messages_on_created_at"
  end

  create_table "solid_queue_blocked_executions", force: :cascade do |t|
    t.string "concurrency_key", null: false
    t.datetime "created_at", null: false
    t.datetime "expires_at", null: false
    t.bigint "job_id", null: false
    t.integer "priority", default: 0, null: false
    t.string "queue_name", null: false
    t.index ["concurrency_key", "priority", "job_id"], name: "index_solid_queue_blocked_executions_for_release"
    t.index ["expires_at", "concurrency_key"], name: "index_solid_queue_blocked_executions_for_maintenance"
    t.index ["job_id"], name: "index_solid_queue_blocked_executions_on_job_id", unique: true
  end

  create_table "solid_queue_claimed_executions", force: :cascade do |t|
    t.datetime "created_at", null: false
    t.bigint "job_id", null: false
    t.bigint "process_id"
    t.index ["job_id"], name: "index_solid_queue_claimed_executions_on_job_id", unique: true
    t.index ["process_id", "job_id"], name: "index_solid_queue_claimed_executions_on_process_id_and_job_id"
  end

  create_table "solid_queue_failed_executions", force: :cascade do |t|
    t.datetime "created_at", null: false
    t.text "error"
    t.bigint "job_id", null: false
    t.index ["job_id"], name: "index_solid_queue_failed_executions_on_job_id", unique: true
  end

  create_table "solid_queue_jobs", force: :cascade do |t|
    t.string "active_job_id"
    t.text "arguments"
    t.string "class_name", null: false
    t.string "concurrency_key"
    t.datetime "created_at", null: false
    t.datetime "finished_at"
    t.integer "priority", default: 0, null: false
    t.string "queue_name", null: false
    t.datetime "scheduled_at"
    t.datetime "updated_at", null: false
    t.index ["active_job_id"], name: "index_solid_queue_jobs_on_active_job_id"
    t.index ["class_name"], name: "index_solid_queue_jobs_on_class_name"
    t.index ["finished_at"], name: "index_solid_queue_jobs_on_finished_at"
    t.index ["queue_name", "finished_at"], name: "index_solid_queue_jobs_for_filtering"
    t.index ["scheduled_at", "finished_at"], name: "index_solid_queue_jobs_for_alerting"
  end

  create_table "solid_queue_pauses", force: :cascade do |t|
    t.datetime "created_at", null: false
    t.string "queue_name", null: false
    t.index ["queue_name"], name: "index_solid_queue_pauses_on_queue_name", unique: true
  end

  create_table "solid_queue_processes", force: :cascade do |t|
    t.datetime "created_at", null: false
    t.string "hostname"
    t.string "kind", null: false
    t.datetime "last_heartbeat_at", null: false
    t.text "metadata"
    t.string "name", null: false
    t.integer "pid", null: false
    t.bigint "supervisor_id"
    t.index ["last_heartbeat_at"], name: "index_solid_queue_processes_on_last_heartbeat_at"
    t.index ["name", "supervisor_id"], name: "index_solid_queue_processes_on_name_and_supervisor_id", unique: true
    t.index ["supervisor_id"], name: "index_solid_queue_processes_on_supervisor_id"
  end

  create_table "solid_queue_ready_executions", force: :cascade do |t|
    t.datetime "created_at", null: false
    t.bigint "job_id", null: false
    t.integer "priority", default: 0, null: false
    t.string "queue_name", null: false
    t.index ["job_id"], name: "index_solid_queue_ready_executions_on_job_id", unique: true
    t.index ["priority", "job_id"], name: "index_solid_queue_poll_all"
    t.index ["queue_name", "priority", "job_id"], name: "index_solid_queue_poll_by_queue"
  end

  create_table "solid_queue_recurring_executions", force: :cascade do |t|
    t.datetime "created_at", null: false
    t.bigint "job_id", null: false
    t.datetime "run_at", null: false
    t.string "task_key", null: false
    t.index ["job_id"], name: "index_solid_queue_recurring_executions_on_job_id", unique: true
    t.index ["task_key", "run_at"], name: "index_solid_queue_recurring_executions_on_task_key_and_run_at", unique: true
  end

  create_table "solid_queue_recurring_tasks", force: :cascade do |t|
    t.text "arguments"
    t.string "class_name"
    t.string "command", limit: 2048
    t.datetime "created_at", null: false
    t.text "description"
    t.string "key", null: false
    t.integer "priority", default: 0
    t.string "queue_name"
    t.string "schedule", null: false
    t.boolean "static", default: true, null: false
    t.datetime "updated_at", null: false
    t.index ["key"], name: "index_solid_queue_recurring_tasks_on_key", unique: true
    t.index ["static"], name: "index_solid_queue_recurring_tasks_on_static"
  end

  create_table "solid_queue_scheduled_executions", force: :cascade do |t|
    t.datetime "created_at", null: false
    t.bigint "job_id", null: false
    t.integer "priority", default: 0, null: false
    t.string "queue_name", null: false
    t.datetime "scheduled_at", null: false
    t.index ["job_id"], name: "index_solid_queue_scheduled_executions_on_job_id", unique: true
    t.index ["scheduled_at", "priority", "job_id"], name: "index_solid_queue_dispatch_all"
  end

  create_table "solid_queue_semaphores", force: :cascade do |t|
    t.datetime "created_at", null: false
    t.datetime "expires_at", null: false
    t.string "key", null: false
    t.datetime "updated_at", null: false
    t.integer "value", default: 1, null: false
    t.index ["expires_at"], name: "index_solid_queue_semaphores_on_expires_at"
    t.index ["key", "value"], name: "index_solid_queue_semaphores_on_key_and_value"
    t.index ["key"], name: "index_solid_queue_semaphores_on_key", unique: true
  end

  create_table "sub_client_budget_campaigns", force: :cascade do |t|
    t.bigint "campaign_id", null: false
    t.datetime "created_at", null: false
    t.bigint "sub_client_budget_id", null: false
    t.datetime "updated_at", null: false
    t.index ["campaign_id"], name: "index_sub_client_budget_campaigns_on_campaign_id"
    t.index ["sub_client_budget_id"], name: "index_sub_client_budget_campaigns_on_sub_client_budget_id"
  end

  create_table "sub_client_budgets", force: :cascade do |t|
    t.decimal "amount", precision: 12, scale: 2
    t.bigint "client_budget_id", null: false
    t.datetime "created_at", null: false
    t.integer "legacy_id"
    t.string "name"
    t.datetime "updated_at", null: false
    t.index ["client_budget_id"], name: "index_sub_client_budgets_on_client_budget_id"
  end

  create_table "target_kpis", force: :cascade do |t|
    t.bigint "client_id", null: false
    t.datetime "created_at", null: false
    t.text "description"
    t.string "kpi_name"
    t.string "measurement_unit"
    t.decimal "target_value"
    t.datetime "updated_at", null: false
    t.string "value_type"
    t.index ["client_id"], name: "index_target_kpis_on_client_id"
  end

  create_table "task_definitions", force: :cascade do |t|
    t.datetime "created_at", null: false
    t.text "description"
    t.string "name", null: false
    t.datetime "updated_at", null: false
    t.index ["name"], name: "index_task_definitions_on_name", unique: true
  end

  create_table "task_runs", force: :cascade do |t|
    t.datetime "completed_at"
    t.datetime "created_at", null: false
    t.text "notes"
    t.json "parameters", default: {}
    t.datetime "started_at"
    t.string "status", default: "pending", null: false
    t.bigint "task_definition_id", null: false
    t.datetime "updated_at", null: false
    t.bigint "user_id"
    t.index ["task_definition_id"], name: "index_task_runs_on_task_definition_id"
  end

  create_table "token_usages", force: :cascade do |t|
    t.datetime "created_at", null: false
    t.integer "input_tokens", null: false
    t.string "model", null: false
    t.integer "output_tokens", null: false
    t.string "source", null: false
    t.datetime "updated_at", null: false
  end

  create_table "track_parties", force: :cascade do |t|
    t.datetime "created_at", null: false
    t.bigint "legacy_id"
    t.string "name"
    t.datetime "updated_at", null: false
    t.index ["legacy_id"], name: "index_track_parties_on_legacy_id", unique: true
  end

  create_table "track_party_campaign_mappings", force: :cascade do |t|
    t.bigint "click_url_id", null: false
    t.datetime "created_at", null: false
    t.integer "legacy_id"
    t.string "legacy_table"
    t.string "mapping_campaign_name"
    t.string "mapping_channel"
    t.datetime "updated_at", null: false
    t.index ["click_url_id"], name: "index_track_party_campaign_mappings_on_click_url_id"
  end

  create_table "track_party_integrations", force: :cascade do |t|
    t.bigint "client_id", null: false
    t.datetime "created_at", null: false
    t.text "credentials"
    t.date "expires_at"
    t.string "name"
    t.bigint "track_party_id", null: false
    t.datetime "updated_at", null: false
    t.index ["client_id"], name: "index_track_party_integrations_on_client_id"
    t.index ["track_party_id"], name: "index_track_party_integrations_on_track_party_id"
  end

  create_table "users", force: :cascade do |t|
    t.datetime "created_at", null: false
    t.string "default_llm_model", default: "claude_3_5_haiku"
    t.datetime "discarded_at"
    t.string "email_address", null: false
    t.string "google_id"
    t.integer "legacy_id"
    t.string "name"
    t.string "password_digest", null: false
    t.string "role", default: "member"
    t.string "slack_user_id"
    t.string "token"
    t.datetime "updated_at", null: false
    t.index ["discarded_at"], name: "index_users_on_discarded_at"
    t.index ["email_address"], name: "index_users_on_email_address", unique: true
    t.index ["google_id"], name: "index_users_on_google_id", unique: true
    t.index ["slack_user_id"], name: "index_users_on_slack_user_id", unique: true
    t.index ["token"], name: "index_users_on_token", unique: true
  end

  create_table "workspaces", force: :cascade do |t|
    t.datetime "created_at", null: false
    t.text "description"
    t.boolean "is_favorited", default: false, null: false
    t.json "layout"
    t.string "share_token"
    t.string "template", default: "blank", null: false
    t.string "title"
    t.datetime "updated_at", null: false
    t.bigint "user_id", null: false
    t.index ["user_id"], name: "index_workspaces_on_user_id"
  end

  add_foreign_key "active_storage_attachments", "active_storage_blobs", column: "blob_id"
  add_foreign_key "active_storage_variant_records", "active_storage_blobs", column: "blob_id"
  add_foreign_key "alert_dismissals", "alerts"
  add_foreign_key "alert_dismissals", "users"
  add_foreign_key "api_playgrounds", "clients"
  add_foreign_key "api_playgrounds", "partners"
  add_foreign_key "api_playgrounds", "track_parties"
  add_foreign_key "attribution_windows", "clients"
  add_foreign_key "campaign_spend_adjustments", "campaign_spends"
  add_foreign_key "campaign_spends", "click_urls"
  add_foreign_key "campaign_spends", "clients"
  add_foreign_key "campaign_spends", "partners"
  add_foreign_key "campaigns", "clients"
  add_foreign_key "click_urls", "campaigns"
  add_foreign_key "click_urls", "partners"
  add_foreign_key "click_urls", "track_parties"
  add_foreign_key "client_budgets", "clients"
  add_foreign_key "client_creatives", "clients"
  add_foreign_key "client_deal_scores", "clients"
  add_foreign_key "client_details", "clients"
  add_foreign_key "client_direct_partners", "clients"
  add_foreign_key "client_direct_partners", "partners"
  add_foreign_key "client_documents", "clients"
  add_foreign_key "client_events", "clients"
  add_foreign_key "client_fraud_platforms", "clients"
  add_foreign_key "client_fraud_platforms", "fraud_platforms"
  add_foreign_key "client_interests", "clients"
  add_foreign_key "client_report_files", "client_reports"
  add_foreign_key "client_reports", "clients"
  add_foreign_key "client_social_media_links", "clients"
  add_foreign_key "client_track_parties", "clients"
  add_foreign_key "client_track_parties", "track_parties"
  add_foreign_key "conversation_contexts", "conversations"
  add_foreign_key "conversations", "conversations", column: "parent_conversation_id"
  add_foreign_key "conversations", "users"
  add_foreign_key "conversion_funnels", "clients"
  add_foreign_key "demographic_segments", "clients"
  add_foreign_key "email_messages", "email_threads"
  add_foreign_key "event_aggregates", "click_urls"
  add_foreign_key "event_mappings", "clients"
  add_foreign_key "fireflies_utterances", "fireflies_meetings"
  add_foreign_key "github_credentials", "users"
  add_foreign_key "gmail_credentials", "users"
  add_foreign_key "gong_calls", "clients"
  add_foreign_key "gong_calls", "partners"
  add_foreign_key "gong_transcripts", "gong_calls"
  add_foreign_key "messages", "conversations"
  add_foreign_key "mobile_app_categorizations", "mobile_app_categories"
  add_foreign_key "mobile_app_categorizations", "mobile_apps"
  add_foreign_key "mobile_app_metrics", "mobile_apps"
  add_foreign_key "mobile_apps", "clients"
  add_foreign_key "monthly_campaign_spend_adjustments", "campaigns"
  add_foreign_key "monthly_campaign_spend_adjustments", "clients"
  add_foreign_key "monthly_campaign_spend_adjustments", "partners"
  add_foreign_key "partner_campaign_mappings", "click_urls"
  add_foreign_key "partner_categorizations", "partner_categories"
  add_foreign_key "partner_categorizations", "partners"
  add_foreign_key "partner_details", "partners"
  add_foreign_key "partner_documents", "partners"
  add_foreign_key "partner_integrations", "clients"
  add_foreign_key "partner_integrations", "partners"
  add_foreign_key "partner_media", "partners"
  add_foreign_key "partner_pricings", "partners"
  add_foreign_key "partner_quotes", "partners"
  add_foreign_key "partner_resources", "partners"
  add_foreign_key "partner_social_media_links", "partners"
  add_foreign_key "partner_spends", "click_urls"
  add_foreign_key "partner_spends", "partners"
  add_foreign_key "partner_use_cases", "partners"
  add_foreign_key "partners", "partner_categories", column: "main_category_id"
  add_foreign_key "proxy_request_logs", "api_playgrounds"
  add_foreign_key "sessions", "users"
  add_foreign_key "slack_messages", "users"
  add_foreign_key "solid_queue_blocked_executions", "solid_queue_jobs", column: "job_id", on_delete: :cascade
  add_foreign_key "solid_queue_claimed_executions", "solid_queue_jobs", column: "job_id", on_delete: :cascade
  add_foreign_key "solid_queue_failed_executions", "solid_queue_jobs", column: "job_id", on_delete: :cascade
  add_foreign_key "solid_queue_ready_executions", "solid_queue_jobs", column: "job_id", on_delete: :cascade
  add_foreign_key "solid_queue_recurring_executions", "solid_queue_jobs", column: "job_id", on_delete: :cascade
  add_foreign_key "solid_queue_scheduled_executions", "solid_queue_jobs", column: "job_id", on_delete: :cascade
  add_foreign_key "sub_client_budget_campaigns", "campaigns"
  add_foreign_key "sub_client_budget_campaigns", "sub_client_budgets"
  add_foreign_key "sub_client_budgets", "client_budgets"
  add_foreign_key "target_kpis", "clients"
  add_foreign_key "task_runs", "task_definitions"
  add_foreign_key "track_party_campaign_mappings", "click_urls"
  add_foreign_key "track_party_integrations", "clients"
  add_foreign_key "track_party_integrations", "track_parties"
  add_foreign_key "workspaces", "users"

  create_view "campaign_spend_summaries", materialized: true, sql_definition: <<-SQL
      WITH adjustments AS (
           SELECT campaign_spend_adjustments.campaign_spend_id,
              sum(campaign_spend_adjustments.amount) AS total_adjustment,
              json_agg(json_build_object('amount', campaign_spend_adjustments.amount, 'reason', campaign_spend_adjustments.reason, 'metadata', campaign_spend_adjustments.metadata)) AS adjustment_details
             FROM campaign_spend_adjustments
            GROUP BY campaign_spend_adjustments.campaign_spend_id
          ), event_counts AS (
           SELECT ranked_events.click_url_id,
              ranked_events.date,
              max(
                  CASE
                      WHEN ((ranked_events.event)::text = 'impression'::text) THEN ranked_events.count
                      ELSE NULL::bigint
                  END) AS impression_count,
              max(
                  CASE
                      WHEN ((ranked_events.event)::text = 'click'::text) THEN ranked_events.count
                      ELSE NULL::bigint
                  END) AS click_count,
              max(
                  CASE
                      WHEN ((ranked_events.event)::text = 'install'::text) THEN ranked_events.count
                      ELSE NULL::bigint
                  END) AS install_count
             FROM ( SELECT DISTINCT ON (event_aggregates.click_url_id, event_aggregates.date, event_aggregates.event) event_aggregates.click_url_id,
                      event_aggregates.date,
                      event_aggregates.event,
                      event_aggregates.count,
                          CASE
                              WHEN ((event_aggregates.data_origin)::text = 'feedmob_pg_conversion'::text) THEN 1
                              WHEN ((event_aggregates.data_origin)::text = ANY ((ARRAY['imported_singular'::character varying, 'imported_appsflyer'::character varying, 'imported_adjust'::character varying])::text[])) THEN 2
                              WHEN ((event_aggregates.data_origin)::text = 'feedmob_pg_agency_conversion'::text) THEN 3
                              WHEN ((event_aggregates.data_origin)::text = 'feedmob_redshift'::text) THEN 4
                              ELSE 5
                          END AS priority
                     FROM event_aggregates
                    WHERE ((event_aggregates.event)::text = ANY ((ARRAY['impression'::character varying, 'click'::character varying, 'install'::character varying])::text[]))
                    ORDER BY event_aggregates.click_url_id, event_aggregates.date, event_aggregates.event,
                          CASE
                              WHEN ((event_aggregates.data_origin)::text = 'feedmob_pg_conversion'::text) THEN 1
                              WHEN ((event_aggregates.data_origin)::text = ANY ((ARRAY['imported_singular'::character varying, 'imported_appsflyer'::character varying, 'imported_adjust'::character varying])::text[])) THEN 2
                              WHEN ((event_aggregates.data_origin)::text = 'feedmob_pg_agency_conversion'::text) THEN 3
                              WHEN ((event_aggregates.data_origin)::text = 'feedmob_redshift'::text) THEN 4
                              ELSE 5
                          END) ranked_events
            GROUP BY ranked_events.click_url_id, ranked_events.date
          ), all_spends AS (
           SELECT cs_client.date AS spend_date,
              clients.legacy_id AS legacy_client_id,
              click_urls.legacy_id AS legacy_click_url_id,
              campaigns.legacy_id AS legacy_campaign_id,
              partners.legacy_id AS legacy_partner_id,
              cs_client.spend AS gross_spend,
              cs_partner.spend AS net_spend,
              COALESCE(partner_adj.total_adjustment, NULL::numeric) AS net_spend_adjustment,
              (cs_partner.spend + COALESCE(partner_adj.total_adjustment, (0)::numeric)) AS adjusted_net_spend,
                  CASE
                      WHEN ((cs_client.spend = (0)::numeric) OR (cs_client.spend IS NULL)) THEN NULL::numeric
                      ELSE round((((cs_client.spend - COALESCE(cs_partner.spend, (0)::numeric)) / cs_client.spend) * (100)::numeric), 2)
                  END AS margin,
                  CASE
                      WHEN ((cs_client.spend = (0)::numeric) OR (cs_client.spend IS NULL)) THEN NULL::numeric
                      ELSE round((((cs_client.spend - (cs_partner.spend + COALESCE(partner_adj.total_adjustment, (0)::numeric))) / cs_client.spend) * (100)::numeric), 2)
                  END AS adjusted_margin,
              cs_client.id AS gross_campaign_spend_id,
              cs_partner.id AS net_campaign_spend_id,
              partner_adj.adjustment_details AS net_spend_adjustments,
              (COALESCE(clients.is_test, false) OR COALESCE(partners.is_test, false)) AS is_test,
              concat(cs_client.calculation_source, '/', COALESCE((cs_client.calculation_metadata ->> 'data_origin'::text), (cs_client.calculation_metadata ->> 'source'::text), 'unknown'::text)) AS gross_spend_source,
              concat(COALESCE(cs_partner.calculation_source, 'N/A'::character varying), '/', COALESCE((cs_partner.calculation_metadata ->> 'data_origin'::text), (cs_partner.calculation_metadata ->> 'source'::text), 'unknown'::text)) AS net_spend_source,
              ec.impression_count,
              ec.click_count,
              ec.install_count,
                  CASE
                      WHEN (COALESCE(ec.click_count, (0)::bigint) = 0) THEN NULL::numeric
                      ELSE round((((ec.install_count)::numeric / (ec.click_count)::numeric) * (100)::numeric), 2)
                  END AS cvr,
              cs_client.click_url_id
             FROM (((((((campaign_spends cs_client
               LEFT JOIN campaign_spends cs_partner ON (((cs_client.date = cs_partner.date) AND (cs_client.click_url_id = cs_partner.click_url_id) AND ((cs_partner.spend_type)::text = 'partner'::text))))
               LEFT JOIN adjustments partner_adj ON ((cs_partner.id = partner_adj.campaign_spend_id)))
               LEFT JOIN event_counts ec ON (((ec.click_url_id = cs_client.click_url_id) AND (ec.date = cs_client.date))))
               JOIN click_urls ON ((cs_client.click_url_id = click_urls.id)))
               JOIN campaigns ON ((click_urls.campaign_id = campaigns.id)))
               JOIN clients ON ((campaigns.client_id = clients.id)))
               JOIN partners ON ((click_urls.partner_id = partners.id)))
            WHERE (((cs_client.spend_type)::text = 'client'::text) AND (NOT ((COALESCE(cs_client.spend, (0)::numeric) = (0)::numeric) AND (COALESCE((cs_partner.spend + COALESCE(partner_adj.total_adjustment, (0)::numeric)), (0)::numeric) = (0)::numeric))))
          UNION ALL
           SELECT cs_partner.date AS spend_date,
              clients.legacy_id AS legacy_client_id,
              click_urls.legacy_id AS legacy_click_url_id,
              campaigns.legacy_id AS legacy_campaign_id,
              partners.legacy_id AS legacy_partner_id,
              NULL::numeric AS gross_spend,
              cs_partner.spend AS net_spend,
              COALESCE(partner_adj.total_adjustment, (0)::numeric) AS net_spend_adjustment,
              (cs_partner.spend + COALESCE(partner_adj.total_adjustment, (0)::numeric)) AS adjusted_net_spend,
              NULL::numeric AS margin,
              NULL::numeric AS adjusted_margin,
              NULL::bigint AS gross_campaign_spend_id,
              cs_partner.id AS net_campaign_spend_id,
              partner_adj.adjustment_details AS net_spend_adjustments,
              (COALESCE(clients.is_test, false) OR COALESCE(partners.is_test, false)) AS is_test,
              NULL::text AS gross_spend_source,
              concat(cs_partner.calculation_source, '/', COALESCE((cs_partner.calculation_metadata ->> 'data_origin'::text), (cs_partner.calculation_metadata ->> 'source'::text), 'unknown'::text)) AS net_spend_source,
              ec.impression_count,
              ec.click_count,
              ec.install_count,
                  CASE
                      WHEN (COALESCE(ec.click_count, (0)::bigint) = 0) THEN NULL::numeric
                      ELSE round((((ec.install_count)::numeric / (ec.click_count)::numeric) * (100)::numeric), 2)
                  END AS cvr,
              cs_partner.click_url_id
             FROM ((((((campaign_spends cs_partner
               LEFT JOIN adjustments partner_adj ON ((cs_partner.id = partner_adj.campaign_spend_id)))
               LEFT JOIN event_counts ec ON (((ec.click_url_id = cs_partner.click_url_id) AND (ec.date = cs_partner.date))))
               JOIN click_urls ON ((cs_partner.click_url_id = click_urls.id)))
               JOIN campaigns ON ((click_urls.campaign_id = campaigns.id)))
               JOIN clients ON ((campaigns.client_id = clients.id)))
               JOIN partners ON ((click_urls.partner_id = partners.id)))
            WHERE (((cs_partner.spend_type)::text = 'partner'::text) AND ((cs_partner.spend + COALESCE(partner_adj.total_adjustment, (0)::numeric)) <> (0)::numeric) AND (NOT (EXISTS ( SELECT 1
                     FROM campaign_spends cs_client
                    WHERE ((cs_client.date = cs_partner.date) AND (cs_client.click_url_id = cs_partner.click_url_id) AND ((cs_client.spend_type)::text = 'client'::text))))))
          )
   SELECT spend_date,
      legacy_client_id,
      legacy_click_url_id,
      legacy_campaign_id,
      legacy_partner_id,
      round(gross_spend, 2) AS gross_spend,
      round((net_spend)::numeric, 2) AS net_spend,
      round(net_spend_adjustment, 2) AS net_spend_adjustment,
      round(adjusted_net_spend, 2) AS adjusted_net_spend,
      margin,
      adjusted_margin,
      round((COALESCE(gross_spend, (0)::numeric) - COALESCE(adjusted_net_spend, (0)::numeric)), 2) AS revenue,
      gross_campaign_spend_id,
      net_campaign_spend_id,
      net_spend_adjustments,
      is_test,
      gross_spend_source,
      net_spend_source,
      impression_count,
      click_count,
      install_count,
      cvr
     FROM all_spends
    WHERE ((COALESCE(gross_spend, (0)::numeric) <> (0)::numeric) OR (COALESCE(adjusted_net_spend, (0)::numeric) <> (0)::numeric));
  SQL
  add_index "campaign_spend_summaries", ["is_test", "spend_date"], name: "idx_css_test_date", where: "(is_test = false)"
  add_index "campaign_spend_summaries", ["spend_date", "gross_spend", "net_spend", "adjusted_net_spend"], name: "idx_css_metrics", where: "((gross_spend IS NOT NULL) OR (net_spend IS NOT NULL) OR (adjusted_net_spend IS NOT NULL))"
  add_index "campaign_spend_summaries", ["spend_date", "legacy_campaign_id", "gross_spend"], name: "idx_css_date_campaign_spend"
  add_index "campaign_spend_summaries", ["spend_date", "legacy_click_url_id"], name: "campaign_spend_summaries_unique_idx", unique: true
  add_index "campaign_spend_summaries", ["spend_date", "legacy_client_id", "legacy_campaign_id", "gross_spend", "adjusted_net_spend", "impression_count", "click_count", "install_count"], name: "idx_css_reporting", order: { spend_date: :desc }
  add_index "campaign_spend_summaries", ["spend_date", "legacy_client_id", "legacy_partner_id"], name: "idx_css_date_client_partner"

  create_view "campaign_monthly_spend_summaries", materialized: true, sql_definition: <<-SQL
      WITH monthly_spends AS (
           SELECT (date_trunc('month'::text, (css.spend_date)::timestamp with time zone))::date AS month_date,
              css.legacy_client_id,
              css.legacy_campaign_id,
              css.legacy_partner_id,
              css.is_test,
              COALESCE(sum(css.gross_spend), (0)::numeric) AS gross_spend,
              COALESCE(sum(css.net_spend), (0)::numeric) AS net_spend,
              COALESCE(sum(css.net_spend_adjustment), NULL::numeric) AS net_spend_adjustment,
              COALESCE(sum(css.adjusted_net_spend), (0)::numeric) AS adjusted_net_spend,
              COALESCE(sum(css.revenue), (0)::numeric) AS revenue,
              NULLIF(sum(css.impression_count), (0)::numeric) AS impression_count,
              NULLIF(sum(css.click_count), (0)::numeric) AS click_count,
              NULLIF(sum(css.install_count), (0)::numeric) AS install_count,
                  CASE
                      WHEN (COALESCE(sum(css.click_count), (0)::numeric) = (0)::numeric) THEN NULL::numeric
                      ELSE round(((sum(css.install_count) / sum(css.click_count)) * (100)::numeric), 2)
                  END AS cvr
             FROM campaign_spend_summaries css
            GROUP BY (date_trunc('month'::text, (css.spend_date)::timestamp with time zone)), css.legacy_client_id, css.legacy_campaign_id, css.legacy_partner_id, css.is_test
          ), aggregated_adjustments AS (
           SELECT monthly_campaign_spend_adjustments.legacy_campaign_id,
              monthly_campaign_spend_adjustments.legacy_client_id,
              monthly_campaign_spend_adjustments.legacy_partner_id,
              monthly_campaign_spend_adjustments.month,
              sum(COALESCE(monthly_campaign_spend_adjustments.gross_amount, (0)::numeric)) AS gross_amount,
              sum(COALESCE(monthly_campaign_spend_adjustments.net_amount, (0)::numeric)) AS net_amount,
              string_agg(DISTINCT (monthly_campaign_spend_adjustments.reason)::text, '; '::text) AS reason,
              min(monthly_campaign_spend_adjustments.id) AS id
             FROM monthly_campaign_spend_adjustments
            GROUP BY monthly_campaign_spend_adjustments.legacy_campaign_id, monthly_campaign_spend_adjustments.legacy_client_id, monthly_campaign_spend_adjustments.legacy_partner_id, monthly_campaign_spend_adjustments.month
          )
   SELECT ms.month_date,
      ms.legacy_client_id,
      ms.legacy_campaign_id,
      ms.legacy_partner_id,
      ms.gross_spend,
      ms.net_spend,
      ms.net_spend_adjustment,
      COALESCE(mcsa.gross_amount, NULL::numeric) AS monthly_gross_adjustment,
      COALESCE(mcsa.net_amount, NULL::numeric) AS monthly_net_adjustment,
      round((ms.gross_spend + COALESCE(mcsa.gross_amount, (0)::numeric)), 2) AS adjusted_gross_spend,
      round((ms.adjusted_net_spend + COALESCE(mcsa.net_amount, (0)::numeric)), 2) AS adjusted_net_spend,
          CASE
              WHEN ((ms.gross_spend + COALESCE(mcsa.gross_amount, (0)::numeric)) = (0)::numeric) THEN NULL::numeric
              ELSE round(((((ms.gross_spend + COALESCE(mcsa.gross_amount, (0)::numeric)) - (ms.adjusted_net_spend + COALESCE(mcsa.net_amount, (0)::numeric))) / (ms.gross_spend + COALESCE(mcsa.gross_amount, (0)::numeric))) * (100)::numeric), 2)
          END AS adjusted_margin,
      round(((ms.revenue + COALESCE(mcsa.gross_amount, (0)::numeric)) - COALESCE(mcsa.net_amount, (0)::numeric)), 2) AS adjusted_revenue,
      ms.is_test,
      COALESCE(mcsa.reason, NULL::text) AS adjustment_reason,
      mcsa.id AS monthly_campaign_spend_adjustment_id,
      ms.impression_count,
      ms.click_count,
      ms.install_count,
      ms.cvr
     FROM (monthly_spends ms
       LEFT JOIN aggregated_adjustments mcsa ON (((mcsa.legacy_campaign_id = ms.legacy_campaign_id) AND (mcsa.legacy_client_id = ms.legacy_client_id) AND (mcsa.legacy_partner_id = ms.legacy_partner_id) AND (to_char((ms.month_date)::timestamp with time zone, 'YYYY-MM'::text) = (mcsa.month)::text) AND (ms.is_test = false))))
    WHERE ((ms.gross_spend <> (0)::numeric) OR (ms.net_spend <> (0)::numeric) OR (ms.adjusted_net_spend <> (0)::numeric) OR (COALESCE(mcsa.gross_amount, (0)::numeric) <> (0)::numeric) OR (COALESCE(mcsa.net_amount, (0)::numeric) <> (0)::numeric));
  SQL
  add_index "campaign_monthly_spend_summaries", ["legacy_client_id", "legacy_partner_id", "month_date"], name: "idx_cms_client_partner", order: { month_date: :desc }, where: "(is_test = false)"
  add_index "campaign_monthly_spend_summaries", ["month_date", "gross_spend", "adjusted_gross_spend", "adjusted_net_spend"], name: "idx_cms_date_metrics", order: { month_date: :desc }, where: "(is_test = false)"
  add_index "campaign_monthly_spend_summaries", ["month_date", "impression_count", "click_count", "install_count", "cvr"], name: "idx_cms_performance", where: "(is_test = false)"
  add_index "campaign_monthly_spend_summaries", ["month_date", "legacy_campaign_id", "legacy_client_id", "legacy_partner_id"], name: "campaign_monthly_spend_summaries_unique_idx", unique: true

end
