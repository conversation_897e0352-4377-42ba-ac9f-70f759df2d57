# Pin npm packages by running ./bin/importmap

pin "application"
pin "@hotwired/turbo-rails", to: "turbo.min.js"
pin "@hotwired/stimulus", to: "@hotwired--stimulus.js" # @3.2.2
pin "@hotwired/stimulus-loading", to: "stimulus-loading.js"
pin_all_from "app/javascript/controllers", under: "controllers"
pin "tom-select" # @2.3.1
pin "stimulus-use" # @0.52.2
pin "@stimulus-components/auto-submit", to: "@stimulus-components--auto-submit.js" # @6.0.0
pin "chartkick", to: "chartkick.js"
pin "Chart.bundle", to: "Chart.bundle.js"
pin "@stimulus-components/password-visibility", to: "@stimulus-components--password-visibility.js" # @3.0.0
pin "@tailwindcss/typography", to: "@tailwindcss--typography.js" # @0.5.15
pin "cssesc" # @3.0.0
pin "lodash.castarray" # @4.4.0
pin "lodash.isplainobject" # @4.0.6
pin "lodash.merge" # @4.6.2
pin "picocolors" # @1.1.1
pin "postcss-selector-parser" # @6.0.10
pin "tailwindcss/colors", to: "tailwindcss--colors.js" # @3.4.14
pin "tailwindcss/plugin", to: "tailwindcss--plugin.js" # @3.4.14
pin "util-deprecate" # @1.0.2
pin "marked" # @15.0.3
pin "marked-highlight" # @2.2.1
pin "@finos/perspective-viewer", to: "https://cdn.jsdelivr.net/npm/@finos/perspective-viewer@3.6.1/dist/cdn/perspective-viewer.js"
pin "@finos/perspective-viewer-datagrid", to: "https://cdn.jsdelivr.net/npm/@finos/perspective-viewer-datagrid@3.6.1/dist/cdn/perspective-viewer-datagrid.js"
pin "@finos/perspective-viewer-d3fc", to: "https://cdn.jsdelivr.net/npm/@finos/perspective-viewer-d3fc@3.6.1/dist/cdn/perspective-viewer-d3fc.js"
pin "@finos/perspective", to: "https://cdn.jsdelivr.net/npm/@finos/perspective@3.6.1/dist/cdn/perspective.js"
pin "@finos/perspective-workspace", to: "https://cdn.jsdelivr.net/npm/@finos/perspective-workspace@3.6.1/dist/cdn/perspective-workspace.js"
pin "ahoy", to: "ahoy.js"
pin "papaparse" # @5.5.2
pin "@ruby/wasm-wasi", to: "https://cdn.jsdelivr.net/npm/@ruby/wasm-wasi@2.7.1/dist/browser/+esm"
pin "ace", to: "https://cdn.jsdelivr.net/npm/ace-builds@1.39.1/src/ace.js"
pin "ace/mode-ruby", to: "https://cdn.jsdelivr.net/npm/ace-builds@1.39.1/src/mode-ruby.js"
pin "ace/theme-monokai", to: "https://cdn.jsdelivr.net/npm/ace-builds@1.39.1/src/theme-monokai.js"
