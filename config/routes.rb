Rails.application.routes.default_url_options[:host] = if Rails.env.production?
  "https://assistant.feedmob.ai"
else
  # "https://leading-mongoose-gently.ngrok-free.app"
  "http://localhost:3000"
end

Rails.application.routes.draw do
  # Defines the root path route ("/")
  root "pages#home"

  # Health and system routes
  get "up" => "rails/health#show", as: :rails_health_check
  get "service-worker" => "rails/pwa#service_worker", as: :pwa_service_worker
  get "manifest" => "rails/pwa#manifest", as: :pwa_manifest

  # Static pages
  get "analytics", to: "analytics#index"
  get "whats-new", to: "pages#whats_new", as: :whats_new
  get "new-home", to: "pages#home", as: :new_home

  # Route concerns
  concern :legacyable do
    get "legacy", on: :member
  end

  concern :searchable do
    get "search", on: :collection
  end

  concern :trendable do
    get "trend", on: :collection
  end

  # API routes
  namespace :api do
    namespace :unstable do
      resources :providers, only: [ :index ]
      # Reports endpoints moved outside the partners collection
      scope "partners/reports", controller: "reports" do
        get :fetch_direct
        post :request_async
        get :fetch_async
      end

      post "samsung/generate_jwt", to: "samsung#generate_jwt"
      get "entities/search", to: "entities#search"
      get "mcp/campaign_spends", to: "mcp#campaign_spends"
    end
  end

  # Authentication and user management
  resource :session do
    get "google_login", on: :collection
  end
  resources :passwords, param: :token
  resources :users

  # Chat and conversation routes
  resources :conversations, exclude: [ :index ] do
    resources :messages, only: [ :create ]
    resources :conversation_contexts, only: [ :create, :destroy ]
    get "context", on: :member
  end
  resources :messages, only: [] do
    member do
      post "confirm_tool_use"
    end
  end

  # Documents and entries
  resources :documents, only: [ :index, :show, :destroy ] do
    get "export", on: :collection
  end
  resources :entries, only: [], concerns: [ :searchable ]
  get "email/:id/preview", to: "entries#show", as: :email_preview

  # Third-party integrations
  post "proxy", to: "proxy#fetch"
  resources :connections, only: [ :index ]

  # GitHub integrations
  get "github/connect"
  get "github/callback"
  delete "github/destroy"

  # Slack integrations
  post "/slack/events", to: "slack#events"
  post "/slack/shortcuts", to: "slack_shortcuts#create"
  resources :slack_messages, only: [ :index, :destroy ]

  # Business domain resources (alphabetically sorted)
  resources :alerts, only: [] do
    member do
      post :dismiss
    end
  end

  resources :api_playgrounds do
    member do
      post :save_code
    end
  end

  resources :audit_logs, only: [ :index, :show ]

  resources :campaigns, concerns: [ :legacyable, :searchable ]

  resources :campaign_spends, only: [ :index ] do
    collection do
      get "grid_data"
      get "export"
      get "export_aggregated"
      get "stats"
      get "trend"
      post "refresh"
    end
  end

  resources :client_reports, only: [ :show ]

  resources :clients, concerns: [ :legacyable, :searchable ] do
    member do
      post :sync_from_website
      get :budget_data
    end
  end

  resources :sub_client_budgets, only: [ :show ]

  resources :legacy_pricings, only: [ :index ] do
    put "batch_update", on: :collection
  end

  resources :mobile_apps, concerns: [ :legacyable, :searchable ]

  resources :monthly_campaign_spends, only: [ :index ] do
    collection do
      get "stats"
      get "trend"
    end
  end

  resources :partner_integrations

  resources :partners, concerns: [ :legacyable, :searchable ] do
    member do
      post :sync_from_website
    end
  end

  resources :tasks, only: [ :index ]

  resources :track_parties, concerns: [ :legacyable ]

  resources :track_party_integrations

  resources :workspaces, only: [ :new, :create, :show, :edit, :update ] do
    member do
      post "generate"
      post "toggle_favorite"
    end
    collection do
      get "share"
      post "perform_share"
    end
  end

  # Engine mounts
  mount MissionControl::Jobs::Engine, at: "/jobs"

  if Rails.env.development?
    mount Flipper::UI.app(Flipper) => "/flipper"
  else
    flipper_app = Flipper::UI.app do |builder|
      builder.use Rack::Auth::Basic do |username, password|
        ActiveSupport::SecurityUtils.secure_compare(username, Rails.application.credentials.flipper[:username]) &&
          ActiveSupport::SecurityUtils.secure_compare(password, Rails.application.credentials.flipper[:password])
      end
    end
    mount flipper_app, at: "/flipper"
  end
end
